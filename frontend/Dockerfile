FROM public.ecr.aws/zomato/node:20.6.1-alpine as build-stage

WORKDIR /app

# Copy only package files first
COPY frontend/package*.json ./

# Install dependencies
RUN npm install

# Copy the rest of the frontend application
COPY frontend/ ./

# Build the application
RUN npm run build

# Production stage
FROM public.ecr.aws/zomato/node:20.6.1-alpine

WORKDIR /app

# Install serve
RUN npm install -g serve

# Copy built static files from build stage
COPY --from=build-stage /app/dist .

EXPOSE 3000

CMD ["serve", "-s", ".", "-l", "3000"]
