{"name": "capacity-planner", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build --mode production", "lint": "eslint .", "preview": "vite preview", "comment": "build to be : tsc -b && vite build"}, "dependencies": {"@ant-design/icons": "^5.6.1", "@tailwindcss/vite": "^4.1.1", "@tanstack/react-query": "^5.72.0", "antd": "^5.24.6", "axios": "^1.8.4", "chart.js": "^4.4.8", "dayjs": "^1.11.13", "lodash.isempty": "^4.4.0", "lodash.startcase": "^4.4.0", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-router-dom": "^6.30.0", "tailwindcss": "^4.1.1", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/lodash.isempty": "^4.4.9", "@types/lodash.startcase": "^4.4.9", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}