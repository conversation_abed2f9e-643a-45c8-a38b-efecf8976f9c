import { createElement } from "react";
import { HistoryOutlined, ScheduleOutlined } from "@ant-design/icons";
import useGenericFiltersStore from "../stores/genericFiltersStore";
import startCase from "lodash.startcase";
import useUserStore from "../stores/userStore";
import { appRoutes } from "../routes";

export interface CategoryInterface {
  id: number;
  name: string;
}

export const getNavBarItems = () => {
  const { selectedTenant, selectedEntity } = useUserStore.getState();
  const { filtersData } = useGenericFiltersStore.getState();
  const { categoriesFilterData } = filtersData || {};
  const { categories } = categoriesFilterData || {};

  const basePath = `/${selectedTenant}/${selectedEntity}`;

  // * hiding overall for now
  // return [
  //   {
  //     key: appRoutes.historicalPlayground,
  //     icon: createElement(HistoryOutlined),
  //     label: "Historical Playground",
  //     children: [
  //       {
  //         key: appRoutes.historicalPlayground,
  //         label: "Overall",
  //       },
  //       ...(categories?.map((category: CategoryInterface) => ({
  //         key: appRoutes.historicalPlayground + "/" + category.name,
  //         label: startCase(category.name),
  //       })) || []),
  //     ],
  //   },
  //   {
  //     key: appRoutes.planner,
  //     icon: createElement(ScheduleOutlined),
  //     label: "Planner",
  //     children: [
  //       {
  //         key: appRoutes.planner,
  //         label: "Overall",
  //       },
  //       ...(categories?.map((category: CategoryInterface) => ({
  //         key: appRoutes.planner + "/" + category.name,
  //         label: startCase(category.name),
  //       })) || []),
  //     ],
  //   },
  // ];

  return [
    {
      key: basePath + appRoutes.historicalPlayground,
      icon: createElement(HistoryOutlined),
      label: "Historical Playground",
      children: [
        {
          key: basePath + appRoutes.historicalPlayground,
          label: "Overall",
        },
        ...(categories?.map((category: CategoryInterface) => ({
          key: basePath + appRoutes.historicalPlayground + "/" + category.id,
          label: startCase(category.name),
        })) || []),
      ],
    },
    {
      key: basePath + appRoutes.planner,
      icon: createElement(ScheduleOutlined),
      label: "Planner",
      children: categories?.map((category: CategoryInterface) => ({
        key: basePath + appRoutes.planner + "/" + category.id,
        label: startCase(category.name),
      })),
    },
  ];
};
