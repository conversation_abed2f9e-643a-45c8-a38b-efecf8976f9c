import { TimeGrain } from "../stores/plannerStore";

const env = import.meta.env;

export const TIME_PERIOD_OPTIONS = [
  "Do1D",
  "Wo1W",
  "Wo2W",
  "Mo1M",
  "Mo2M",
  "Yo1Y",
];

export const STORE_HEALTH_CLASSES: Record<string, Record<string, string>> = {
  good: {
    bg: "bg-green-100/90",
    btnBg: "bg-green-100 text-green-800 border border-green-300",
  },
  okay: {
    bg: "bg-amber-100/90",
    btnBg: "bg-amber-100 text-amber-800 border border-amber-300",
  },
  critical: {
    bg: "bg-rose-100/90",
    btnBg: "bg-rose-100 text-rose-800 border border-rose-300",
  },
};

export const CITY_STORE_CONSTANTS = {
  panIndia: { label: "Pan India", value: "pan-india" },
  overall: { label: "Overall", value: "overall" },
};

export const COLOR_PALETTE = [
  "67, 56, 202", // Indigo
  "239, 68, 68", // Red
  "34, 197, 94", // Green
  "234, 179, 8", // Yellow
  "14, 165, 233", // Blue
  "168, 85, 247", // Purple
  "236, 72, 153", // Pink
  "249, 115, 22", // Orange
  "16, 185, 129", // Emerald
  "59, 130, 246", // Light Blue
];

export const TIME_GRAIN_OPTIONS: Record<string, TimeGrain> = {
  weekly: "weekly",
  daily: "daily",
  hourly: "hourly",
};

export const AGGREGATION_GRAIN: Record<string, string> = {
  ENTITY: "entity",
  CITY: "city",
  PAN_INDIA: "pan_india",
};

export const FORECASTS: Record<string, string> = {
  2: "Instore Forecasts",
  3: "Demo Forecast 2",
};

export const PARAM_ARRAY_KEYS = ["metric", "comparison", "categories"]; //? keys/filters parsed as array in url

env["VITE_APPROVAL_EMAIL_IDS"] = '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>'
export const APPROVAL_EMAIL_IDS = env.VITE_APPROVAL_EMAIL_IDS?.split(",");
