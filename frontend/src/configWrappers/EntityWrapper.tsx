import { Card, Spin } from "antd";
import useUserStore from "../stores/userStore";
import { useQuery } from "@tanstack/react-query";
import { getEntities } from "../apis/entity";
import validateResponse from "../utils/validateResponse";
import { RightOutlined } from "@ant-design/icons";

//* here, selectedEntity = storeops
function EntityWrapper({ children }: { children: React.ReactNode }) {
  const { selectedEntity, setSelectedEntity, setEntities } = useUserStore(
    (state) => state
  );

  const { data, isLoading, isError } = useQuery({
    queryKey: ["getTenants"],
    queryFn: async () => {
      const response = await getEntities();
      const result = validateResponse(response);
      const entities = result?.data?.data;
      setEntities(entities);
      return result;
    },
  });

  if (isLoading) {
    return (
      <div className="h-screen w-screen flex justify-center items-center">
        <Spin size="large" />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="h-screen w-screen flex justify-center items-center">
        Some error occurred. Please retry in some time.
      </div>
    );
  }

  const entities = data?.data?.data;

  const handleEntityChange = (entity: string) => {
    setSelectedEntity(entity);
  };

  if (!selectedEntity) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-blue-100 via-indigo-100 to-pink-100 p-12">
        <h2 className="text-4xl font-bold text-blue-800 !mb-3 animate-fade-in">
          Select Entity
        </h2>
        <p className="text-indigo-600 !mb-10 text-lg">
          Choose an entity to continue your journey
        </p>
        <div className="grid grid-cols-2 gap-8">
          {entities?.map((entity: string) => (
            <Card
              key={entity}
              onClick={() => handleEntityChange(entity)}
              className="w-80 hover:shadow-2xl transition-all duration-500 cursor-pointer transform hover:-translate-y-3 hover:rotate-1 border border-white/30 rounded-xl overflow-hidden"
            >
              <div className="flex items-center justify-between group">
                <div className="text-xl font-semibold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent capitalize">
                  {entity}
                </div>

                <RightOutlined className="!text-indigo-500 transform group-hover:translate-x-2 transition-transform duration-300" />
              </div>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return <>{children}</>;
}

export default EntityWrapper;
