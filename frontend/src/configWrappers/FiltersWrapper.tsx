import { message, Spin } from "antd";
import { useQueries } from "@tanstack/react-query";
import { useFetchAllFilters } from "../hooks/useFetchAllFilters";
import useGenericFiltersStore from "../stores/genericFiltersStore";
import useUserStore from "../stores/userStore";
import { useEffect } from "react";
import useConfigStore from "../stores/configStore";
import isEmpty from "lodash.isempty";
import useHistoricalPlaygroundStore, {
  INITIAL_PARAMS as HISTORICAL_INITIAL_PARAMS,
} from "../stores/historicalPlaygroundStore";
import usePlannerStore, {
  INITIAL_PARAMS as PLANNER_INITIAL_PARAMS,
} from "../stores/plannerStore";

function FiltersWrapper({ children }: { children: React.ReactNode }) {
  const {
    selectedTenant,
    selectedEntity,
    setSelectedTenant,
    setSelectedEntity,
  } = useUserStore((state) => state);
  const { config, setConfig } = useConfigStore((state) => state);
  const { filtersData, setFilterData } = useGenericFiltersStore(
    (state) => state
  );
  const { setParams: setHistoricalPlaygroundParams } =
    useHistoricalPlaygroundStore((state) => state);
  const { setParams: setPlannerParams } = usePlannerStore((state) => state);

  const queries = useFetchAllFilters({
    fetchFilters: config?.fetchFilters,
    setFilterData,
    enabled: !isEmpty(config),
  });

  const results = useQueries({ queries });
  const isLoading = results?.some((q) => q?.isLoading);
  const isError = results?.some((q) => q?.isError);

  useEffect(() => {
    const fetchConfig = async () => {
      try {
        await setConfig(
          selectedTenant,
          selectedEntity,
          setSelectedTenant,
          setSelectedEntity
        );
      } catch (e) {
        console.warn(e);
        // todo: fix this, message doesn't show
        // Move message.info inside effect to avoid React 18 concurrent mode issues
        // Defer message to next tick to ensure it's not in render
        setTimeout(() => {
          message.warning("Failed to fetch config");
        }, 0);
      }
    };
    fetchConfig();
  }, [
    setConfig,
    selectedTenant,
    selectedEntity,
    setSelectedTenant,
    setSelectedEntity,
  ]);

  //* setting initial filters for planner and playground
  useEffect(() => {
    if (!isLoading && !isError) {
      const { categoriesFilterData, metricsFilterData } = filtersData;

      const categories = categoriesFilterData?.categories || [];

      // setting for OVERALL playground
      setHistoricalPlaygroundParams("", {
        ...HISTORICAL_INITIAL_PARAMS,
        categories: [categories?.[0]?.id?.toString()],
      });

      categories?.forEach((category: { id: number; name: string }) => {
        const { id, name } = category || {};

        const categoryKey = id?.toString();

        setHistoricalPlaygroundParams(categoryKey, {
          ...HISTORICAL_INITIAL_PARAMS,
          metric: [
            metricsFilterData?.categories?.[name]?.[0]?.metric_name,
          ] as string[],
        });

        setPlannerParams(categoryKey, PLANNER_INITIAL_PARAMS);
      });
    }
  }, [
    isLoading,
    isError,
    filtersData,
    setHistoricalPlaygroundParams,
    setPlannerParams,
  ]);

  if (isLoading || !config) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Spin size="large" />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex justify-center items-center h-screen">
        Some error occurred. Please retry in some time.
      </div>
    );
  }

  return <>{children}</>;
}

export default FiltersWrapper;
