import { useQuery } from "@tanstack/react-query";
import { ReactNode } from "react";
import { getTenants } from "../apis/tenant";
import validateResponse from "../utils/validateResponse";
import { Spin } from "antd";
import useUserStore from "../stores/userStore";

function TenantWrapper({ children }: { children: ReactNode }) {
  const { setTenants, setSelectedTenant } = useUserStore((state) => state);

  const { data, isLoading, isError } = useQuery({
    queryKey: ["getTenants"],
    queryFn: async () => {
      const response = await getTenants();
      const result = validateResponse(response);
      const tenants = result?.data?.data;
      setTenants(tenants);
      // set tenant, by default 0th key
      setSelectedTenant(tenants[0]);
      return result;
    },
  });

  if (isLoading) {
    return (
      <div className="h-screen w-screen flex justify-center items-center">
        <Spin size="large" />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="h-screen w-screen flex justify-center items-center">
        Some error occurred. Please retry in some time.
      </div>
    );
  }

  return <>{children}</>;
}

export default TenantWrapper;
