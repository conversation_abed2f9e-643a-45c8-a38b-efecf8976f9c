@import "tailwindcss";

@font-face {
  font-family: "Okra";
  src: url("./assets/fonts/Okra/Okra-Regular.ttf") format("truetype");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Okra";
  src: url("./assets/fonts/Okra/Okra-Bold.ttf") format("truetype");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Okra";
  src: url("./assets/fonts/Okra/Okra-ExtraBold.ttf") format("truetype");
  font-weight: 800;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Okra";
  src: url("./assets/fonts/Okra/Okra-ExtraLight.ttf") format("truetype");
  font-weight: 200;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Okra";
  src: url("./assets/fonts/Okra/Okra-Italic.ttf") format("truetype");
  font-weight: 400;
  font-style: italic;
  font-display: swap;
}
@font-face {
  font-family: "Okra";
  src: url("./assets/fonts/Okra/Okra-Light.ttf") format("truetype");
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Okra";
  src: url("./assets/fonts/Okra/Okra-LightItalic.ttf") format("truetype");
  font-weight: 300;
  font-style: italic;
  font-display: swap;
}
@font-face {
  font-family: "Okra";
  src: url("./assets/fonts/Okra/Okra-Medium.ttf") format("truetype");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Okra";
  src: url("./assets/fonts/Okra/Okra-MediumItalic.ttf") format("truetype");
  font-weight: 500;
  font-style: italic;
  font-display: swap;
}
@font-face {
  font-family: "Okra";
  src: url("./assets/fonts/Okra/Okra-Regular.ttf") format("truetype");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Okra";
  src: url("./assets/fonts/Okra/Okra-SemiBold.ttf") format("truetype");
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Okra";
  src: url("./assets/fonts/Okra/Okra-Thin.ttf") format("truetype");
  font-weight: 100;
  font-style: normal;
  font-display: swap;
}

* {
  margin: 0;
  font-family: "Okra";
}

:root {
  font-family: "Okra", sans-serif;
  line-height: 1.5;
  font-weight: 400;
}

.ant-layout {
  background-color: #ffffff;
}

@layer base {
  h1 {
    @apply text-4xl;
    @apply font-bold;
  }
  h2 {
    @apply text-3xl;
    @apply font-bold;
  }
  h3 {
    @apply text-2xl;
    @apply font-bold;
  }
  h4 {
    @apply text-xl;
    @apply font-semibold;
  }
  h5 {
    @apply text-base; /*or text-lg */
    @apply font-semibold;
  }
  h6 {
    @apply text-sm;
    @apply font-semibold;
  }
}
