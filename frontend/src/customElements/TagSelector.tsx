// ** NEED TO REMOVE THIS...

import { CloseCircleOutlined } from "@ant-design/icons";
import { Form, FormInstance, Tag } from "antd";
import { useWatch } from "antd/es/form/Form";

interface TagSelectorInterface {
  label: string;
  options: string[];
  form: FormInstance;
}

//todo:  custom change
// {filter.options.map((item) => {
//                 const timeRange = formData?.timeRange;
//                 if (!timeRange?.length)
//                   return <TimePeriodTag key={item} item={item} form={form} />;

//                 const [start, end] = timeRange;
//                 const diffDays = end.diff(start, "days");

//                 if (
//                   (item === "Do1D" && diffDays >= 1) ||
//                   (item.startsWith("Wo1W") && diffDays >= 7) ||
//                   (item.startsWith("Wo2W") && diffDays >= 14) ||
//                   (item.startsWith("Mo1M") && diffDays >= 30)
//                 ) {
//                   return null;
//                 }

//                 return <TimePeriodTag key={item} item={item} form={form} />;
//               })}

export function TagSelector({ label, options, form }: TagSelectorInterface) {
  return (
    <div>
      {label}

      <div className="!mt-2 flex flex-wrap !gap-3">
        {options?.map((item) => (
          <Form.Item name={item} noStyle>
            <Tag
              key={item}
              bordered={false}
              className="cursor-pointer !text-sm !font-thin !p-1 !rounded-lg !tracking-wider hover:!bg-blue-50"
              color={useWatch([item], form) ? "blue" : ""}
              icon={form.getFieldValue(item) ? <CloseCircleOutlined /> : null}
              onClick={() =>
                form.setFieldsValue({ [item]: !form.getFieldValue(item) })
              }
            >
              {item}
            </Tag>
          </Form.Item>
        ))}
      </div>
    </div>
  );
}
