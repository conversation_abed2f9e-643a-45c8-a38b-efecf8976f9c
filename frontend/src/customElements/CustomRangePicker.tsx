import { DatePicker } from "antd";

interface CustomRangePickerInterface {
  format: string;
  allowClear?: boolean;
  disable?: boolean;
}
const { RangePicker } = DatePicker;

export function CustomRangePicker({
  format,
  allowClear = false,
  disable = false,
  ...rest
}: CustomRangePickerInterface) {
  return (
    <RangePicker
      {...rest}
      showTime={{
        format: "HH",
        minuteStep: 60,
        secondStep: 60,
      }}
      format={format}
      className="w-full"
      placeholder={["Start Time", "End Time"]}
      allowClear={allowClear}
      disable={disable}
    />
  );
}
