import { Select } from "antd";

interface SearchableSelectInterface {
  options: {
    label: null | string | number | boolean | Date;
    value: null | string | number | boolean | Date;
  }[];
  mode?: "multiple";
  allowClear?: boolean;
  disable?: boolean;
}

//* as we are using FC, we would have to pass form context separately
export function SearchableSelect({
  options,
  mode,
  allowClear = false,
  disable = false,
  ...rest
}: SearchableSelectInterface) {
  return (
    <Select
      {...rest}
      disabled={disable}
      options={options}
      mode={mode}
      allowClear={allowClear}
      showSearch
      className="w-full"
      filterOption={(input, option) =>
        (option?.label?.toString()?.toLowerCase() ?? "").includes(
          input.toLowerCase()
        )
      }
    />
  );
}
