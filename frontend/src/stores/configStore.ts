import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";

interface ConfigState {
  config: any; // typeof storeOpsConfig;
  setConfig: (
    tenant: string,
    entity: string,
    setTenant: (tenant: string) => void,
    setEntity: (entity: string) => void
  ) => Promise<void>;
}

export interface FetchFiltersInterface {
  [key: string]: {
    keyForStorage: string;
    optionsEndpoint: string;
  };
}

// export interface FilterInterface {
//   name: string;
//   key?: string;
//   label: string;
//   type: "searchable-select" | "custom-range-picker" | "tags-select";
//   derivedViaApiRes?: boolean;
//   keyForStorage?: string | string[];
//   labelKey?: string;
//   valueKey?: string;
//   staticOptions?: Array<any>;
//   mandatory?: boolean;
//   dependsOn?: { key: string; matchKey: string };
//   clearable?: boolean;
//   format?: string;
//   isFormedFromMultiRes?: boolean;
//   options?: string[];
//   multiple?: boolean;
// }

// const defaultConfig = storeOpsConfig;

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const configStore = (set: any): ConfigState => ({
  // tenant: defaultConfig.tenant,
  // entity: defaultConfig.entity,
  // config: defaultConfig,
  config: "",
  setConfig: async (
    tenant: string,
    entity: string,
    setTenant: (tenant: string) => void, //? doing nothing with this
    setEntity: (entity: string) => void
  ) => {
    try {
      // Dynamic import using the folder structure

      // ? not the correct way to import in vite. ref: https://github.com/rollup/plugins/tree/master/packages/dynamic-import-vars#limitations
      // const path = `../configurations/${tenant}/${department}/index.ts`;
      // const configModule = await import(path);

      // ? correct way
      const configModule = await import(
        `../configurations/${tenant}/${entity}/index.ts`
      );
      const newConfig = configModule.default;

      set(() => ({
        config: newConfig,
      }));
    } catch (e) {
      setEntity("");

      set(() => ({
        config: "",
        // configdefaultConfig,
      }));

      throw new Error(`Config not found for ${tenant}/${entity}`);

      // todo: not able to show any message

      // console.warn(
      //   `Config not found for ${tenant}/${entity}, using default config`
      // );
    }
  },
});

const useConfigStore = create<ConfigState>()(
  devtools(
    persist(configStore, {
      name: "config-storage",
    }),
    { name: "Config Store" }
  )
);

export default useConfigStore;
