import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
import { CITY_STORE_CONSTANTS } from "../constants";
import dayjs from "dayjs";

export interface HistoricalPlaygroundParams {
  city: string | null;
  store: number | string | null;
  metric?: string[];
  event: string;
  timeRange: object[] | string; //empty string, try null
  comparison: string[];
  categories?: string[];
}

// rather than storing them directly, store them in a category dict:
/* 
  {
      overall: HistoricalPlaygroundParams;
     [categoryId: string]: HistoricalPlaygroundParams;
  }
*/

interface HistoricalPlaygroundState {
  params: {
    [categoryId: string]: HistoricalPlaygroundParams;
  };
  setParams: (categoryId: string, params: HistoricalPlaygroundParams) => void;
  resetParams: () => void;
}

// Initial state for a single categoryId
export const INITIAL_PARAMS: HistoricalPlaygroundParams = {
  city: CITY_STORE_CONSTANTS.panIndia.value,
  store: CITY_STORE_CONSTANTS.overall.value,
  metric: [],
  event: "",
  timeRange: [dayjs(new Date()).subtract(1, "day").startOf("day"), dayjs(new Date()).subtract(1, "day").endOf("day")],
  comparison: [],
  categories: [],
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const historicalPlaygroundStore = (set: any): HistoricalPlaygroundState => ({
  params: {
    "": INITIAL_PARAMS,
  },

  setParams: (category: string, categoryParams: HistoricalPlaygroundParams) =>
    set((state: HistoricalPlaygroundState) => ({
      params: { ...state.params, [category]: categoryParams },
    })),

  resetParams: () => set(() => ({ params: {} })),
});

const useHistoricalPlaygroundStore = create<HistoricalPlaygroundState>()(
  devtools(
    persist(historicalPlaygroundStore, {
      name: "historical-playground-storage",
    }),
    { name: "Historical Playground Store" }
  )
);

export default useHistoricalPlaygroundStore;
