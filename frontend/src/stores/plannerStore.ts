import dayjs, { Dayjs } from "dayjs";
import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
import { CITY_STORE_CONSTANTS, TIME_GRAIN_OPTIONS } from "../constants";
import { OtherUserInputInterface } from "../utils/getForecastColumns";

export type TimeGrain = "weekly" | "daily" | "hourly";

export interface DirtyField {
  single_aggregation: {
    value: string;
    grain: string;
  };
  start_time: string | Dayjs;
  metric_name: string;
  time_grain: TimeGrain;
  value: number;
}

// Base params interface for each categoryId
export interface PlannerParams {
  city: string | null;
  store: number | string | null;
  date: string | Dayjs;
  forecast_id: number | null;
}

// State interface with categoryId support
interface PlannerState {
  // Category-based params storage
  params: {
    [categoryId: string]: PlannerParams;
  };
  timeGrain: TimeGrain;
  setParams: (categoryId: string, params: PlannerParams) => void;
  resetParams: () => void;
  setTimeGrain: (timeGrain: TimeGrain) => void;

  // Editable Table State
  editTable: boolean;
  editingCell: {
    row: string;
    col: string;
  } | null;
  // Store dirty fields per categoryId
  dirtyFields: Record<string, Record<string, DirtyField[]>>; //dirty Fields of current user, categorized
  otherUserInputs: OtherUserInputInterface[]; //dirty Fields of other users, categorized
  getDirtyFieldsForParams: (categoryId: string) => DirtyField[];
  setEditTable: (editTable: boolean) => void;
  setEditingCell: (cell: { row: string; col: string } | null) => void;
  setDirtyFields: (categoryId: string, fields: DirtyField[]) => void;
  setOtherUserInputs: (fields: OtherUserInputInterface[]) => void;
  resetDashboardDirtyFields: (categoryId: string) => void;
  resetTableState: () => void;
  getDashboardKey: (categoryId: string) => string;
}

// Initial state for a single categoryId
export const INITIAL_PARAMS: PlannerParams = {
  city: CITY_STORE_CONSTANTS.panIndia.value,
  store: CITY_STORE_CONSTANTS.overall.value,
  date: dayjs(new Date()).startOf("week").add(1, 'day').format("YYYY-MM-DD:HH:mm:ss"), //Todo: need to check why it doesn't take monday as it's 0th index
  forecast_id: null,
};

// Initial table state with categoryId support
const INITIAL_TABLE_STATE = {
  editTable: false,
  editingCell: null,
  dirtyFields: {},
  otherUserInputs: [],
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const plannerStore = (set: any, get: any): PlannerState => ({
  params: {
    "": INITIAL_PARAMS,
  },
  timeGrain: TIME_GRAIN_OPTIONS.weekly,

  setParams: (categoryId: string, params: PlannerParams) =>
    set((state: PlannerState) => ({
      params: { ...state.params, [categoryId]: params },
    })),

  resetParams: () => set({ params: { "": INITIAL_PARAMS } }),

  setTimeGrain: (timeGrain) => set({ timeGrain }),

  // Editable Table State |

  ...INITIAL_TABLE_STATE,
  setEditTable: (editTable) => set({ editTable }),

  setEditingCell: (cell) => set({ editingCell: cell }),

  getDirtyFieldsForParams: (categoryId) => {
    const { dirtyFields, getDashboardKey } = get();
    const dashboardKey = getDashboardKey(categoryId);
    return dirtyFields[categoryId]?.[dashboardKey] || [];
  },

  setDirtyFields: (categoryId, fields) => {
    const { dirtyFields, getDashboardKey } = get();
    const dashboardKey = getDashboardKey(categoryId);

    set({
      dirtyFields: {
        ...dirtyFields,
        [categoryId]: {
          ...(dirtyFields[categoryId] || {}),
          [dashboardKey]: fields,
        },
      },
    });
  },

  setOtherUserInputs: (fields) => set({ otherUserInputs: fields }),

  resetDashboardDirtyFields: (categoryId) => {
    const { dirtyFields, getDashboardKey } = get();
    const dashboardKey = getDashboardKey(categoryId);
    set({
      editTable: false,
      editingCell: null,
      dirtyFields: {
        ...dirtyFields,
        [categoryId]: {
          ...(dirtyFields[categoryId] || {}),
          [dashboardKey]: [],
        },
      },
    });
  },

  resetTableState: () => {
    set({
      ...INITIAL_TABLE_STATE,
      editTable: false,
      editingCell: null,
    });
  },

  getDashboardKey: (categoryId) => {
    const { params, timeGrain } = get();
    const obj = { ...params[categoryId], timeGrain };
    return JSON.stringify(obj, Object.keys(obj).sort());
  },
});

const usePlannerStore = create<PlannerState>()(
  devtools(
    persist(plannerStore, {
      name: "planner-storage",
    }),
    { name: "Planner Store" }
  )
);

export default usePlannerStore;
