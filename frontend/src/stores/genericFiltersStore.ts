/* eslint-disable @typescript-eslint/no-explicit-any */
import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";

type GenericFiltersStore = {
  // filtersData: Record<string, any>;
  filtersData: Record<
    string,
    Record<string, string | number | boolean | Date>[]
  >;
  setFilterData: (key: string, data: any) => void;
  resetAllFilters: () => void;
};

const genericFiltersStore = (set: any) => ({
  filtersData: {},

  setFilterData: (key: string, data: any) =>
    set((state: any) => ({
      filtersData: {
        ...state.filtersData,
        [key]: data,
      },
    })),

  resetAllFilters: () =>
    set(() => ({
      filtersData: {},
    })),
});

const useGenericFiltersStore = create<GenericFiltersStore>()(
  devtools(
    persist(genericFiltersStore, {
      name: "generic-filter-storage",
    }),
    { name: "Generic Filter Store" }
  )
);

export default useGenericFiltersStore;
