import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";

interface UserInfo {
  id: number;
  name: string;
  email: string;
  is_allowed: boolean;
  tenant_id: string | number;
  profile_url?: string;
}

interface AuthState {
  isAuthenticated: boolean;
  user: UserInfo | null;
  setAuth: (user: UserInfo) => void;
  clearAuth: () => void;
}

const initialState = {
  isAuthenticated: false,
  user: null,
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const authStore = (set: any): AuthState => ({
  ...initialState,

  setAuth: (user: UserInfo) =>
    set(() => ({
      isAuthenticated: true,
      user,
    })),

  clearAuth: () =>
    set(() => ({
      ...initialState,
    })),
});

const useAuthStore = create<AuthState>()(
  devtools(
    persist(authStore, {
      name: "auth-storage",
    }),
    {
      name: "Auth Store",
    }
  )
);

export default useAuthStore;
