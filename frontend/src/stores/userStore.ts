import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";

interface UserState {
  currentNav: string;
  setCurrentNav: (nav: string) => void;

  tenants: string[];
  setTenants: (tenants: string[]) => void;

  selectedTenant: string;
  setSelectedTenant: (selectedTenant: string) => void;

  entities: string[];
  setEntities: (entities: string[]) => void;

  selectedEntity: string;
  setSelectedEntity: (selectedEntity: string) => void;
}

// Todo: add tenant, entity and category data setter

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const userStore = (set: any, get: any): UserState => ({
  currentNav: "",
  setCurrentNav: (nav: string) => set(() => ({ currentNav: nav })),

  tenants: [],
  setTenants: (tenants: string[]) => set(() => ({ tenants })),

  selectedTenant: "",
  setSelectedTenant: (selectedTenant: string) =>
    set(() => ({ selectedTenant })),

  entities: [],
  setEntities: (entities: string[]) => set(() => ({ entities })),

  selectedEntity: "",
  setSelectedEntity: (selectedEntity: string) =>
    set(() => ({ selectedEntity })),
});

const useUserStore = create<UserState>()(
  devtools(
    persist(userStore, {
      name: "user-store",
    }),
    { name: "User Store" }
  )
);

export default useUserStore;
