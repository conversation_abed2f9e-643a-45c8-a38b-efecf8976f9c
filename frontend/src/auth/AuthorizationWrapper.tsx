import { Navigate, useLocation } from "react-router-dom";
import { userRoutes } from "../routes";
// import useAuthStore from "../stores/authStore";

//* Authentication wrapper --> not being used atm
function AuthorizationWrapper({ children }: { children: React.ReactNode }) {
  const location = useLocation();

  // const { isAuthenticated } = useAuthStore((state) => state);
  // const isAuthorised = some function (input = location url) --> output, if the user can access the page
  const isAuthorised = true;

  if (!isAuthorised) {
    return (
      <Navigate
        to={userRoutes.accessDenied}
        state={{ from: location }}
        replace
      />
    );
  }

  return <>{children}</>;
}

export default AuthorizationWrapper;
