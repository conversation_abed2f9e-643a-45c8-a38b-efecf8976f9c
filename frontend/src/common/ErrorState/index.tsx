import Title from "antd/es/typography/Title";
import ErrorBox from "../../assets/error-state.webp";
import { ErrorStateProps } from "./types";

function ErrorState({
  height = 400,
  width = 400,
  flexDirection = "column",
  title = "Some Error Occured.",
  subTtitle = "Please retry after sometime.",
  titleSize = 4,
  subTitleSize = 5,
}: ErrorStateProps) {
  return (
    <div
      className={`flex items-center justify-center text-slate-100 tracking-wider`}
      style={{ flexDirection: flexDirection }}
    >
      <div>
        <img
          src={ErrorBox}
          alt="empty-state"
          className="object-contain p-4"
          height={height}
          width={width}
        />
      </div>
      <div className="flex flex-col items-center">
        <Title level={titleSize} style={{ color: "#4f4f4f", margin: 0 }}>
          {title}
        </Title>
        <Title
          level={subTitleSize}
          style={{
            color: "#4f4f4f",
            margin: 0,
            fontWeight: 300,
          }}
          className="font-thin"
        >
          {subTtitle}
        </Title>
      </div>
    </div>
  );
}

export default ErrorState;
