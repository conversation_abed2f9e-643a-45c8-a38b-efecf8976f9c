import Title from "antd/es/typography/Title";
import { HeaderProps } from "./types";
import "./styles.css";

function Header({ title, subTitle, btnContainer, children }: HeaderProps) {
  return (
    <div className="header-container w-full bg-white px-8 py-4 sticky top-0 z-10">
      <div className="w-full flex items-center justify-between">
        <div>
          <Title level={3} className="no-margin">
            {title}
          </Title>
          <div>{subTitle}</div>
        </div>

        {btnContainer}
      </div>

      {children}
    </div>
  );
}

export default Header;
