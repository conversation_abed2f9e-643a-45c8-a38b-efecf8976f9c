export const getFormattedValue = (value: number | string, type: string) => {
  if (typeof value === "string") return value; //todo: correct this condition..

  switch (type) {
    case "percentage":
      return `${(value || 0).toFixed(1)}%`;
    case "seconds":
      return `${Math.floor((value || 0) / 60)}m ${Math.floor(
        (value || 0) % 60
      )}s`;
    case "minutes":
      return `${Math.floor(value || 0)}m`;
    case "hours":
      return `${(value || 0).toFixed(1)}h`;
    case "count":
      return Math.floor(value || 0).toLocaleString();
    default:
      return (value || 0).toString();
  }
};
