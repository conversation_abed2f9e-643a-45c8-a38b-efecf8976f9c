import dayjs, { Dayjs } from "dayjs";
import { HistoricalPlaygroundParams } from "../stores/historicalPlaygroundStore";
import { CITY_STORE_CONSTANTS } from "../constants";

export const getHistoricalPlaygroundParams = (
  values: HistoricalPlaygroundParams,
  filtersData: any
) => {
  const { city, store, metric, event, timeRange, comparison, categories } =
    values ?? {};

  const eventData = filtersData?.eventsFilterData?.find(
    (e) => e?.event_id === event
  );

  if (!store) throw new Error("Store is required");
  if (!timeRange) throw new Error("Time range is required");

  // dayjs by default converts the object into UTC when saved in storage. therefore converting it again to dayjs format first to retain IST date time
  const [startTime, endTime] = (timeRange as [Dayjs, Dayjs]).map((date) => {
    const dt = dayjs(date);
    return dt?.format("YYYY-MM-DD HH:mm:ss");
  });

  return {
    city: city === CITY_STORE_CONSTANTS.panIndia.value ? null : city,
    store_id: store === CITY_STORE_CONSTANTS.overall.value ? null : store,
    metric_list: metric,
    start_time: startTime,
    end_time: endTime,
    comparison,
    event,
    event_date: event ? JSON.stringify({
      current_yr_date: dayjs(eventData?.event_dt)?.format(
        "YYYY-MM-DD HH:mm:ss"
      ),
      last_yr_date: dayjs(eventData?.event_dt_last_year)?.format(
        "YYYY-MM-DD HH:mm:ss"
      ),
    }) : "",
    categories,
  };
};
