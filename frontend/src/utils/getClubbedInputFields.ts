import dayjs, { Dayjs } from "dayjs";
import { OtherUserInputInterface } from "./getForecastColumns";
import { TimeGrain } from "../stores/plannerStore";

export interface InputInterface {
  input_id: number;
  entity_id: number;
  start_time: string | Date;
  time_grain: TimeGrain;
  status: string;
  updated_at: string | Date;
  values: [
    {
      metric_name: string;
      value: number;
    }
  ];
  is_processed: boolean;
}

export const getClubbedInputFields = (
  inputs: InputInterface[]
): OtherUserInputInterface[] => {
  const clubbedInputs = inputs.reduce((acc, input) => {
    const existingInput = acc.find(
      (i) =>
        i.entity_id === input.entity_id &&
        i.status === input.status &&
        i.is_processed === input.is_processed
    );

    if (existingInput) {
      existingInput.input_ids.push(input.input_id);

      existingInput.values.push(
        ...(input?.values?.map((val) => ({
          ...val,
          updated_at: input.updated_at,
        })) || [])
      );

      existingInput.updated_at = dayjs(
        Math.max(dayjs(existingInput.updated_at), dayjs(input.updated_at))
      ) as Dayjs;
    } else {
      const newInput = {
        ...input,
        input_ids: [input.input_id],
        values: input.values?.map((val) => ({
          ...val,
          updated_at: input.updated_at,
        })),
      };

      acc.push(newInput);
    }
    return acc;
  }, [] as OtherUserInputInterface[]);

  clubbedInputs.forEach((input: OtherUserInputInterface) => {
    const sortedValues = input.values.sort((a, b) => {
      return dayjs(b.updated_at).diff(dayjs(a.updated_at));
    });

    // now only take initial values of each metric
    input.values = sortedValues.filter((val, index: number) => {
      return (
        sortedValues.findIndex((v) => v.metric_name === val.metric_name) ===
        index
      );
    });
  });

  return clubbedInputs;
};
