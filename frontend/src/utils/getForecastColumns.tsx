import { <PERSON><PERSON>, FormInstance, message, Modal, Tooltip } from "antd";
import {
  ApprovalInputsInterface,
  MetricColumn,
  TableRecord,
} from "../components/EditableTable/types";
import { CalendarOutlined, CheckOutlined } from "@ant-design/icons";
import { CITY_STORE_CONSTANTS, TIME_GRAIN_OPTIONS } from "../constants";
import { DirtyField, PlannerParams, TimeGrain } from "../stores/plannerStore";
import dayjs, { Dayjs } from "dayjs";
import { modelChangeInterface } from "../apis/forecast";
import { InputInterface } from "./getClubbedInputFields";

type BaseColumn = {
  title: string | number;
  dataIndex: string;
  key: string;
};

type RenderColumn = BaseColumn & {
  render: (text: string) => JSX.Element;
  fixed?: string;
};

type EditableColumn = BaseColumn & {
  onCell: (record: TableRecord) => any;
};

type ActionColumn = {
  title: string;
  key: string;
  render: (text: string, record: TableRecord) => JSX.Element;
};

export interface OtherUserInputInterface
  //* removes both input_id and values from the parent interface
  extends Omit<InputInterface, "values" | "input_id"> {
  values: {
    updated_at: string | Date | Dayjs;
    metric_name: string;
    value: number;
  }[];
  input_ids: number[];
}

const { confirm } = Modal;

export function getForecastColumns({
  indexCols = {},
  cols = {},
  primaryKey,
  plannerParams,
  dashboardDirtyFields,
  otherUserInputs,
  filtersData,
  editingCell,
  setEditingCell,
  setDirtyFields,
  setOpen,
  setModalFilters,
  timeGrain,
  form,
  editTable = false,
  categoryId = "",
  isAnApprover = false,
  approveInputs,
  selectedModels,
  models,
  handleModelChange,
  expandedRowKeys,
  setExpandedRowKeys,
}: {
  indexCols: MetricColumn;
  cols: MetricColumn;
  primaryKey: string;
  plannerParams: PlannerParams;
  filtersData?: Record<string, any>;
  editingCell?: { row: string; col: string } | null;
  dashboardDirtyFields: DirtyField[];
  otherUserInputs: OtherUserInputInterface[];
  setEditingCell: (cell: { row: string; col: string } | null) => void;
  setDirtyFields: (category: string, fields: DirtyField[]) => void;
  setOpen: (open: boolean) => void;
  setModalFilters: (filters: Record<string, any>) => void;
  timeGrain: TimeGrain;
  form: FormInstance;
  editTable?: boolean;
  categoryId: string;
  isAnApprover?: boolean;
  approveInputs: (inputs: ApprovalInputsInterface[]) => void;
  selectedModels?: Record<string, string>;
  models?: Record<string, string>[];
  handleModelChange: (modelChange: modelChangeInterface) => void;
  expandedRowKeys: React.Key[];
  setExpandedRowKeys: (keys: React.Key[]) => void;
}) {
  const isEditingSomeCell = (record: TableRecord, dataIndex: string) => {
    return (
      editingCell?.row === record[primaryKey] && editingCell?.col === dataIndex
    );
  };

  const getDirtyField = (primaryKeyValue: string, dataIndex: string) => {
    return dashboardDirtyFields.find(
      (field) =>
        field.single_aggregation.value === primaryKeyValue &&
        field.metric_name === dataIndex
    );
  };

  const getOtherUserFieldValue = (
    primaryKeyValue: string,
    dataIndex: string
  ) => {
    //* inputs approved by approvers
    const approvedInputs = otherUserInputs.find(
      (input) =>
        input.entity_id === Number(primaryKeyValue) &&
        input.status === "approved"
    );

    //* inputs not approved and not processed for model output
    const unprocessedInputs = otherUserInputs.find(
      (input) =>
        input.entity_id === Number(primaryKeyValue) &&
        input.status === "pending" &&
        !input.is_processed
    );

    //* inputs not approved but processed for model output
    const processedInputs = otherUserInputs.find(
      (input) =>
        input.entity_id === Number(primaryKeyValue) &&
        input.status === "pending" &&
        input.is_processed
    );

    return {
      approvedInputs: approvedInputs
        ? {
            value: approvedInputs?.values?.find(
              (field) => field?.metric_name === dataIndex
            )?.value,
            updated_at: approvedInputs?.updated_at,
          }
        : undefined,

      processedInputs: processedInputs
        ? {
            value: processedInputs?.values?.find(
              (field) => field?.metric_name === dataIndex
            )?.value,
            updated_at: processedInputs?.updated_at,
          }
        : undefined,

      unprocessedInputs: unprocessedInputs
        ? {
            value: unprocessedInputs?.values?.find(
              (field) => field?.metric_name === dataIndex
            )?.value,
            updated_at: unprocessedInputs?.updated_at,
          }
        : undefined,
    };
  };

  const isRowApproved = (record: TableRecord) => {
    const primaryKeyValue = record[primaryKey] as string;
    const inputs = otherUserInputs.find(
      (input) => input.entity_id === Number(primaryKeyValue)
    );

    return inputs?.status === "approved";
  };

  const edit = (record: TableRecord, dataIndex: string) => {
    const dirtyField = getDirtyField(record[primaryKey] as string, dataIndex);
    const { approvedInputs, processedInputs, unprocessedInputs } =
      getOtherUserFieldValue(record[primaryKey] as string, dataIndex);

    const currentValue =
      dirtyField?.value ??
      unprocessedInputs?.value ??
      processedInputs?.value ??
      approvedInputs?.value ??
      record[dataIndex];

    form.setFieldsValue({ [dataIndex]: currentValue });
    setEditingCell({ row: record[primaryKey] as string, col: dataIndex });
  };

  const save = async (record: TableRecord, dataIndex: string) => {
    try {
      const { approvedInputs, processedInputs, unprocessedInputs } =
        getOtherUserFieldValue(record[primaryKey] as string, dataIndex);

      const value = await form.validateFields();
      const newValue = value[dataIndex];
      const originalValue = record[dataIndex];

      const valueMatchesLastInput =
        unprocessedInputs?.value === undefined
          ? processedInputs?.value === undefined
            ? approvedInputs?.value === undefined
              ? newValue === originalValue
              : newValue === approvedInputs?.value
            : newValue === processedInputs?.value
          : newValue === unprocessedInputs?.value;

      if (valueMatchesLastInput) {
        // Remove from dirty fields if value matches original
        setDirtyFields(
          categoryId,
          dashboardDirtyFields.filter(
            (field) =>
              field.single_aggregation.value === record[primaryKey] ||
              field.metric_name !== dataIndex
          )
        );
      } else {
        // Update or add to dirty fields
        const existingIndex = dashboardDirtyFields.findIndex(
          (field) =>
            field.single_aggregation.value === record[primaryKey] &&
            field.metric_name === dataIndex
        );

        const newField: DirtyField = {
          single_aggregation: {
            value: record[primaryKey] as string,
            grain: primaryKey,
          },
          start_time: dayjs(
            (record?.date as unknown as Dayjs) || (plannerParams?.date as Dayjs)
          ).format("YYYY-MM-DD HH:mm:ss"),
          metric_name: dataIndex,
          time_grain: timeGrain,
          value: newValue,
        };

        if (existingIndex >= 0) {
          const newFields = [...dashboardDirtyFields];
          newFields[existingIndex] = newField;
          setDirtyFields(categoryId, newFields);
        } else {
          setDirtyFields(categoryId, [...dashboardDirtyFields, newField]);
        }
      }

      setEditingCell(null);
    } catch (errInfo) {
      console.log("Validate Failed:", errInfo);
    }
  };

  const handleDetailView = (record: TableRecord) => {
    const primaryKeyVal = record[primaryKey];

    if (timeGrain === TIME_GRAIN_OPTIONS.weekly) {
      // is a store view
      const storeViewFilter = filtersData?.storesFilterData.find(
        (store: { entity_id: number }) => store.entity_id === primaryKeyVal
      );

      // is a city view
      const cityViewFilter = filtersData?.storesFilterData.find(
        (store: { city_name: string }) => store.city_name === primaryKeyVal
      );

      const cityView = cityViewFilter
        ? {
            ...cityViewFilter,
            entity_id: CITY_STORE_CONSTANTS.overall.value,
          }
        : null;
      const filterValue = storeViewFilter || cityView;

      if (!filterValue) return;

      setOpen(true);

      setModalFilters({
        ...plannerParams,
        city: filterValue.city_name,
        store: filterValue.entity_id,
      });
    }
  };

  const handleApprove = (record: TableRecord) => {
    const primaryKeyVal = record[primaryKey] as string;

    const inputIds = otherUserInputs?.find(
      (input) => input.entity_id === Number(primaryKeyVal)
    )?.input_ids;

    confirm({
      title: (
        <span>
          Do you want to approve the inputs for{" "}
          <span className="text-blue-600">{primaryKeyVal}</span>?
        </span>
      ),
      icon: null,
      content: "Once approved, this action cannot be undone.",
      okText: "Approve",
      okType: "primary",
      cancelText: "Cancel",
      onOk() {
        if (!inputIds) return;
        approveInputs(
          inputIds.map((id) => ({ input_id: id, status: "approved" }))
        );
        message.success("Inputs approved successfully");
      },
      onCancel() {
        // console.log("Cancelled");
      },
    });
  };

  const isPanIndiaSelected =
    plannerParams?.city === CITY_STORE_CONSTANTS.panIndia.value;

  //* constant columns
  const columns: (RenderColumn | EditableColumn | ActionColumn)[] = [
    ...Object.entries(indexCols).map(([colKey, col]) => ({
      title: col.display_name,
      dataIndex: colKey,
      key: colKey,
      render: (text: string | number) => {
        if (timeGrain === TIME_GRAIN_OPTIONS.weekly && !isPanIndiaSelected) {
          const store = filtersData?.storesFilterData.find(
            (store: { entity_id: number }) => store.entity_id === text
          );

          return <b>{store?.entity_name || "-"}</b>;
        }
        return <b>{text}</b>;
      },
      width: "200px",
      fixed: "left",
    })),
  ];

  if (timeGrain === TIME_GRAIN_OPTIONS.weekly) {
    columns.push(
      ...Object.entries(cols).map(([colKey, col]) => {
        return {
          title: `${col.display_name} (${col.metric_type})`,
          // dataIndex: `values.selectedModel.colKey`, //* this key is not picked (antd doesn't pick dynamically). it's just for reference as to what is being picked
          dataIndex: colKey,
          key: colKey,
          width: "400px",
          ellipsis: true,
          onCell: (record: TableRecord) => ({
            primaryKey,
            record,
            dataIndex: colKey,
            title: col.display_name,
            alreadyEditing: editTable
              ? isEditingSomeCell(record, colKey)
              : false,
            edit,
            save,
            getDirtyField,
            getOtherUserFieldValue,
            isEditable: col.editable,
            isAnApprover: isAnApprover,
            selectedModels: selectedModels,
            cols,
            models,
            handleModelChange,
            expandedRowKeys,

            //entity, cell key
            onTriggerRowExpand: (key: string) => {
              if (!expandedRowKeys.includes(key)) {
                setExpandedRowKeys([...expandedRowKeys, key]);
              }
            },
            onTriggerRowCollapse: (key: string) => {
              setExpandedRowKeys(expandedRowKeys.filter((k) => k !== key));
            },
          }),
        };
      }),
      {
        title: "Actions",
        key: "action",
        width: "200px",
        fixed: "right",
        render: (text: string, record: TableRecord) => (
          <div className="flex gap-2">
            <Tooltip title="Show Detailed View">
              <Button
                type="primary"
                shape="circle"
                size="small"
                icon={<CalendarOutlined />}
                onClick={() => handleDetailView(record)}
              />
            </Tooltip>

            {isAnApprover &&
              otherUserInputs?.find(
                (input) => input.entity_id === Number(record[primaryKey])
              ) && (
                <Tooltip title={isRowApproved(record) ? "Approved" : "Approve"}>
                  <Button
                    type="primary"
                    shape="circle"
                    size="small"
                    icon={<CheckOutlined />}
                    disabled={isRowApproved(record)}
                    onClick={() => handleApprove(record)}
                  />
                </Tooltip>
              )}
          </div>
        ),
      }
    );
  } else {
    columns.push(
      ...Object.entries(cols).map(([colKey, col]) => ({
        title: col.display_name,
        dataIndex: colKey,
        key: colKey,
        render: (text: string) => <span>{text}</span>,
      }))
    );
  }

  return columns;
}

export default getForecastColumns;
