import { message } from "antd";
import { AxiosResponse } from "axios";

interface ErrorResponse {
  error?: string;
  err?: string;
  message?: string;
  status?: number;
}

function validateResponse<T>(
  response: AxiosResponse<T> | ErrorResponse | null | undefined,
  successMsg?: string
): AxiosResponse<T> {
  if (
    !response ||
    ("error" in response && response.error) ||
    ("err" in response && response.err) ||
    ("status" in response && response.status !== 200) ||
    ("data" in response &&
      typeof response.data === "object" &&
      response.data &&
      "status" in response.data &&
      response.data.status !== "success")
  ) {
    throw new Error(
      (response && "err" in response ? response.err : undefined) ||
        (response && "message" in response ? response.message : undefined) ||
        "Failed to fetch global attributes"
    );
  }

  if (successMsg) message.success(successMsg);
  return response as AxiosResponse<T>;
}

export default validateResponse;
