import dayjs from "dayjs";
import {
  AGGREGATION_GRAIN,
  CITY_STORE_CONSTANTS,
  TIME_GRAIN_OPTIONS,
} from "../constants";
import { PlannerParams, TimeGrain } from "../stores/plannerStore";

export const getPlannerParams = (
  values: PlannerParams,
  timeGrain: TimeGrain
) => {
  const { city, store, date, forecast_id } = values ?? {};
  if (!city) throw new Error("City is required");
  if (!store) throw new Error("Store is required");
  if (!date) throw new Error("Date is required");
  if (!timeGrain) throw new Error("View Type is required");

  const selectedCity =
    city === CITY_STORE_CONSTANTS.panIndia.value ? null : city;

  //* store_id
  const selectedStore =
    store === CITY_STORE_CONSTANTS.overall.value ? null : store;

  const getGrain = () => {
    if (timeGrain === TIME_GRAIN_OPTIONS.weekly) {
      if (selectedStore) return AGGREGATION_GRAIN.ENTITY;
      if (selectedCity) return AGGREGATION_GRAIN.ENTITY;
      return AGGREGATION_GRAIN.CITY;
    } else {
      if (selectedStore) return AGGREGATION_GRAIN.ENTITY;
      if (selectedCity) return AGGREGATION_GRAIN.CITY;
      return AGGREGATION_GRAIN.PAN_INDIA;
    }
  };

  const getValue = () => {
    if (selectedStore) return selectedStore;
    if (selectedCity) return selectedCity;
    return [];
  };

  const params = {
    forecast_id: forecast_id,
    start_time: dayjs(date)?.format("YYYY-MM-DD HH:mm:ss"),
    time_grain: timeGrain,
  };

  if (timeGrain === TIME_GRAIN_OPTIONS.weekly) {
    return {
      ...params,
      multi_aggregation_values: [getValue()], // list
      multi_aggregation_grain: getGrain(),
    };
  }

  //daily, hourly
  return {
    ...params,
    single_aggregation_value: getValue(), // single value
    single_aggregation_grain: getGrain(),
  };
};
