/* eslint-disable @typescript-eslint/no-explicit-any */
import isEmpty from "lodash.isempty";
import { PARAM_ARRAY_KEYS } from "../constants";

type Params = Record<string, string | string[]>;

function parseAndSetUrlParams(
  searchParams: URLSearchParams,
  setUrlParams: (categoryName: string, params: any) => void,
  categoryName: string
) {
  if (!isEmpty(Array.from(searchParams.entries()))) {
    const formValues: Params = {};
    for (const [key, value] of searchParams.entries()) {
      if (formValues[key]) {
        if (Array.isArray(formValues[key])) {
          formValues[key].push(value);
        } else {
          formValues[key] = [formValues[key], value]; //creating array
        }
      } else {
        if (PARAM_ARRAY_KEYS.includes(key)) {
          formValues[key] = [value];
        } else {
          formValues[key] = value;
        }
      }
    }

    setUrlParams(categoryName, formValues);
  }
}

export default parseAndSetUrlParams;
