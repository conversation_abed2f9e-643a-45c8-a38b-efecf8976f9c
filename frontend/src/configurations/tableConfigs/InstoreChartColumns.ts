import { TIME_PERIOD_OPTIONS } from "../../constants";
import { HistoricalPlaygroundParams } from "../../stores/historicalPlaygroundStore";

export const getInstoreChartColumns = (params: HistoricalPlaygroundParams) => [
  {
    title: "Date",
    dataIndex: "date",
    key: "date",
    sorter: (a: any, b: any) =>
      new Date(a.date).getTime() - new Date(b.date).getTime(),
    defaultSortOrder: "descend",
    fixed: "left",
  },
  {
    title: "Hour",
    dataIndex: "hour",
    key: "hour",
    sorter: (a: any, b: any) => a.hour - b.hour,
    fixed: "left",
  },
  {
    title: "Current",
    dataIndex: "current_value",
    key: "current_value",
    sorter: (a: any, b: any) => (a.current_value || 0) - (b.current_value || 0),
  },
  ...TIME_PERIOD_OPTIONS.map((option) => {
    if (!params?.[option]) return null;
    return {
      title: option,
      dataIndex: option.toLowerCase(),
      key: option.toLowerCase(),
      sorter: (a: any, b: any) => {
        const aValue = a[option.toLowerCase()] || 0;
        const bValue = b[option.toLowerCase()] || 0;
        return aValue - bValue;
      },
    };
  }).filter(Boolean),
];
