import { apiUrl } from "../../../../env";

const storeOpsConfig = {
  tenant: "blinkit",
  entity: "storeops",

  fetchFilters: {
    // apis:  `${apiUrl}/v1/{tenant}/{entity}/....`,
    store: {
      keyForStorage: "storesFilterData",
      optionsEndpoint: `${apiUrl}/v1/blinkit/storeops`,
      labelKey: "entity_name",
      valueKey: "entity_id",
    },
    events: {
      keyForStorage: "eventsFilterData",
      optionsEndpoint: `${apiUrl}/v1/blinkit/events`,
    },
    metrics: {
      keyForStorage: "metricsFilterData",
      optionsEndpoint: `${apiUrl}/v1/blinkit/storeops/metrics`,
    },
    categories: {
      keyForStorage: "categoriesFilterData",
      optionsEndpoint: `${apiUrl}/v1/blinkit/storeops/categories/`,
    },
    forecasts: {
      keyForStorage: "forecastsFilterData",
      optionsEndpoint: `${apiUrl}/v1/blinkit/storeops/forecasts`,
    },
  },
};

export default storeOpsConfig;
