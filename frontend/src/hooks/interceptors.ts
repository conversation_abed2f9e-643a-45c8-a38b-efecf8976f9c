import axios from "axios";
import { message } from "antd";
import useAuthStore from "../stores/authStore";

// Setup interceptors for the global axios instance (this instance does not set withCredentials: true for all requests i.e. axios instance created in apis folder)
export const setupGlobalInterceptors = () => {
  axios.defaults.withCredentials = true;
  axios.interceptors.response.use(
    (response) => response,
    (error) => {
      if (error.response?.status === 401) {
        const { clearAuth } = useAuthStore.getState();
        message.error("Session expired. Please login again.");
        setTimeout(() => {
          window.location.replace("/login");
          clearAuth();
        }, 1000);
      }
      return Promise.reject(error);
    }
  );
};
