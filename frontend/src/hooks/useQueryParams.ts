import { useSearchParams } from "react-router-dom";

// * to handle single key update
type SetQueryParam = (key: string, value: string | number | null) => void;

// * to handle multiple keys update
type SetQueryParams = (params: Record<string, string | number | null>) => void;

const useQueryParams = (): [URLSearchParams, SetQueryParam, SetQueryParams] => {
  const [searchParams, setSearchParams] = useSearchParams();

  const setQueryParam: SetQueryParam = (key, value) => {
    const newParams = new URLSearchParams(searchParams.toString());

    if (value === null || value === "") {
      newParams.delete(key);
    } else {
      newParams.set(key, value);
    }

    setSearchParams(newParams);
  };

  const setQueryParams: SetQueryParams = (params) => {
    const newParams = new URLSearchParams(searchParams.toString());

    Object.entries(params).forEach(([key, value]) => {
      if (value === null || value === "") {
        newParams.delete(key);
      } else {
        newParams.set(key, value);
      }
    });

    setSearchParams(newParams);
  };

  return [searchParams, setQueryParam, setQueryParams];
};

export default useQueryParams;
