import axios from "axios";
import validateResponse from "../utils/validateResponse";

export const useFetchAllFilters = ({
  fetchFilters = {},
  setFilterData,
  enabled,
}: {
  fetchFilters: Record<string, Record<string, string>>;
  setFilterData: (key: string, data: any) => void;
  enabled?: boolean;
}) => {
  const queries = Object?.entries(fetchFilters)?.map(([key, meta]) => ({
    queryKey: ["filter", key],
    queryFn: async () => {
      const response = await axios.get(meta.optionsEndpoint);
      const res = validateResponse(response);
      const { data } = res.data || {};
      setFilterData(meta.keyForStorage, data);
      return res;
    },
    enabled,
  }));

  return queries;
};
