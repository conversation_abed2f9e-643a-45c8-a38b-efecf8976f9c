import { createBrowserRouter, Navigate, RouteObject } from "react-router-dom";

// routes
import { userRoutes } from "../routes";
import AdminRoutes from "./AdminRoutes";

// wrappers
import ProtectedRoute from "../auth/ProtectedRoute";
import TenantWrapper from "../configWrappers/TenantWrapper";
import EntityWrapper from "../configWrappers/EntityWrapper";
import FiltersWrapper from "../configWrappers/FiltersWrapper";

// layout
import CoreLayout from "../layout/CoreLayout";

// pages
import Login from "../pages/Login";
import AuthCallback from "../pages/AuthCallback";
import PageNotFound from "../pages/404";
import AccessDenied from "../pages/403";

const AppRoutes: RouteObject[] = [
  {
    path: "/*",
    element: (
      <ProtectedRoute>
        <TenantWrapper>
          <EntityWrapper>
            <FiltersWrapper>
              <CoreLayout>
                <AdminRoutes />
              </CoreLayout>
            </FiltersWrapper>
          </EntityWrapper>
        </TenantWrapper>
      </ProtectedRoute>
    ),
  },
  {
    path: userRoutes.login,
    element: <Login />,
  },
  {
    // path: "/auth/google/callback",
    path: "/auth/callback",
    element: <AuthCallback />,
  },
  {
    path: userRoutes.pageNotFound,
    element: <PageNotFound />,
  },
  {
    path: userRoutes.accessDenied,
    element: <AccessDenied />,
  },
  {
    path: userRoutes.accessDenied,
    element: <AccessDenied />,
  },
  {
    path: "*",
    element: <Navigate to={userRoutes.pageNotFound} />,
  },
];

export const AppRouter = createBrowserRouter(AppRoutes);
