import { FC } from "react";
import { Navigate, Route, Routes } from "react-router-dom";
import { appRoutes, userRoutes } from "../routes";
import HistoricalPlayground from "../pages/HistoricalPlayground";
import Planner from "../pages/Planner";
import useGenericFiltersStore from "../stores/genericFiltersStore";
import useUserStore from "../stores/userStore";

const AdminRoutes: FC = () => {
  const { selectedTenant, selectedEntity } = useUserStore((state) => state);
  const { filtersData } = useGenericFiltersStore((state) => state);
  const { categoriesFilterData } = filtersData || {};
  const { categories } = categoriesFilterData || {};

  const basePath = `/${selectedTenant}/${selectedEntity}`;

  return (
    <Routes>
      <Route
        path="/"
        element={
          <Navigate to={`${basePath}${appRoutes.planner}`} />
        }
      />

      {/* hiding overall for now */}
      <Route
        path={`${basePath}${appRoutes.historicalPlayground}`}
        element={<HistoricalPlayground />}
      />

      {/* remove this route later */}
      {/* <Route
        path={`${basePath}${appRoutes.historicalPlayground}`}
        element={
          <Navigate
            to={`${basePath}${appRoutes.historicalPlayground}/${categories?.[0]?.id}`}
          />
        }
      /> */}

      {/* dynamic routes */}
      <Route
        path={`${basePath}${appRoutes.historicalPlaygroundCategory}`}
        element={<HistoricalPlayground />}
      />

      {/* ----------------------- ----------------------- ----------------------- ----------------------- ----------------------- ----------------------- ----------------------- */}

      {/* hiding overall for now */}
      {/* <Route path={appRoutes.planner} element={<Planner />} /> */}

      {/* remove this route later */}
      <Route
        path={`${basePath}${appRoutes.planner}`}
        element={
          <Navigate
            to={`${basePath}${appRoutes.planner}/${categories?.[0]?.id}`}
          />
        }
      />

      {/* dynamic routes */}
      <Route
        path={`${basePath}${appRoutes.plannerCategory}`}
        element={<Planner />}
      />

      <Route path="*" element={<Navigate to={userRoutes.pageNotFound} />} />
    </Routes>
  );
};

export default AdminRoutes;
