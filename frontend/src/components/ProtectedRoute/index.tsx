import { FC } from "react";
import { Navigate, useLocation } from "react-router-dom";

import useAuthStore from "../../stores/authStore";
import { userRoutes } from "../../routes";

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: FC<ProtectedRouteProps> = ({ children }) => {
  const location = useLocation();
  const { isAuthenticated } = useAuthStore((state) => state);

  // have an isAuthorised state in authStore > maybe after integrating openFGA, for page level or dashboard lvl access
  // if (!isAuthorised) {
  //   return (
  //     <Navigate
  //       to={userRoutes.accessDenied}
  //       state={{ from: location }}
  //       replace
  //     />
  //   );
  // }

  if (!isAuthenticated) {
    return (
      <Navigate to={userRoutes.login} state={{ from: location }} replace />
    );
  }

  return <>{children}</>;
};

export default ProtectedRoute;
