import { ReactNode } from "react";
import { DirtyField } from "../../stores/plannerStore";
import { Dayjs } from "dayjs";
import { modelChangeInterface } from "../../apis/forecast";

export interface MetricColumn {
  [key: string]: Record<string, string | number>;
}

export interface TableRecord {
  [key: string]: string | number | Element | Record<string, any>;
}

export type InputStatusTypes = "pending" | "approved" | "rejected";

export interface ApprovalInputsInterface {
  input_id: number;
  status: InputStatusTypes;
}

export interface CustomEditableTableProps {
  tableData: TableRecord[];
  cols: MetricColumn;
  indexCols: MetricColumn;
  approveInputs: (inputs: ApprovalInputsInterface[]) => void;
  models: Record<string, string>[];
  handleModelChange: (modelChange: modelChangeInterface) => void;
}

type Inputs = {
  value: number;
  updated_at: string | Date | Dayjs;
} | null;

export interface EditableCellInterface {
  primaryKey: string;
  dataIndex: string;
  title: string;
  record: TableRecord;
  index: number;
  children: ReactNode;
  isEditingTable: boolean;
  alreadyEditing: boolean;
  edit: (record: TableRecord, dataIndex: string) => void;
  save: (record: TableRecord, dataIndex: string) => Promise<void>;
  getDirtyField: (primaryKeyValue: string, dataIndex: string) => DirtyField;
  getOtherUserFieldValue: (
    primaryKeyValue: string,
    dataIndex: string
  ) => {
    approvedInputs: Inputs;
    processedInputs: Inputs;
    unprocessedInputs: Inputs;
  };
  isEditable: boolean;
  isAnApprover: boolean;
  selectedModels: Record<string, string>;
  cols: MetricColumn;
  models: Record<string, string>[];
  handleModelChange: (modelChange: modelChangeInterface) => void;
  expandedRowKeys: React.Key[];
  onTriggerRowExpand: (key: React.Key) => void;
  onTriggerRowCollapse: (key: React.Key) => void;
}

export interface ModelEditingInterface {
  primaryKey: string;
  alreadyEditing: boolean;
  dataIndex: string;
  record: TableRecord;
  edit: (record: TableRecord, dataIndex: string) => void;
  isAnApprover: boolean;
  selectedModel: string;
  cols: MetricColumn;
  models: Record<string, string>[];
  handleModelChange: (modelChange: modelChangeInterface) => void;
  restProps: any;
}
