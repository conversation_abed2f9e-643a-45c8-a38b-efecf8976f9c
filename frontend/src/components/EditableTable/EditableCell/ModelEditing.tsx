import { Select, Modal, Table } from "antd";
import { ModelEditingInterface } from "../types";

const { confirm } = Modal;

function ModelEditing({
  primaryKey,
  alreadyEditing,
  dataIndex,
  record,
  edit,
  isAnApprover,
  selectedModel,
  cols,
  models,
  handleModelChange,
  ...restProps
}: ModelEditingInterface) {
  if (!isAnApprover)
    return <td {...restProps}>{record[dataIndex] as string}</td>;

  const primaryKeyVal = record[primaryKey] as string;

  const currentModelName = models?.find(
    (model) => model.model_id === selectedModel
  )?.model_name;

  const onModelChange = (newModel: string) => {
    const metrics = Object.keys(record?.values?.[selectedModel]);

    const tableData = metrics.map((key) => {
      const baseValue = (
        record?.values?.[selectedModel] as unknown as Record<string, number>
      )?.[key as string];

      const compareValue = (
        record?.values?.[newModel] as unknown as Record<string, number>
      )?.[key as string];

      const diff = compareValue - baseValue;
      const diffPercent =
        baseValue === 0 ? "∞" : `${((diff / baseValue) * 100).toFixed(1)}%`;

      return {
        key,
        metric: `${cols[key]?.display_name} (${cols[key]?.metric_type})`,
        baseValue,
        compareValue,
        diff: `${diff > 0 ? "+" : ""}${diff} (${diffPercent})`,
      };
    });

    const columns = [
      { title: "Metric", dataIndex: "metric", key: "metric" },
      {
        title: currentModelName,
        dataIndex: "baseValue",
        key: "baseValue",
      },
      {
        title: models?.find((model) => model.model_id === newModel)?.model_name,
        dataIndex: "compareValue",
        key: "compareValue",
        render: (value: number) => (
          <span
            style={{
              background: "#e6f4ff",
              fontWeight: 500,
              padding: "0.25rem",
              borderRadius: "0.25rem",
            }}
          >
            {value}
          </span>
        ),
      },
      { title: "Change", dataIndex: "diff", key: "diff" },
    ];

    confirm({
      title: (
        <span>
          Do you want to change the model for{" "}
          <span className="text-blue-600">{primaryKeyVal}</span>?
        </span>
      ),
      icon: null,
      content: <Table columns={columns} dataSource={tableData} />,
      okText: "Change model",
      okType: "primary",
      cancelText: "Cancel",
      onOk() {
        handleModelChange({
          entity_value: primaryKeyVal,
          model_id: newModel,
        });
      },
      onCancel() {
        // console.log("Cancelled");
      },
      width: "80svh",
    });
  };

  const handleEdit = () => {
    if (alreadyEditing) return;
    edit(record, dataIndex);
  };

  return (
    <td
      {...restProps}
      onDoubleClick={handleEdit}
      className={alreadyEditing ? "!p-0 relative" : "p-2"}
    >
      {alreadyEditing ? (
        <Select
          value={selectedModel}
          onChange={onModelChange}
          options={models?.map((model) => ({
            value: model.model_id,
            label: model.model_name,
          }))}
          className="!b-0 h-full w-full model-alreadyEditing-select"
          style={{
            border: "none",
          }}
        />
      ) : (
        <>{currentModelName as string}</>
      )}
    </td>
  );
}

export default ModelEditing;
