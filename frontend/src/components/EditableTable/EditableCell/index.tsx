import { Collapse, Form, InputNumber } from "antd";
import usePlannerStore, { DirtyField } from "../../../stores/plannerStore";
import { EditableCellInterface } from "../types";
import CollapsePanel from "antd/es/collapse/CollapsePanel";
import "./styles.css";
import ModelEditing from "./ModelEditing";
import isEmpty from "lodash.isempty";

// todo: inputs/outputs will be reflected as it is. just need to make sure that for outputs we are only considering outputs with the model_id same as selectedModel for that entity

function EditableCell({
  primaryKey,
  dataIndex,
  title,
  record,
  children,
  alreadyEditing,
  edit, //edit cell
  save,
  getDirtyField,
  getOtherUserFieldValue,
  isEditable,
  isAnApprover,
  selectedModels,
  cols,
  models,
  handleModelChange,
  expandedRowKeys,
  onTriggerRowExpand,
  onTriggerRowCollapse,
  ...restProps
}: EditableCellInterface) {
  const { editTable: isEditingTable } = usePlannerStore((state) => state);

  if (!record || !isEditable) return <td {...restProps}>{children}</td>;

  const primaryKeyVal = record[primaryKey] as string;

  const selectedModel = selectedModels?.[primaryKeyVal] || "";

  if (dataIndex === "model_id") {
    return (
      <ModelEditing
        primaryKey={primaryKey}
        alreadyEditing={alreadyEditing}
        dataIndex={dataIndex}
        record={record}
        edit={edit}
        isAnApprover={isAnApprover}
        selectedModel={selectedModel}
        cols={cols}
        models={models}
        handleModelChange={handleModelChange}
        restProps={restProps}
      />
    );
  }

  const dirtyField = getDirtyField(
    primaryKeyVal as string,
    dataIndex
  ) as unknown as DirtyField;

  // const otherUserFieldValue
  const { approvedInputs, processedInputs, unprocessedInputs } =
    getOtherUserFieldValue(primaryKeyVal as string, dataIndex);

  const getCurrentValue = () => {
    return (
      dirtyField?.value ??
      unprocessedInputs?.value ??
      processedInputs?.value ??
      approvedInputs?.value ??
      record[dataIndex]
    );
  };

  const showUnprocessedInputs =
    unprocessedInputs?.value || unprocessedInputs?.value === 0;

  const showDirtyValue = dirtyField?.value || dirtyField?.value === 0;

  const renderCellValue = () => {
    if (dirtyField || approvedInputs || processedInputs) {
      const showApprovedValue =
        approvedInputs?.value || approvedInputs?.value === 0;

      const showProcessedInputs =
        processedInputs?.value || processedInputs?.value === 0;

      return (
        <div className="flex items-center justify-between w-full gap-2">
          <div className="font-medium">
            {record?.values?.[selectedModel]?.[dataIndex] as string}
          </div>

          {showApprovedValue && (
            <div className="text-green-600 bg-green-100 rounded-md py-0.5 px-2 text-xs font-medium shadow-sm">
              {approvedInputs?.value}
            </div>
          )}

          {showProcessedInputs && (
            <div className="text-blue-600 bg-blue-100 rounded-md py-0.5 px-2 text-xs font-medium shadow-sm">
              {processedInputs?.value}
            </div>
          )}
        </div>
      );
    }

    return (
      <>
        {
          (
            (record?.values as Record<string, string>)?.[
              selectedModel
            ] as Record<string, string>
          )?.[dataIndex] as string
        }
      </>
    );
  };

  const handleEdit = () => {
    if (alreadyEditing) return;
    edit(record, dataIndex);
  };

  const cellKey = `${primaryKeyVal}-${dataIndex}-cell`;

  return (
    <td
      {...restProps}
      onDoubleClick={handleEdit}
      className={alreadyEditing ? "!p-0 relative" : "p-2"}
      onClick={(e) => e.stopPropagation()}
    >
      {!(showDirtyValue || showUnprocessedInputs || alreadyEditing) ? (
        <>{renderCellValue()}</>
      ) : (
        <Collapse
          onChange={(e) => {
            if (isEmpty(e)) onTriggerRowCollapse?.(primaryKeyVal);
            else onTriggerRowExpand?.(primaryKeyVal);
          }}
          activeKey={expandedRowKeys.includes(primaryKeyVal) ? [cellKey] : []}
          expandIconPosition="end"
          className="h-full w-full absolute top-0 left-0 !b-0 editable-collapse z-1"
        >
          <CollapsePanel header={renderCellValue()} key={cellKey}>
            <div className="bg-white flex justify-between gap-2 items-center h-full flex-row-reverse flex-wrap">
              {/* unprocessed entry */}
              {showUnprocessedInputs && (
                <div className="text-blue-600 bg-blue-100 rounded-md py-0.5 px-2 text-xs font-medium shadow-sm">
                  {unprocessedInputs?.value}
                </div>
              )}

              {showDirtyValue && (
                <div className="text-black bg-gray-300 rounded-md py-0.5 px-2 text-xs font-medium shadow-sm">
                  {dirtyField?.value}
                </div>
              )}

              {isEditingTable && (
                <Form.Item
                  name={dataIndex}
                  style={{ margin: 0 }}
                  initialValue={getCurrentValue()}
                  rules={[{ required: true, message: `${title} is required.` }]}
                >
                  <InputNumber
                    onPressEnter={(e) => {
                      e.stopPropagation();
                      save(record, dataIndex);
                    }}
                    onBlur={(e) => {
                      e.stopPropagation();
                      save(record, dataIndex);
                    }}
                    // autoFocus //? on autofocus on, all inputs will get focused -> break ui
                  />
                </Form.Item>
              )}
            </div>
          </CollapsePanel>
        </Collapse>
      )}
    </td>
  );
}

export default EditableCell;
