.editable-collapse {
  border: none !important;
}

.ant-collapse {
  .ant-collapse-item {
    height: 100%;
    width: 100%;
    border-radius: 0 !important;
    /* box-shadow: inset 0px 0px 4px 0.5px #3e77ff; #cea9e2; */

    .ant-collapse-header {
      height: 100%;
      width: 100%;
      align-items: center !important;
      border: 1px solid #3e77ff;
      border-radius: 0 !important;
    }
  }

  .ant-collapse-item-active {
    .ant-collapse-header {
      height: 100%;
      width: 100%;
      align-items: center !important;
      border: 1px solid #3e77ff !important;
      border-bottom: none !important;
      /* box-shadow: inset inset 0px 3px 9px -3px blue; */
      border-radius: 0 !important;
    }

    .ant-collapse-content-active {
      border: 1px solid #3e77ff !important;
      border-top: none !important;
      /* box-shadow: inset 0px -4px 12px -6px blue; */
      border-radius: 0 !important;
    }
  }
}

.model-editing-select {
  .ant-select-selector {
    border: none !important;
    box-shadow: none !important;
  }
}
