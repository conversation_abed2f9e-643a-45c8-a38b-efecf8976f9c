import { Table, Form, Modal, Tabs, TabsProps } from "antd";
import type { ColumnType } from "antd/es/table";
import usePlannerStore from "../../stores/plannerStore";
import type { PlannerParams } from "../../stores/plannerStore";
import EditableCell from "./EditableCell";
import useGenericFiltersStore from "../../stores/genericFiltersStore";
import getForecastColumns from "../../utils/getForecastColumns";
import { useEffect, useState } from "react";
import {
  APPROVAL_EMAIL_IDS,
  CITY_STORE_CONSTANTS,
  TIME_GRAIN_OPTIONS,
} from "../../constants";
import startCase from "lodash.startcase";
import { CustomEditableTableProps, TableRecord } from "./types";
import DetailedGrainView from "../DetailedGrainView";
import { useParams } from "react-router-dom";
import useAuthStore from "../../stores/authStore";

function EditableTable({
  tableData,
  cols,
  indexCols,
  approveInputs,
  models,
  handleModelChange,
}: CustomEditableTableProps) {
  const { categoryId = "" } = useParams();

  const {
    timeGrain,
    params: appliedParams,
    editTable,
    editingCell,
    otherUserInputs,
    setEditingCell,
    setDirtyFields,
    getDirtyFieldsForParams,
  } = usePlannerStore((state) => state);
  const { filtersData } = useGenericFiltersStore((state) => state);
  const { user } = useAuthStore((state) => state);

  const [selectedModels, setSelectedModels] = useState<Record<string, string>>(
    {}
  );
  const [expandedRowKeys, setExpandedRowKeys] = useState<React.Key[]>([]);

  const plannerParams = appliedParams?.[categoryId] || {};

  const dashboardDirtyFields = getDirtyFieldsForParams(categoryId);

  const [open, setOpen] = useState(false);
  const [modalFilters, setModalFilters] = useState({});

  const [form] = Form.useForm();
  const primaryKey = Object.keys(indexCols)[0];

  const isAnApprover = APPROVAL_EMAIL_IDS?.includes(user?.email || "");

  useEffect(() => {
    //todo: upda'te the key for selected model

    const models = tableData?.reduce((acc, row) => {
      return {
        ...acc,
        [row[primaryKey] as string]: row.model_id,
      };
    }, {}) as Record<string, string>;

    setSelectedModels(models);
  }, [JSON.stringify(tableData)]);

  const columns = getForecastColumns({
    indexCols,
    cols,
    primaryKey,
    plannerParams,
    filtersData,
    editingCell,
    dashboardDirtyFields,
    otherUserInputs,
    setEditingCell,
    setDirtyFields,
    setOpen,
    setModalFilters,
    timeGrain,
    form,
    editTable,
    categoryId,
    isAnApprover,
    approveInputs,
    selectedModels,
    models,
    handleModelChange,
    expandedRowKeys,
    setExpandedRowKeys,
  });

  const detailedViewItems: TabsProps["items"] = [
    TIME_GRAIN_OPTIONS.daily,
    TIME_GRAIN_OPTIONS.hourly,
  ].map((key) => ({
    key,
    label: startCase(key),
    children: (
      <DetailedGrainView
        timeGrain={key}
        modalFilters={modalFilters as PlannerParams}
      />
    ),
  }));

  const getTitle = () => {
    const { city, store } = modalFilters || {};
    if (store === CITY_STORE_CONSTANTS.overall.value)
      return (
        <div>
          Detailed View for{" "}
          <span className="bg-blue-50 text-blue-600 px-2 py-1 rounded-md">
            {city}
          </span>
        </div>
      );

    const storeName = filtersData?.storesFilterData?.find(
      (s) => s.entity_id === store
    )?.entity_name;

    return (
      <div>
        Detailed View for{" "}
        <span className="bg-blue-50 text-blue-600 px-2 py-1 rounded-md">
          {city} - {storeName}
        </span>
      </div>
    );
  };

  return (
    <div>
      <Form form={form} component={false}>
        <Table
          expandable={{
            expandedRowKeys,
            onExpand: (expanded, record) => {
              const key = record[primaryKey] as string;
              setExpandedRowKeys(
                expanded
                  ? [...expandedRowKeys, key]
                  : expandedRowKeys.filter((k) => k !== key)
              );
            },
            // expandedRowRender: (record) => <div className="min-h-20 w-full" />,
            expandedRowRender: () => <div className="min-h-20 w-full" />,
          }}
          components={{
            body: {
              cell: EditableCell,
            },
          }}
          bordered
          dataSource={tableData}
          columns={columns as ColumnType<TableRecord>[]}
          rowKey={primaryKey}
          scroll={{ x: "max-content" }}
        />
      </Form>

      <Modal
        title={getTitle()}
        centered
        open={open}
        onOk={() => setOpen(false)}
        onCancel={() => setOpen(false)}
        width={"80%"}
        footer={null}
      >
        <Tabs
          defaultActiveKey={TIME_GRAIN_OPTIONS.daily}
          items={detailedViewItems}
        />
      </Modal>
    </div>
  );
}

export default EditableTable;
