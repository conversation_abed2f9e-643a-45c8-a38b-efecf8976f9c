import EditableTable from "../EditableTable";
import { useMutation, useQuery } from "@tanstack/react-query";
import {
  getDetailedForecast,
  getForecast,
  getForecastInputs,
  getForecastModels,
  InputsInterface,
  modelChangeInterface,
  postForecastInputs,
  updateEntityModel,
  updateInputStatus,
} from "../../apis/forecast";
import validateResponse from "../../utils/validateResponse";
import { Button, message, Spin, Tabs, TabsProps } from "antd";
import isEmpty from "lodash.isempty";
import usePlannerStore, { TimeGrain } from "../../stores/plannerStore";
import { AGGREGATION_GRAIN, TIME_GRAIN_OPTIONS } from "../../constants";
import startCase from "lodash.startcase";
import EmptyState from "../../common/EmptyState";
import ErrorState from "../../common/ErrorState";
import useQueryParams from "../../hooks/useQueryParams";
import dayjs, { Dayjs } from "dayjs";
import { useEffect, useMemo } from "react";
import { PlannerPlaygroundInterface } from "./types";
import { useParams } from "react-router-dom";
import useGenericFiltersStore from "../../stores/genericFiltersStore";
import { ApprovalInputsInterface } from "../EditableTable/types";
import { getClubbedInputFields } from "../../utils/getClubbedInputFields";

function PlannerPlayground({ params }: PlannerPlaygroundInterface) {
  const { categoryId = "" } = useParams();
  const [searchParams, , setQueryParams] = useQueryParams();

  const {
    params: plannerParams,
    timeGrain,
    editTable,
    setOtherUserInputs,
    setEditTable,
    resetDashboardDirtyFields,
    resetTableState,
    setTimeGrain,
    getDirtyFieldsForParams,
  } = usePlannerStore((state) => state);

  const { filtersData } = useGenericFiltersStore((state) => state);

  const categoryName = useMemo(
    () =>
      filtersData?.categoriesFilterData?.categories?.find(
        (c) => c.id === Number(categoryId)
      )?.name,
    [categoryId, filtersData?.categoriesFilterData]
  );

  const dashboardDirtyFields = getDirtyFieldsForParams(categoryId);

  const forecast_id = useMemo(() => {
    return filtersData?.forecastsFilterData?.forecasts?.[categoryName]?.id;
  }, [categoryName, filtersData?.forecastsFilterData?.forecasts]);

  const {
    data,
    isLoading,
    isError,
    refetch: refetchForecastData,
  } = useQuery({
    queryKey: ["getDetailedData", params, forecast_id],
    // queryKey: ["getDetailedData", params, timeGrain], //* removed timeGrain as in outer layer we are already using useEffect on searchParams, and on change of time grain we update the searchParams
    queryFn: async ({ signal }) => {
      const finalParams = {
        ...params,
        forecast_id,
        time_grain: timeGrain,
      };

      const fetchFunc =
        timeGrain !== TIME_GRAIN_OPTIONS.weekly
          ? getDetailedForecast
          : getForecast;

      const response = await fetchFunc(
        signal,
        categoryId,
        forecast_id,
        finalParams
      );
      return validateResponse(response);
    },
    enabled: !!params && !!timeGrain,
  });

  const {
    isLoading: isInputLoading,
    isError: isInputError,
    refetch: refetchForecastInputs,
  } = useQuery({
    queryKey: ["getForecastInputs", params, forecast_id],
    // queryKey: ["getForecastInputs", params, timeGrain], //* removed timeGrain as in outer layer we are already using useEffect on searchParams, and on change of time grain we update the searchParams
    queryFn: async ({ signal }) => {
      const finalParams = {
        ...params,
        forecast_id,
        time_grain: timeGrain,
      };

      setOtherUserInputs([]); //* reset on every call

      const response = await getForecastInputs(
        signal,
        categoryId,
        forecast_id,
        finalParams
      );
      const res = validateResponse(response);

      //? getClubbedInputFields is temporary : till we are not showing a separate approval page
      //* clubbing approved request together and pending requests together for a particular entity

      const fields = getClubbedInputFields(res.data?.data);
      setOtherUserInputs(fields);
      return res;
    },
    enabled: !!params && !!timeGrain,
  });

  const {
    data: modelData,
    isLoading: modelLoading,
    isError: modelError,
    refetch: refetchForecastModels,
  } = useQuery({
    queryKey: ["getForecastModels", params, forecast_id],
    queryFn: async ({ signal }) => {
      const response = await getForecastModels(signal, categoryId, forecast_id);
      return validateResponse(response);
    },
    enabled: !!params && !!timeGrain,
  });

  const { cols, idx_cols, values } = data?.data?.data || {};

  const { mutateAsync: postInputs, isPending } = useMutation({
    mutationFn: postForecastInputs,
    onSuccess: () => {
      resetTableState();
      refetch();
      message.success("Inputs sent successfully");
    },
    onError: (error) => {
      message.error(
        `Failed to send inputs: ${error?.message || "Unknown error"}`
      );
    },
  });

  const { mutateAsync: approveForecastInputs, isPending: isApprovalPending } =
    useMutation({
      mutationFn: updateInputStatus,
      onSuccess: () => {
        resetTableState();
        refetch();
        message.success("Inputs approved successfully");
      },
      onError: (error) => {
        message.error(
          `Failed to send inputs: ${error?.message || "Unknown error"}`
        );
      },
    });

  const { mutateAsync: changeEntityModel, isPending: isModelChangePending } =
    useMutation({
      mutationFn: updateEntityModel,
      onSuccess: () => {
        resetTableState();
        refetch();
        message.success("Inputs approved successfully");
      },
      onError: (error) => {
        message.error(
          `Failed to send inputs: ${error?.message || "Unknown error"}`
        );
      },
    });

  const refetch = () => {
    setOtherUserInputs([]);
    refetchForecastData();
    refetchForecastInputs();
    refetchForecastModels();
  };

  const handleTimeGrainChange = (key: string) => {
    setTimeGrain(key as TimeGrain);

    let params: Record<string, string | number | null | Dayjs> = {
      timeGrain: key,
    };

    if (key !== TIME_GRAIN_OPTIONS.hourly) {
      const date = dayjs(
        searchParams.get("date") || plannerParams?.[categoryId]?.date
      );
      params["date"] = dayjs(date)
        .startOf("week")
        .format("YYYY-MM-DD:HH:mm:ss");
    }

    if (!searchParams?.size) {
      params = {
        ...(plannerParams?.[categoryId] || {}),
        ...params,
      };
    }

    setQueryParams(params);
  };

  const handleSave = () => {
    const inputsObj = dashboardDirtyFields?.reduce((acc, field) => {
      const { single_aggregation, start_time, metric_name, time_grain, value } =
        field || {};
      const { value: entity_id, grain } = single_aggregation || {};

      if (Object.prototype.hasOwnProperty.call(acc, entity_id)) {
        acc[entity_id].values.push({ metric_name, value });
      } else {
        acc[entity_id] = {
          entity_id,
          start_time,
          time_grain,
          // status:'pending', // shouldn't be needed
          values: [{ metric_name, value }],
        };
      }

      return acc;
    }, {});

    const inputs = Object.values(inputsObj);

    postInputs({
      categoryId,
      forecast_id: Number(forecast_id),
      inputs: inputs as InputsInterface[],
    });
  };

  const approveInputs = (inputs: ApprovalInputsInterface[]) => {
    approveForecastInputs({
      categoryId,
      forecast_id: Number(forecast_id),
      inputs,
    });
  };

  const handleModelChange = (modelChange: modelChangeInterface) => {
    changeEntityModel({
      categoryId,
      forecast_id: Number(forecast_id),
      modelChange,
    });
  };

  const Children = () => {
    const loading = isLoading || isInputLoading || modelLoading;
    const error = isError || isInputError || modelError;

    if (loading) {
      return (
        <div className="flex justify-center items-center h-screen">
          <Spin size="large" />
        </div>
      );
    }

    if (error) {
      return (
        <div className="bg-white p-4 rounded-lg">
          <ErrorState height={300} width={300} />
        </div>
      );
    }

    if (isEmpty(values)) {
      return (
        <div className="bg-white p-4 rounded-lg">
          <EmptyState />
        </div>
      );
    }

    return (
      <EditableTable
        tableData={values}
        cols={cols}
        indexCols={idx_cols}
        approveInputs={approveInputs}
        models={modelData?.data?.data}
        handleModelChange={handleModelChange}
      />
    );
  };

  const Operations = () => {
    const isEntityView =
      params?.multi_aggregation_grain === AGGREGATION_GRAIN.ENTITY ||
      params?.single_aggregation_grain === AGGREGATION_GRAIN.ENTITY;

    if (timeGrain !== TIME_GRAIN_OPTIONS.weekly || !isEntityView) return null;

    const loading = isPending || isApprovalPending || isModelChangePending;

    return (
      <div className="flex justify-end w-full gap-4">
        {editTable ? (
          <>
            <Button
              onClick={() => resetDashboardDirtyFields(categoryId)}
              className="px-4 py-2 bg-blue-500 text-white rounded"
              loading={loading}
              disabled={loading}
            >
              {isEmpty(dashboardDirtyFields) ? "Cancel" : "Reset Changes"}
            </Button>

            <Button
              type="primary"
              onClick={handleSave}
              loading={loading}
              disabled={loading}
              className="px-4 py-2 bg-blue-500 text-white rounded"
            >
              Save Changes
            </Button>
          </>
        ) : (
          <Button
            onClick={() => setEditTable(true)}
            className="px-4 py-2 bg-blue-500 text-white rounded"
            loading={loading}
            disabled={loading}
          >
            Edit
          </Button>
        )}
      </div>
    );
  };

  const timeGrainItems: TabsProps["items"] = Object.entries(
    TIME_GRAIN_OPTIONS
  ).map(([key, value]) => ({
    key,
    label: startCase(value),
    children: <Children />,
  }));

  // todo: remove this, overhead..
  useEffect(() => {
    if (!timeGrain) setTimeGrain(TIME_GRAIN_OPTIONS.weekly);
  }, [setTimeGrain, timeGrain]);

  return (
    <div className="!mt-6">
      <Tabs
        defaultActiveKey={timeGrain || TIME_GRAIN_OPTIONS.weekly}
        items={timeGrainItems}
        tabBarExtraContent={<Operations />}
        onChange={handleTimeGrainChange}
        type="card"
      />
    </div>
  );
}

export default PlannerPlayground;
