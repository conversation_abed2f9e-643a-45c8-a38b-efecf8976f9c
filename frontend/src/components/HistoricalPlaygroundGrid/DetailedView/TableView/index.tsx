import { Spin, Table } from "antd";
import { getInstoreChartColumns } from "../../../../configurations/tableConfigs/InstoreChartColumns";
import validateResponse from "../../../../utils/validateResponse";
import { useQuery } from "@tanstack/react-query";
import useHistoricalPlaygroundStore from "../../../../stores/historicalPlaygroundStore";
import { getHistoricalPlaygroundParams } from "../../../../utils/getHistoricalPlaygroundParams";
import ErrorState from "../../../../common/ErrorState";
import { TIME_PERIOD_OPTIONS } from "../../../../constants";
import { ColumnType } from "antd/es/table";
import useGenericFiltersStore from "../../../../stores/genericFiltersStore";
import { ColumnInterface, TableViewInterface } from "../../types";
import { useParams } from "react-router-dom";
import { ParamsInterface } from "../../../../apis/stores";
import { getVizMetrics } from "../../../../apis/metrics";

function TableView({ metricKey }: TableViewInterface) {
  const { categoryId = "" } = useParams();

  const { filtersData } = useGenericFiltersStore((state) => state);
  const { params: historicalPlaygroundParams } = useHistoricalPlaygroundStore(
    (state) => state
  );

  const appliedParams = historicalPlaygroundParams?.[categoryId] || {};

  const { data, isLoading, isError } = useQuery({
    queryKey: ["getVizDetailedMetrics", appliedParams, categoryId],
    queryFn: async ({ signal }) => {
      const params =
        getHistoricalPlaygroundParams(appliedParams, filtersData) || {};

      const response = await getVizMetrics(signal, categoryId, {
        ...params,
        metric_list: [metricKey],
        detailed: true,
      } as unknown as ParamsInterface);

      return validateResponse(response);
    },
  });

  const columns = getInstoreChartColumns(appliedParams);

  const tableData =
    data?.data?.data?.current?.map(
      (currentItem: any, index: string | number) => {
        let itemData = {
          key: index,
          date: currentItem?.date,
          hour: currentItem?.hour,
          current_value: currentItem?.metrics?.[metricKey]?.metric_value || 0,
        };

        TIME_PERIOD_OPTIONS.forEach((item) => {
          if (appliedParams?.[item]) {
            const key = item?.toLowerCase();
            const value =
              data?.data?.data?.[key]?.[index]?.metrics?.[metricKey]
                ?.metric_value || 0;

            itemData = {
              ...itemData,
              [key]: value,
            };
          }
        });

        return itemData;
      }
    ) || [];

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-full">
        <Spin />
      </div>
    );
  }

  if (isError) {
    return <ErrorState />;
  }

  return (
    <div className="animate-fadeIn !relative z-0">
      <Table
        columns={columns as ColumnType<ColumnInterface>[]}
        dataSource={tableData}
        size="small"
        scroll={{ x: "max-content" }}
      />
    </div>
  );
}

export default TableView;
