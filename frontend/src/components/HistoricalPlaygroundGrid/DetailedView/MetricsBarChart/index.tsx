import { useMemo } from "react";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { Bar } from "react-chartjs-2";
import { COLOR_PALETTE, TIME_PERIOD_OPTIONS } from "../../../../constants";
import { MetricsBarChartProps } from "../../types";

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

const getColorFromHash = (str: string): string => {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    hash = str.charCodeAt(i) + ((hash << 5) - hash);
  }
  const index = Math.abs(hash) % COLOR_PALETTE.length;
  return COLOR_PALETTE[index];
};

function MetricsBarChart({ data, metricKey }: MetricsBarChartProps) {
  const rgbColor = useMemo(() => getColorFromHash(metricKey), [metricKey]);

  const chartData = {
    labels: [data?.current?.metrics?.[metricKey]?.name || ""],
    datasets: [
      ...Object.entries(data || {})
        .filter(([key]) => key !== "current")
        .sort((a, b) => {
          return (
            TIME_PERIOD_OPTIONS.indexOf(a?.[0]) -
            TIME_PERIOD_OPTIONS.indexOf(b?.[0])
          );
        })
        .reverse() // Reverse the order of comparison periods
        .map(([period], index, array) => {
          // Calculate opacity that decreases for older dates
          const baseOpacity = 0.85;
          const opacityStep = 0.15;
          const opacity = Math.max(
            baseOpacity - (Object.keys(data)?.length - index) * opacityStep,
            0.15
          );

          return {
            label: period.toUpperCase(),
            data: [data?.[period]?.metrics?.[metricKey]?.metric_value || 0],
            backgroundColor: `rgba(${rgbColor}, ${opacity})`,
            borderWidth: 1,
            borderRadius: 4,
            hoverBackgroundColor: `rgba(${rgbColor}, ${opacity + 0.15})`,
            barThickness: 40,
            period,
          };
        }),
      // Current period always shown last (rightmost)
      {
        label: "Current Period",
        data: [data?.current?.metrics?.[metricKey]?.metric_value || 0],
        backgroundColor: `rgba(${rgbColor}, 1)`, // full opacity for current
        borderWidth: 1,
        borderRadius: 4,
        hoverBackgroundColor: `rgba(${rgbColor}, 0.85)`,
        barThickness: 40,
        period: "current",
      },
    ]?.filter((item) => item.data?.[0] !== 0),
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    animation: {
      duration: 750,
      easing: "easeInOutQuart",
    },
    plugins: {
      legend: {
        position: "bottom" as const,
        padding: 20,
        labels: {
          font: {
            size: 13,
          },
          usePointStyle: true,
          pointStyle: "circle",
          padding: 20,
        },
      },
      tooltip: {
        titleColor: "#FFFFFF",
        bodyColor: "#efefef",
        borderWidth: 1,
        padding: 12,
        cornerRadius: 8,
        titleFont: {
          size: 14,
          weight: "500",
        },
        bodyFont: {
          size: 13,
        },
        displayColors: true,
        boxPadding: 2,
        callbacks: {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          title: (tooltipItems: any) => {
            const period = tooltipItems[0].dataset?.period || "";
            return [period.toUpperCase()];
          },
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          label: (context: any) => {
            const value = context.raw || 0;
            const period = context?.dataset?.period || "";
            const { start_time, end_time } = data?.[period]?.time_range || {};
            if (!start_time || !end_time)
              return [`Value: ${value.toLocaleString()}`];

            const startDate = new Date(start_time).toLocaleDateString();
            const endDate = new Date(end_time).toLocaleDateString();
            return [
              `Date: ${startDate} - ${endDate}`,
              `Value: ${value.toLocaleString()}`,
            ];
          },
        },
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          font: {
            size: 13,
            family: "'Okra', sans-serif",
          },
          maxRotation: 90,
          minRotation: 0,
          color: "#4b5563",
        },
      },
      y: {
        grid: {
          color: "rgba(243, 244, 246, 1)",
          drawBorder: false,
        },
        ticks: {
          font: {
            size: 13,
            family: "'Okra', sans-serif",
          },
          color: "#4b5563",
          padding: 8,
        },
        border: {
          dash: [4, 4],
        },
      },
    },
  };

  return (
    <div className="h-[400px] animate-fadeIn">
      <Bar data={chartData} options={options} />
    </div>
  );
}

export default MetricsBarChart;
