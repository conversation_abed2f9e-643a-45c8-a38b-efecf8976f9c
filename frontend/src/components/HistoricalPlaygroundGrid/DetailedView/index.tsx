import { useState } from "react";
import MetricsBarChart from "./MetricsBarChart";
import { Button, Tooltip } from "antd";
import { BarChartOutlined, TableOutlined } from "@ant-design/icons";
import TableView from "./TableView";
import { DetailedViewInterface } from "../types";

function DetailedView({ data, metricKey }: DetailedViewInterface) {
  const [tableView, setTableView] = useState<boolean>(false);

  return (
    <div className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-300 border border-gray-100">
      <div className="flex justify-between items-center !mb-6">
        <div className="">
          <h2 className="text-xl font-semibold text-gray-800 mb-1">
            {data?.current?.metrics?.[metricKey]?.display_name || metricKey}
          </h2>
          <p className="text-gray-500 text-sm tracking-wide">
            Metric Type: '
            {data?.current?.metrics?.[metricKey]?.metric_type || metricKey}'
          </p>
        </div>

        <Tooltip
          title={`Click to see ${tableView ? "Chart View" : "Table View"}`}
        >
          <Button
            icon={tableView ? <BarChartOutlined /> : <TableOutlined />}
            onClick={() => setTableView(!tableView)}
            type="text"
            className="!bg-gray-50 hover:!bg-gray-100"
          />
        </Tooltip>
      </div>

      {tableView ? (
        <TableView metricKey={metricKey} />
      ) : (
        <MetricsBarChart data={data} metricKey={metricKey} />
      )}
    </div>
  );
}

export default DetailedView;
