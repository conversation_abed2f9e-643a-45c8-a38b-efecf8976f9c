import { useQueries, useQuery } from "@tanstack/react-query";
import validateResponse from "../../utils/validateResponse";
import { Spin } from "antd";
import ErrorState from "../../common/ErrorState";
import isEmpty from "lodash.isempty";
import DetailedView from "./DetailedView";
import EmptyState from "../../common/EmptyState";
import { HistoricalPlaygroundGridInterface } from "./types";
import { ParamsInterface } from "../../apis/stores";
import { getVizMetrics } from "../../apis/metrics";
import { useParams } from "react-router-dom";
import useGenericFiltersStore from "../../stores/genericFiltersStore";

function HistoricalPlaygroundGrid({
  params,
}: HistoricalPlaygroundGridInterface) {
  const { categoryId = "" } = useParams();

  const { filtersData } = useGenericFiltersStore((state) => state);

  const { categories, ...restParams } = params || {};

  const categoriesToFetch = categories || [categoryId];

  const queries = categoriesToFetch?.map((category) => ({
    queryKey: ["getVizMetrics", params, category],
    queryFn: async ({ signal }: { signal: AbortSignal }) => {
      let queryParams: ParamsInterface = { ...restParams };

      // if category is selected from nav bar, then we need to filter the metrics based on that category
      if (categoryId) {
        queryParams = {
          ...queryParams,
          metric_list: restParams?.metric_list,
        };
      } else {
        // else provided in the url (category page is open). set metrics from params directly
        const categoryName =
          filtersData?.categoriesFilterData?.categories?.find(
            (c) => c.id === Number(category)
          )?.name;

        queryParams = {
          ...queryParams,
          metric_list: filtersData?.metricsFilterData?.categories?.[
            categoryName
          ]?.map((metric) => metric?.metric_name),
        };
      }

      const response = await getVizMetrics(signal, category?.toString(), {
        ...queryParams,
      } as ParamsInterface);

      return validateResponse(response);
    },
    enabled: !!params,
  }));

  const results = useQueries({ queries });
  const isLoading = results?.some((q) => q?.isLoading);
  const isError = results?.some((q) => q?.isError);
  const data = results
    ?.map((q) => ({
      metrics: q?.data?.config?.params?.metric_list,
      vizData: q?.data?.data?.data,
    }))
    ?.filter((vizData) => vizData?.metrics?.length);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Spin size="large" />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="!mt-4 bg-white p-4 rounded-lg">
        <ErrorState height={300} width={300} />
      </div>
    );
  }

  if (isEmpty(data)) {
    return (
      <div className="!mt-10 bg-white p-4 rounded-lg">
        <EmptyState />
      </div>
    );
  }

  return (
    <div className="!mt-4 grid grid-cols-3 gap-4">
      {data?.map(({ metrics, vizData }) => {
        return metrics?.map((metricName: string) => {
          if (
            !Object.prototype.hasOwnProperty.call(
              vizData?.current?.metrics,
              metricName
            )
          ) {
            return null;
          }
          return (
            <DetailedView
              key={metricName}
              data={vizData}
              metricKey={metricName}
            />
          );
        });
      })}
    </div>
  );
}

export default HistoricalPlaygroundGrid;
