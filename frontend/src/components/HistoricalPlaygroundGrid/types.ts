import { ParamsInterface } from "../../apis/stores";

export interface HistoricalPlaygroundGridInterface {
  params: ParamsInterface | null;
}

export interface DetailedViewInterface {
  data: any;
  metricKey: string;
}

export interface TableViewInterface {
  metricKey: string;
}

export interface ColumnInterface {
  [key: string]: string | number;
}

export interface MetricsBarChartProps {
  data: any;
  metricKey: string;
}
