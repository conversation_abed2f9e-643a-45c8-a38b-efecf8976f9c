import { Spin, Table } from "antd";
import type { ColumnType } from "antd/es/table";
import { TableRecord } from "../EditableTable/types";
import { Forecast, getDetailedForecast } from "../../apis/forecast";
import { useQuery } from "@tanstack/react-query";
import validateResponse from "../../utils/validateResponse";
import getForecastColumns from "../../utils/getForecastColumns";
import { PlannerParams } from "../../stores/plannerStore";
import { getPlannerParams } from "../../utils/getPlannerParams";
import { DetailedGrainViewInterface } from "./types";
import { useParams } from "react-router-dom";
import { useMemo } from "react";
import useGenericFiltersStore from "../../stores/genericFiltersStore";

function DetailedGrainView({
  timeGrain,
  modalFilters,
}: DetailedGrainViewInterface) {
  const { categoryId = "" } = useParams();
  const { filtersData } = useGenericFiltersStore((state) => state);

  const categoryName = useMemo(
    () =>
      filtersData?.categoriesFilterData?.categories?.find(
        (c) => c.id === Number(categoryId)
      )?.name,
    [categoryId, filtersData?.categoriesFilterData]
  );

  const forecast_id = useMemo(() => {
    return filtersData?.forecastsFilterData?.forecasts?.[categoryName]?.id;
  }, [categoryName, filtersData?.forecastsFilterData?.forecasts]);

  const { data, isLoading, isError } = useQuery({
    queryKey: ["getDetailedForecast", modalFilters, timeGrain],
    queryFn: async ({ signal }) => {
      const param = {
        ...modalFilters,
        forecast_id,
        timeGrain: timeGrain,
      };

      const plannerParams = getPlannerParams(
        param as PlannerParams,
        timeGrain
      ) as unknown as Forecast;

      const response = await getDetailedForecast(
        signal,
        categoryId,
        forecast_id,
        plannerParams
      );
      return validateResponse(response);
    },
  });

  if (isLoading)
    return (
      <div className="flex justify-center items-center h-screen">
        <Spin size="large" />
      </div>
    );

  if (isError) return <div>error occured</div>;

  const { cols, idx_cols, values } = data?.data?.data || {};
  const primaryKey = Object?.keys(idx_cols)?.[0] || "";

  const columns = getForecastColumns({
    indexCols: idx_cols,
    cols,
    primaryKey,
    plannerParams: {
      ...modalFilters,
    },
    timeGrain,

    // not used properties
    setOpen: () => {},
    setModalFilters: () => {},
    form: null,
    dashboardDirtyFields: [],
    otherUserInputs: [],
    setDirtyFields: () => {},
    setEditingCell: () => {},
  });

  return (
    <Table
      bordered
      dataSource={values}
      columns={columns as ColumnType<TableRecord>[]}
      rowKey={primaryKey}
      loading={isLoading}
      scroll={{ x: true }}
    />
  );
}

export default DetailedGrainView;
