import { FilterTwoTone } from "@ant-design/icons";
import { Button, Checkbox, Drawer, Form, Tooltip } from "antd";
import { useWatch } from "antd/es/form/Form";
import { useEffect, useMemo, useState } from "react";
import { CITY_STORE_CONSTANTS, TIME_PERIOD_OPTIONS } from "../../constants";
import useHistoricalPlaygroundStore from "../../stores/historicalPlaygroundStore";
import dayjs from "dayjs";
import AppliedFilters from "./AppliedFilters";
import useGenericFiltersStore from "../../stores/genericFiltersStore";
import { SearchableSelect } from "../../customElements/SearchableSelect";
import { CustomRangePicker } from "../../customElements/CustomRangePicker";
import { useParams, useSearchParams } from "react-router-dom";
import { FiltersInterface } from "./types";
import startCase from "lodash.startcase";

const MANDATORY_RULES = [{ required: true, message: "Required!" }];

function Filters({ form, handleSubmit }: FiltersInterface) {
  const [, setSearchParams] = useSearchParams();
  const { categoryId = "" } = useParams();

  const { filtersData } = useGenericFiltersStore((state) => state);
  const { params } = useHistoricalPlaygroundStore((state) => state);

  const [open, setOpen] = useState(false);

  const categoryName = useMemo(
    () =>
      filtersData?.categoriesFilterData?.categories?.find(
        (c) => c.id === Number(categoryId)
      )?.name,
    [categoryId, filtersData?.categoriesFilterData]
  );

  const eventId = useWatch(["event"], form);
  const formData = useWatch([], form);

  const { timeRange, ...rest } = params?.[categoryId] || {};

  const showDrawer = () => setOpen(true);
  const onClose = () => setOpen(false);

  const cityOptions = Array.from(
    new Map(
      filtersData?.storesFilterData?.map((item) => [
        item?.city_name,
        {
          label: item?.city_name,
          value: item?.city_name,
        },
      ])
    ).values()
  )?.sort((a, b) => (a.label as string).localeCompare(b.label as string));

  cityOptions.unshift(CITY_STORE_CONSTANTS.panIndia);

  const storeOptions = useMemo(() => {
    return filtersData?.storesFilterData
      ?.filter((store) => store?.city_name === formData?.city)
      ?.map((store) => ({
        label: store?.entity_name,
        value: store?.entity_id,
      }));
  }, [formData?.city, filtersData?.storesFilterData]);
  storeOptions.unshift(CITY_STORE_CONSTANTS.overall);

  const eventOptions = filtersData?.eventsFilterData.map((event) => ({
    label: event?.event_name,
    value: event?.event_id,
  }));

  const metricList = categoryName
    ? filtersData?.metricsFilterData?.categories?.[categoryName]
    : Object.values(filtersData?.metricsFilterData?.categories)?.flatMap(
        (category) => category
      );

  const metricOptions = metricList?.map((metric) => ({
    label: metric?.display_name,
    value: metric?.metric_name,
  }));

  const categoryOptions = filtersData?.categoriesFilterData?.categories?.map(
    (category) => ({
      label: startCase(category?.name),
      value: category?.id?.toString(),
    })
  );

  const initialValues = useMemo(
    () => ({
      ...rest,
      timeRange: timeRange
        ? [dayjs(timeRange?.[0] as string), dayjs(timeRange?.[1] as string)]
        : [],
      store: Number(rest?.store) ? Number(rest?.store) : rest?.store,
    }),
    [rest, timeRange]
  );

  const applyFilters = () => {
    setSearchParams(formData);
    onClose();
    handleSubmit();
  };

  useEffect(() => {
    if (eventId) {
      const eventInfo = filtersData?.eventsFilterData?.find(
        (e) => e?.event_id === eventId
      );

      form.setFieldsValue({
        timeRange: [
          dayjs(eventInfo?.event_dt as Date),
          dayjs(eventInfo?.event_dt as Date).endOf("day"),
        ],
      });
    }
  }, [eventId, filtersData?.eventsFilterData, form]);

  useEffect(() => {
    // also set form values. as they are already mounted. so they will show older values
    // doesn't matter if you use 'initialValues' as a useState state as antd form only shows mounted values, and needs to be updated using setFieldsValue directly
    form.setFieldsValue(initialValues);
  }, [JSON.stringify(initialValues)]);

  return (
    <div>
      <div className="flex justify-between items-center bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition-all duration-200">
        <AppliedFilters
          metricOptions={metricOptions}
          categoryOptions={categoryOptions}
        />

        <Tooltip title="Click to open filters">
          <Button
            type="primary"
            onClick={showDrawer}
            icon={<FilterTwoTone />}
            className="hover:opacity-90 transition-opacity ml-4"
          >
            Filters
          </Button>
        </Tooltip>
      </div>

      <Drawer
        title={false}
        onClose={onClose}
        open={open}
        styles={{ header: { display: "none" } }}
        footer={
          <div className="flex justify-end items-center">
            <Button
              onClick={applyFilters}
              type="primary"
              className="px-6 h-8"
              disabled={
                !formData?.store ||
                !formData?.timeRange?.length ||
                // (categoryId
                //   ? formData?.metric?.length
                //   : formData?.category?.length) ||
                JSON.stringify(params?.[categoryId]) ===
                  JSON.stringify(formData)
              }
            >
              Apply
            </Button>
          </div>
        }
      >
        <Form
          form={form}
          initialValues={initialValues}
          layout="vertical"
          className="gap-4"
        >
          <Form.Item name="city" label="City" rules={MANDATORY_RULES}>
            <SearchableSelect
              options={cityOptions}
              onChange={() =>
                form.setFieldValue("store", CITY_STORE_CONSTANTS.overall.value)
              }
            />
          </Form.Item>

          <Form.Item name="store" label="Store" rules={MANDATORY_RULES}>
            <SearchableSelect
              options={storeOptions}
              disable={
                !formData?.city ||
                formData.city === CITY_STORE_CONSTANTS.panIndia.value
              }
            />
          </Form.Item>

          <Form.Item name="event" label="Occasion">
            <SearchableSelect options={eventOptions} allowClear />
          </Form.Item>

          <Form.Item
            name="timeRange"
            label="Time Range"
            rules={MANDATORY_RULES}
          >
            <CustomRangePicker format="MMM D, YYYY HH" allowClear={true} />
          </Form.Item>

          {categoryId ? (
            <Form.Item name="metric" label="Metrics" rules={MANDATORY_RULES}>
              <SearchableSelect
                options={metricOptions}
                mode="multiple"
                allowClear={true}
              />
            </Form.Item>
          ) : (
            <Form.Item
              name="categories"
              label="Categories"
              rules={MANDATORY_RULES}
            >
              <SearchableSelect
                options={categoryOptions}
                mode="multiple"
                allowClear={true}
              />
            </Form.Item>
          )}

          <Form.Item name="comparison" label="Comparison >>>">
            <Checkbox.Group
              options={TIME_PERIOD_OPTIONS.map((option) => ({
                label: option,
                value: option?.toLowerCase(),
              }))}
            />
          </Form.Item>
        </Form>
      </Drawer>
    </div>
  );
}

export default Filters;
