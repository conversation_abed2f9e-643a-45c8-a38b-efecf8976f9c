import { Tag, Tooltip } from "antd";
import useHistoricalPlaygroundStore from "../../../stores/historicalPlaygroundStore";
import dayjs from "dayjs";
import { CITY_STORE_CONSTANTS, TIME_PERIOD_OPTIONS } from "../../../constants";
import useGenericFiltersStore from "../../../stores/genericFiltersStore";
import { AppliedFiltersInterface } from "../types";
import { useParams } from "react-router-dom";

function AppliedFilters({
  metricOptions,
  categoryOptions,
}: AppliedFiltersInterface) {
  const { categoryId = "" } = useParams();

  const { filtersData } = useGenericFiltersStore((state) => state);
  const { params } = useHistoricalPlaygroundStore((state) => state);

  const storesData = filtersData?.storesFilterData as Record<
    string,
    string | number
  >[];
  const eventsData = filtersData?.eventsFilterData as Record<string, string>[];

  const appliedParams = params?.[categoryId] || {};

  const { store = "", metric = [], categories = [] } = appliedParams || {};

  return (
    <div className="flex flex-wrap items-center gap-3">
      <div className="flex items-center gap-2">
        {appliedParams.city && (
          <Tag className="!px-3 !py-1.5 !rounded-md !border-gray-200 hover:!border-gray-300 transition-colors">
            <span className="text-gray-500 mr-1">City: </span>
            <span className="font-medium">
              {appliedParams.city === CITY_STORE_CONSTANTS.panIndia.value
                ? "Pan India"
                : appliedParams.city}
            </span>
          </Tag>
        )}

        {store && (
          <Tag className="!px-3 !py-1.5 !rounded-md !border-gray-200 hover:!border-gray-300 transition-colors">
            <span className="text-gray-500 mr-1">Store: </span>
            <span className="font-medium">
              {store === CITY_STORE_CONSTANTS.overall.value
                ? "Overall"
                : (storesData as Record<string, string | number>[])?.find(
                    (s) => s.entity_id === store
                  )?.entity_name}
            </span>
          </Tag>
        )}

        {appliedParams.event && (
          <Tag className="!px-3 !py-1.5 !rounded-md !border-gray-200 hover:!border-gray-300 transition-colors">
            <span className="text-gray-500 mr-1">Event: </span>
            <span className="font-medium">
              {
                eventsData?.find((e) => e.event_id === appliedParams.event)
                  ?.event_name
              }
            </span>
          </Tag>
        )}

        {appliedParams.timeRange &&
          Array.isArray(appliedParams.timeRange) &&
          appliedParams.timeRange.length > 0 && (
            <Tag className="!px-3 !py-1.5 !rounded-md !border-gray-200 hover:!border-gray-300 transition-colors">
              <span className="text-gray-500 mr-1">Time: </span>
              <span className="font-medium">
                {dayjs(appliedParams.timeRange[0] as unknown as string).format(
                  "MMM D, YYYY HH:mm"
                )}{" "}
                -{" "}
                {dayjs(appliedParams.timeRange[1] as unknown as string).format(
                  "MMM D, YYYY HH:mm"
                )}
              </span>
            </Tag>
          )}
      </div>

      {categories?.length > 0 && <div className="h-6 w-px bg-gray-200"></div>}

      {categories?.length > 0 && (
        <div className="flex items-center gap-2">
          <span className="text-gray-500 text-sm">Categories:</span>
          <div className="flex items-center gap-1">
            {categories?.slice(0, 3)?.map((categoryId: string) => (
              <Tag
                key={categoryId}
                className="!px-2 !py-1 !rounded-md !border-gray-200 !bg-blue-50 !text-blue-600"
              >
                {categoryOptions.find((m) => m.value === categoryId)?.label}
              </Tag>
            ))}
            {categories?.length > 3 && (
              <Tooltip
                title={
                  <div className="flex flex-col gap-1">
                    {categories?.slice(3)?.map((categoryId: string) => (
                      <div key={categoryId}>
                        {
                          categoryOptions.find((m) => m.value === categoryId)
                            ?.label
                        }
                      </div>
                    ))}
                  </div>
                }
              >
                <Tag className="!px-2 !py-1 !rounded-md !border-gray-200 !bg-blue-50 !text-blue-600 cursor-pointer">
                  +{categories.length - 3}
                </Tag>
              </Tooltip>
            )}
          </div>
        </div>
      )}

      {metric?.length > 0 && <div className="h-6 w-px bg-gray-200"></div>}

      {metric?.length > 0 && (
        <div className="flex items-center gap-2">
          <span className="text-gray-500 text-sm">Metrics:</span>
          <div className="flex items-center gap-1">
            {appliedParams.metric?.slice(0, 3).map((metric: string) => (
              <Tag
                key={metric}
                className="!px-2 !py-1 !rounded-md !border-gray-200 !bg-blue-50 !text-blue-600"
              >
                {metricOptions.find((m) => m.value === metric)?.label}
              </Tag>
            ))}
            {metric?.length > 3 && (
              <Tooltip
                title={
                  <div className="flex flex-col gap-1">
                    {metric.slice(3).map((metric: string) => (
                      <div key={metric}>
                        {metricOptions.find((m) => m.value === metric)?.label}
                      </div>
                    ))}
                  </div>
                }
              >
                <Tag className="!px-2 !py-1 !rounded-md !border-gray-200 !bg-blue-50 !text-blue-600 cursor-pointer">
                  +{metric.length - 3}
                </Tag>
              </Tooltip>
            )}
          </div>
        </div>
      )}

      {appliedParams.comparison?.length > 0 && (
        <div className="h-6 w-px bg-gray-200"></div>
      )}

      {appliedParams.comparison?.length > 0 && (
        <div className="flex items-center gap-2">
          <span className="text-gray-500 text-sm">Compare:</span>
          <div className="flex items-center gap-1">
            {appliedParams.comparison.slice(0, 3).map((option: string) => (
              <Tag
                key={option}
                className="!px-2 !py-1 !rounded-md !border-gray-200 !bg-purple-50 !text-purple-600"
              >
                {TIME_PERIOD_OPTIONS.find(
                  (item) => item.toLowerCase() === option
                ) || option}
              </Tag>
            ))}
            {appliedParams.comparison.length > 3 && (
              <Tooltip
                title={
                  <div className="flex flex-col gap-1">
                    {appliedParams.comparison.slice(3).map((option: string) => (
                      <div key={option}>
                        {TIME_PERIOD_OPTIONS.find(
                          (item) => item.toLowerCase() === option
                        ) || option}
                      </div>
                    ))}
                  </div>
                }
              >
                <Tag className="!px-2 !py-1 !rounded-md !border-gray-200 !bg-purple-50 !text-purple-600 cursor-pointer">
                  +{appliedParams.comparison.length - 3}
                </Tag>
              </Tooltip>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

export default AppliedFilters;
