import { getFormattedValue } from "../../../utils/getFormattedValue";
import { MetricItemInterface } from "../types";

function MetricItem({
  label,
  value,
  type,
  className = "",
}: MetricItemInterface) {
  return (
    <div className={className} style={{ borderRadius: "0.5rem" }}>
      <div className="flex items-center justify-between gap-3">
        <span className="text-[11px] text-gray-600 whitespace-nowrap">
          {label}
        </span>
        <div className="w-[1px] h-3 bg-gray-200"></div>
        <span className="text-xs font-semibold text-gray-800 ml-auto">
          {getFormattedValue(value, type)}
        </span>
      </div>
    </div>
  );
}

export default MetricItem;
