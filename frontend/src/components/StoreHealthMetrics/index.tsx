import { StockOutlined } from "@ant-design/icons";
import MetricItem from "./MetricItem";
import { useQuery } from "@tanstack/react-query";
import { ParamsInterface } from "../../apis/stores";
import validateResponse from "../../utils/validateResponse";
import isEmpty from "lodash.isempty";
import { STORE_HEALTH_CLASSES } from "../../constants";
import { StoreHealthMetricsInterface, MetricInterface } from "./types";
import { getStoreHealth } from "../../apis/healthMetrics";

function StoreHealthMetrics({ params }: StoreHealthMetricsInterface) {
  const { data, isLoading, isError } = useQuery({
    queryKey: ["getStoreHealth", params],
    queryFn: async ({ signal }) => {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { metric_list, ...rest } = params;
      const response = await getStoreHealth(signal, {
        ...rest,
      } as ParamsInterface);

      return validateResponse(response);
    },
  });

  // const workforceMetrics: MetricData[] = [
  //   { label: "Approved HC", value: data?.data?.approved_hc || 0 },
  //   { label: "Current Active HC", value: data?.data?.current_active_hc || 0 },
  //   { label: "Absenteeism", value: data?.data?.absenteeism || 0 },
  //   { label: "FSS L7", value: data?.data?.fss_l7 || 0 },
  //   { label: "Churn LW", value: data?.data?.churn_lw || 0 },
  //   { label: "Referral Leads", value: data?.data?.referral_leads || 0 },
  //   { label: "Self OB Leads", value: data?.data?.self_ob_leads || 0 },
  // ];

  if (isLoading || isError || isEmpty(data)) return null;

  const storeHealthData = data?.data?.data;
  if (isEmpty(storeHealthData)) return null;

  return (
    <div className="shadow-md">
      {/* <div className="px-6 py-4 bg-blue-100/80">
        <div className="flex gap-3">
          {workforceMetrics.map((metric) => (
            <MetricItem
              key={metric.label}
              label={metric.label}
              value={metric.value}
              className="bg-white/95 rounded px-3 py-1 shadow-sm hover:bg-white transition-colors duration-200 border border-blue-200 min-w-[130px]"
            />
          ))}
        </div>
      </div> */}

      <div
        className={`px-6 py-4 ${
          STORE_HEALTH_CLASSES[storeHealthData?.store_health]?.bg
        }`}
      >
        <div className="flex gap-4 items-center">
          <div className="flex items-center gap-2 bg-white/95 rounded px-3 py-1 shadow-sm border border-gray-300">
            <StockOutlined className="text-gray-700 text-xs" />
            <span className="text-gray-800 text-xs font-medium">
              Store Health
            </span>
          </div>

          <div className="flex gap-2">
            <span
              className={`px-2.5 py-1.5 rounded shadow-sm ${
                STORE_HEALTH_CLASSES?.[storeHealthData?.store_health]?.btnBg
              } min-w-[70px] text-center text-xs`}
            >
              {storeHealthData?.store_health?.toUpperCase()}
            </span>
          </div>

          <div className="flex gap-3 ml-auto">
            {Object.entries(
              storeHealthData as Record<string, MetricInterface>
            )?.map(([key, metric]: [string, MetricInterface]) => {
              if (key === "store_health") return null;
              return (
                <MetricItem
                  key={metric.metric_name}
                  label={metric.display_name}
                  value={metric.metric_value}
                  type={metric.metric_type}
                  className="bg-white/95 rounded px-3 py-1 shadow-sm hover:bg-white transition-colors duration-200 border border-gray-300 min-w-[130px]"
                />
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}

export default StoreHealthMetrics;
