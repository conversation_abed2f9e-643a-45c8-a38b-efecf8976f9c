import { FilterTwoTone } from "@ant-design/icons";
import {
  <PERSON><PERSON>,
  DatePicker,
  DatePickerProps,
  Drawer,
  Form,
  Tooltip,
} from "antd";
import { useWatch } from "antd/es/form/Form";
import { useEffect, useMemo, useState } from "react";
import { CITY_STORE_CONSTANTS, TIME_GRAIN_OPTIONS } from "../../constants";
import dayjs from "dayjs";
import AppliedFilters from "./AppliedFilters";
import useGenericFiltersStore from "../../stores/genericFiltersStore";
import { SearchableSelect } from "../../customElements/SearchableSelect";
import usePlannerStore from "../../stores/plannerStore";
import { useParams, useSearchParams } from "react-router-dom";
import { FiltersInterface } from "./types";

const WEEK_FORMAT = "MMM D, YYYY";
const MANDATORY_RULES = [{ required: true, message: "Required!" }];

function PlannerFilters({ form, handleSubmit }: FiltersInterface) {
  const [, setSearchParams] = useSearchParams();
  const { categoryId = "" } = useParams();

  const { filtersData } = useGenericFiltersStore((state) => state);
  const { params, timeGrain } = usePlannerStore((state) => state);

  const appliedParams = params?.[categoryId] || {};

  const formData = useWatch([], form);

  const [open, setOpen] = useState(false);

  const showDrawer = () => setOpen(true);
  const onClose = () => setOpen(false);

  const cityOptions = useMemo(() => {
    const filterValue = Array.from(
      new Map(
        filtersData?.storesFilterData?.map((item: Record<string, string>) => [
          item?.city_name,
          {
            label: item?.city_name,
            value: item?.city_name,
          },
        ])
      ).values()
    )?.sort((a, b) => a.label.localeCompare(b.label));
    filterValue?.unshift(CITY_STORE_CONSTANTS.panIndia);
    return filterValue;
  }, [filtersData?.storesFilterData]);

  const storeOptions = filtersData?.storesFilterData
    ?.filter((store) => store?.city_name === formData?.city)
    ?.map((store) => ({
      label: store?.entity_name,
      value: store?.entity_id,
    }));
  storeOptions?.unshift(CITY_STORE_CONSTANTS.overall);

  const initialValues = useMemo(
    () => ({
      ...appliedParams,
      //* when it gets appended to url, it becomes a string
      date: dayjs(appliedParams?.date),
      store: Number(appliedParams?.store)
        ? Number(appliedParams?.store)
        : appliedParams?.store,
    }),
    [appliedParams]
  );

  const customWeekStartEndFormat: DatePickerProps["format"] = (value) => {
    return `${dayjs(value).startOf("week").format(WEEK_FORMAT)} ~ ${dayjs(value)
      .endOf("week")
      .format(WEEK_FORMAT)}`;
  };

  const formValues = useWatch([], form);

  const applyFilters = () => {
    const searchParamValues = {
      ...formValues,
      timeGrain: timeGrain,
    };

    setSearchParams(searchParamValues);
    onClose();
    handleSubmit();
  };

  // cutom handling to change the date format based on the timeGrain
  useEffect(() => {
    if (timeGrain !== TIME_GRAIN_OPTIONS.hourly) {
      form.setFieldValue("date", dayjs(formValues?.date).startOf("week"));
    }
  }, [form, formValues, timeGrain]);

  useEffect(() => {
    // also set form values. as they are already mounted. so they will show older values
    // doesn't matter if you use 'initialValues' as a useState state as antd form only shows mounted values, and needs to be updated using setFieldsValue directly
    form.setFieldsValue(initialValues);
  }, [JSON.stringify(initialValues)]);

  return (
    <div>
      <div className="flex justify-between items-center bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition-all duration-200">
        <AppliedFilters timeGrain={timeGrain} />

        <Tooltip title="Click to open filters">
          <Button
            type="primary"
            onClick={showDrawer}
            icon={<FilterTwoTone />}
            className="hover:opacity-90 transition-opacity ml-4"
          >
            Filters
          </Button>
        </Tooltip>
      </div>

      <Drawer
        title={false}
        onClose={onClose}
        open={open}
        styles={{ header: { display: "none" } }}
        footer={
          <div className="flex justify-end items-center">
            <Button
              onClick={applyFilters}
              type="primary"
              className="px-6 h-8"
              disabled={
                !formData?.store ||
                JSON.stringify(appliedParams) === JSON.stringify(formData)
              }
            >
              Apply
            </Button>
          </div>
        }
      >
        <Form
          form={form}
          initialValues={initialValues}
          layout="vertical"
          className="gap-4"
        >
          <Form.Item name="city" label="City" rules={MANDATORY_RULES}>
            <SearchableSelect
              options={cityOptions}
              onChange={() => {
                form.setFieldValue("store", CITY_STORE_CONSTANTS.overall.value);
              }}
            />
          </Form.Item>

          <Form.Item name="store" label="Store" rules={MANDATORY_RULES}>
            <SearchableSelect
              options={storeOptions}
              disable={
                !formData?.city ||
                formData.city === CITY_STORE_CONSTANTS.panIndia.value
              }
            />
          </Form.Item>

          <Form.Item name="date" label="Date" rules={MANDATORY_RULES}>
            {/* move this whole into a separate Element/controller for config > also have the custom onChange in the element itself, and just take the name */}
            {timeGrain === TIME_GRAIN_OPTIONS.hourly ? (
              <DatePicker
                format={WEEK_FORMAT}
                allowClear={true}
                className="w-full"
                onChange={(date) => {
                  form.setFieldValue("date", dayjs(date));
                }}
              />
            ) : (
              <DatePicker.WeekPicker
                format={customWeekStartEndFormat}
                allowClear={true}
                className="w-full"
                onChange={(date) => {
                  form.setFieldValue("date", dayjs(date).startOf("week"));
                }}
              />
            )}
          </Form.Item>
        </Form>
      </Drawer>
    </div>
  );
}

export default PlannerFilters;
