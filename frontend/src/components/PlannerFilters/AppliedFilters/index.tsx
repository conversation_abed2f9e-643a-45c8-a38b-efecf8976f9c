import { Tag } from "antd";
import dayjs from "dayjs";
import { CITY_STORE_CONSTANTS, TIME_GRAIN_OPTIONS } from "../../../constants";
import useGenericFiltersStore from "../../../stores/genericFiltersStore";
import usePlannerStore from "../../../stores/plannerStore";
import { AppliedFiltersInterface } from "../types";
import { useParams } from "react-router-dom";

function AppliedFilters({ timeGrain }: AppliedFiltersInterface) {
  const { categoryId = "" } = useParams();

  const { filtersData } = useGenericFiltersStore((state) => state);
  const { params } = usePlannerStore((state) => state);

  const storesData = filtersData?.storesFilterData as Record<
    string,
    string | number
  >[];

  const appliedParams = params?.[categoryId] || {};

  const { store = "" } = appliedParams || {};

  return (
    <div className="flex flex-wrap items-center gap-3">
      <div className="flex items-center gap-2">
        {appliedParams.city && (
          <Tag className="!px-3 !py-1.5 !rounded-md !border-gray-200 hover:!border-gray-300 transition-colors">
            <span className="text-gray-500 mr-1">City: </span>
            <span className="font-medium">
              {appliedParams.city === CITY_STORE_CONSTANTS.panIndia.value
                ? "Pan India"
                : appliedParams.city}
            </span>
          </Tag>
        )}

        {appliedParams.store && (
          <Tag className="!px-3 !py-1.5 !rounded-md !border-gray-200 hover:!border-gray-300 transition-colors">
            <span className="text-gray-500 mr-1">Store: </span>
            <span className="font-medium">
              {store === CITY_STORE_CONSTANTS.overall.value
                ? "Overall"
                : storesData?.find((s) => s.entity_id?.toString() === store)
                    ?.entity_name}
            </span>
          </Tag>
        )}

        {appliedParams.date && (
          <Tag className="!px-3 !py-1.5 !rounded-md !border-gray-200 hover:!border-gray-300 transition-colors">
            <span className="text-gray-500 mr-1">Date: </span>
            <span className="font-medium">
              {timeGrain === TIME_GRAIN_OPTIONS.hourly
                ? dayjs(appliedParams.date).format("MMM D, YYYY")
                : `${dayjs(appliedParams.date)
                    .startOf("week")
                    .format("MMM D, YYYY")} ~ ${dayjs(appliedParams.date)
                    .endOf("week")
                    .format("MMM D, YYYY")}`}
            </span>
          </Tag>
        )}
      </div>
    </div>
  );
}

export default AppliedFilters;
