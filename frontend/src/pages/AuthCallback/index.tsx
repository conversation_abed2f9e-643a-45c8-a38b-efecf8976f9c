import { FC, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { validateUser } from "../../apis/auth";
import { Spin } from "antd";
import validateResponse from "../../utils/validateResponse";
import useAuthStore from "../../stores/authStore";

const AuthCallback: FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const { setAuth } = useAuthStore((state) => state);

  useEffect(() => {
    const handleCallback = async () => {
      const code = searchParams.get("code");

      if (!code) {
        navigate("/login");
        return;
      }

      try {
        const validateUserResponse = await validateUser({ code });
        const response = validateResponse(validateUserResponse);
        setAuth(response.data.result.user);
        navigate("/");
      } catch (error) {
        console.error("Auth callback error:", error);
        navigate("/403");
      }
    };

    handleCallback();
  }, []);

  return (
    <div className="w-screen h-screen flex items-center justify-center">
      <Spin size="large" />
    </div>
  );
};

export default AuthCallback;
