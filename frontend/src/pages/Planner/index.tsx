import { useForm } from "antd/es/form/Form";
import { useEffect, useState } from "react";
import { getPlannerParams } from "../../utils/getPlannerParams";
import { message } from "antd";
import Header from "../../common/Header";
import usePlannerStore, { TimeGrain } from "../../stores/plannerStore";
import PlannerPlayground from "../../components/PlannerPlayground";
import PlannerFilters from "../../components/PlannerFilters";
import { useParams, useSearchParams } from "react-router-dom";
import { Forecast } from "../../apis/forecast";
import parseAndSetUrlParams from "../../utils/parseAndSetUrlParams";

function Planner() {
  const [searchParams] = useSearchParams();
  const { categoryId = "" } = useParams();

  const TimeGrain = searchParams.get("timeGrain") as TimeGrain;

  const {
    params: plannerParams,
    setParams: setPlannerParams,
    timeGrain,
    setTimeGrain,
  } = usePlannerStore((state) => state);

  const appliedParams = plannerParams?.[categoryId] || {};

  const [params, setParams] = useState<Forecast | null | undefined>();

  const [form] = useForm();

  const applyFilters = async () => {
    try {
      const values = await form.validateFields();
      setPlannerParams(categoryId, values);

      const finalParams = getPlannerParams(values, timeGrain);
      setParams(finalParams as unknown as Forecast);
    } catch (e) {
      message.error(e);
    }
  };

  useEffect(() => {
    try {
      setTimeGrain(TimeGrain);
      parseAndSetUrlParams(searchParams, setPlannerParams, categoryId);

      const finalParams = getPlannerParams(
        appliedParams,
        timeGrain as TimeGrain
      );
      setParams(finalParams as unknown as Forecast);
    } catch (e) {
      message.error(e);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(appliedParams), searchParams, setPlannerParams]);

  return (
    <div className="w-full min-h-screen relative flex flex-col">
      <Header
        title="Store Ops Planning"
        subTitle="Plan your store capacity based on historical data"
      />

      <div className="p-4 flex-1">
        <PlannerFilters form={form} handleSubmit={applyFilters} />

        <PlannerPlayground params={params as unknown as Forecast} />
      </div>
    </div>
  );
}

export default Planner;
