import { Typography } from "antd";
import { useNavigate } from "react-router-dom";
import accessDenied from "../../assets/access-forbidden.webp";
import { userRoutes } from "../../routes";

const { Title, Text } = Typography;

function AccessDenied() {
  const navigate = useNavigate();

  const handleBack = (): void => {
    navigate(-1);
  };

  const handleLogin = (): void => {
    navigate(userRoutes.login);
  };

  return (
    <div className="w-screen h-screen flex flex-col items-center justify-center p-4">
      <div className="text-center max-w-4xl !mx-auto tracking-wider">
        <div className="!mb-2">
          <Title
            level={1}
            className="!m-0 pb-3 font-bold"
            style={{ color: "#1677ff", fontWeight: 800 }}
          >
            403
          </Title>
          <Title
            level={2}
            className="!m-0 pb-16"
            style={{ color: "#1677ff", letterSpacing: 3, fontWeight: 400 }}
          >
            Access Denied
          </Title>
          <Text className="text-gray-500 text-lg block !mb-2 tracking-wider">
            Sorry, but you don't have permission to access this page.
          </Text>
          <Text className="text-gray-500 text-lg block tracking-wider">
            You can go back to <a onClick={handleBack}>previous page</a> or try{" "}
            <a onClick={handleLogin}>login</a> again.
          </Text>
        </div>

        <img
          src={accessDenied}
          alt="Access Denied Illustration"
          className="w-full max-w-sm !mx-auto animate-fadeIn"
        />
      </div>
    </div>
  );
}

export default AccessDenied;
