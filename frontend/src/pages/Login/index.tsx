import { FC } from "react";
import { <PERSON><PERSON>, message } from "antd";
import GoogleIcon from "../../common/GoogleIcon";
import { useMutation } from "@tanstack/react-query";
import { login } from "../../apis/auth";
import loginBg from "../../assets/login-bg.png";

const Login: FC = () => {
  const { mutateAsync: handleGoogleLogin, isPending } = useMutation({
    mutationFn: login,
    onSuccess: (response) => {
      const redirect_url = response?.data?.redirect_url;
      if (redirect_url) window.location.replace(redirect_url);
    },
    onError: (error) => {
      // or not authorised...
      message.error(
        `Failed to add Sign in: ${error?.message || "Unknown error"}`
      );
    },
  });

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 !h-screen">
      <div className="hidden lg:block relative">
        <div
          className="absolute inset-0 bg-cover bg-center"
          style={{
            backgroundImage: `url(${loginBg})`,
            backgroundSize: "cover",
            backgroundPosition: "center",
          }}
        />

        {/* Overlay for better text visibility */}
        <div className="absolute inset-0 bg-black" style={{ opacity: "0.2" }} />

        {/* Logo */}
        <div className="absolute bottom-8 left-8 text-white text-2xl font-bold tracking-wider">
          <div
            className="tracking-widest"
            style={{ textShadow: "1px 1px 4px rgba(0, 0, 0, 0.4)" }}
          >
            Plan<span className="text-pink-300">It</span>
          </div>
          <div
            className="text-lg font-normal"
            style={{ textShadow: "1px 1px 4px rgba(0, 0, 0, 0.4)" }}
          >
            by Blinkit
          </div>
        </div>
      </div>

      {/* Right Side - Login Form */}
      <div className="flex items-center justify-center px-8 py-12 bg-gray-50 !h-screen">
        <div className="w-full max-w-md space-y-8">
          <div className="bg-white rounded-2xl shadow-xl p-8">
            <div className="text-center space-y-3">
              <h2 className="text-3xl font-extrabold text-gray-900">
                Welcome Back
              </h2>
              <p className="text-gray-500">
                Sign in to your account to continue
              </p>
            </div>

            <div className="!mt-8">
              <Button
                className="w-full rounded-xl shadow-sm hover:shadow-md flex items-center justify-center"
                onClick={() => handleGoogleLogin()}
                loading={isPending}
                size="large"
                icon={<GoogleIcon />}
              >
                {isPending ? "Connecting..." : "Continue with Google"}
              </Button>
            </div>

            <div className="!mt-8 text-center">
              <p className="text-sm text-gray-500">
                By signing in, you agree to our{" "}
                <a
                  href="#"
                  className="font-medium text-blue-600 hover:text-blue-500 
                           transition-colors duration-200"
                >
                  Terms of Service
                </a>{" "}
                and{" "}
                <a
                  href="#"
                  className="font-medium text-blue-600 hover:text-blue-500 
                           transition-colors duration-200"
                >
                  Privacy Policy
                </a>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
