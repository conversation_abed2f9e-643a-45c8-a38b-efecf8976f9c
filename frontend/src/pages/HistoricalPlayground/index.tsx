import { useForm } from "antd/es/form/Form";
import Filters from "../../components/Filters";
import { useEffect, useState } from "react";
import { getHistoricalPlaygroundParams } from "../../utils/getHistoricalPlaygroundParams";
import { ParamsInterface } from "../../apis/stores";
import { message } from "antd";
import useHistoricalPlaygroundStore from "../../stores/historicalPlaygroundStore";
import Header from "../../common/Header";
import StoreHealthMetrics from "../../components/StoreHealthMetrics";
import HistoricalPlaygroundGrid from "../../components/HistoricalPlaygroundGrid";
import useGenericFiltersStore from "../../stores/genericFiltersStore";
import { useParams, useSearchParams } from "react-router-dom";
import parseAndSetUrlParams from "../../utils/parseAndSetUrlParams";

function HistoricalPlayground() {
  const [searchParams] = useSearchParams();
  const { categoryId = "" } = useParams();

  const { filtersData } = useGenericFiltersStore((state) => state);
  const {
    params: historicalPlaygroundParams,
    setParams: setHistoricalPlaygroundParams,
  } = useHistoricalPlaygroundStore((state) => state);

  const appliedParams = historicalPlaygroundParams?.[categoryId] || {};

  const [params, setParams] = useState<ParamsInterface | null | undefined>();

  const [form] = useForm();

  const applyFilters = async () => {
    try {
      const values = await form.validateFields();
      setHistoricalPlaygroundParams(categoryId, values);

      const finalParams = getHistoricalPlaygroundParams(values, filtersData);
      setParams(finalParams as unknown as ParamsInterface);
    } catch (e) {
      message.error(e);
    }
  };

  useEffect(() => {
    try {
      parseAndSetUrlParams(
        searchParams,
        setHistoricalPlaygroundParams,
        categoryId
      );

      const finalParams = getHistoricalPlaygroundParams(
        appliedParams,
        filtersData
      );
      setParams(finalParams as unknown as ParamsInterface);
    } catch (e) {
      message.error(e);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    JSON.stringify(appliedParams),
    searchParams,
    setHistoricalPlaygroundParams,
  ]);

  return (
    <div className="w-full min-h-screen relative flex flex-col">
      <Header
        title="Store Ops Analytics"
        subTitle="Monitor and analyze your store performance metrics"
      />

      <div className="p-4 flex-1">
        <Filters form={form} handleSubmit={applyFilters} />
        <HistoricalPlaygroundGrid params={params as ParamsInterface} />
      </div>

      <div className="sticky bottom-0 left-0 w-full">
        <StoreHealthMetrics params={params as ParamsInterface} />
      </div>
    </div>
  );
}

export default HistoricalPlayground;
