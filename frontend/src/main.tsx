import { createRoot } from "react-dom/client";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { setupGlobalInterceptors } from "./hooks/interceptors";
import dayjs from "dayjs";
import { ConfigProvider } from "antd";
import "dayjs/locale/en-gb"; // 'en-gb' locale starts week on Monday
import locale from "antd/locale/en_GB";
import App from "./App";
import "./index.css";

dayjs.locale("en-gb"); // Set dayjs locale globally

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false, // Disable retries for all queries
      refetchOnWindowFocus: false, // Disable refetch on window focus
    },
  },
});

// Set up global axios interceptors
setupGlobalInterceptors();

createRoot(document.getElementById("root")!).render(
  <QueryClientProvider client={queryClient}>
    <ConfigProvider locale={locale}>
      <App />
    </ConfigProvider>
  </QueryClientProvider>
);
