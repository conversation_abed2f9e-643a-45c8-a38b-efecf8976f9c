.sidebar {
  min-height: 100vh;
  position: fixed;
  inset-inline-start: 0;
  top: 0;
  bottom: 0;
  background-color: #ffffff;
  border-right: 0.5px solid #d0d4dc;
}

.sidebar-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.sidebar-top {
  flex: 1;
  overflow-y: auto;
}

.sidebar-bottom {
  margin-top: auto;
  border-top: 0.5px solid #d0d4dc;
}

.content-container {
  background-color: #f5f6fb;
  margin-left: 240px;
  transition: margin-left 0.2s;
}

:where(.ant-layout-sider-collapsed) + .ant-layout .content-container {
  margin-left: 80px;
}

.tenant-header-container {
  border-bottom: 0.5px solid #d0d4dc;
}
