import { <PERSON><PERSON>, Button, Dropdown, MenuProps, message } from "antd";
import {
  LogoutOutlined,
  MoreOutlined,
  SettingOutlined,
  UserOutlined,
} from "@ant-design/icons";
import { useMutation } from "@tanstack/react-query";
import useAuthStore from "../../../stores/authStore";
import { logout } from "../../../apis/auth";
import { useNavigate } from "react-router-dom";
import { userRoutes } from "../../../routes";
import { UserProfileProps } from "../types";

function UserProfile({ collapsed }: UserProfileProps) {
  const navigate = useNavigate();

  const { user, clearAuth } = useAuthStore((state) => state);

  const { mutateAsync: logoutUser, isPending } = useMutation({
    mutationFn: logout,
    onSuccess: () => {
      navigate(userRoutes.login);

      setTimeout(() => {
        // reset all stores
        localStorage.clear();

        clearAuth();
        window.location.reload();
      }, 0);
    },
    onError: (error) => {
      message.error(`Failed to logout: ${error?.message || "Unknown error"}`);
    },
  });

  const items: MenuProps["items"] = [
    {
      key: "settings",
      icon: <SettingOutlined />,
      label: "Settings",
      disabled: true,
    },
    {
      type: "divider",
    },
    {
      key: "logout",
      icon: <LogoutOutlined />,
      label: "Logout",
      onClick: () => logoutUser(),
      disabled: isPending,
      className: "text-red-500 hover:text-red-600",
    },
  ];

  return (
    <div className="container flex items-center justify-between px-5 py-4 min-h-16">
      <div className="flex items-center gap-3">
        <Avatar
          size={40}
          style={{
            backgroundColor: "#1677FF",
            cursor: "pointer",
          }}
          className="transition-opacity hover:opacity-90"
          icon={user?.name ? null : <UserOutlined />}
          src={user?.profile_url}
        >
          {user?.name?.[0]}
        </Avatar>

        {!collapsed && (
          <div
            className={`min-w-0 flex flex-col transition-all duration-200 ease-in-out overflow-hidden ${
              collapsed ? "w-0 opacity-0" : "w-auto opacity-100"
            }`}
          >
            <div
              className="text-sm text-gray-900 leading-tight mb-0.5"
              ellipsis={{ tooltip: user?.name }}
              style={{ maxWidth: "100px" }}
            >
              {user?.name}
            </div>
            <div
              className="text-xs leading-tight text-gray-500"
              ellipsis={{ tooltip: user?.email }}
              style={{ maxWidth: "100px" }}
            >
              {user?.email}
            </div>
          </div>
        )}
      </div>

      {!collapsed && (
        <Dropdown
          menu={{ items }}
          placement="topRight"
          trigger={["click"]}
          overlayClassName="user-profile-dropdown"
        >
          <Button
            type="text"
            shape="circle"
            icon={<MoreOutlined className="text-gray-600" />}
            className="hover:bg-gray-100 ml-2 transition-colors"
          />
        </Dropdown>
      )}
    </div>
  );
}

export default UserProfile;
