import { MoreOutlined } from "@ant-design/icons";
import { Avatar, Button, Dropdown, MenuProps, Tooltip } from "antd";
import BlinkitSvg from "../../../assets/blinkit.svg";
import { TenantHeaderProps } from "../types";
import useUserStore from "../../../stores/userStore";
import startCase from "lodash.startcase";

function TenantHeader({ collapsed }: TenantHeaderProps) {
  const {
    selectedTenant,
    tenants,
    selectedEntity,
    entities,
    setSelectedTenant,
    setSelectedEntity,
  } = useUserStore((state) => state);

  // there will be a relation for tenant <> entites
  const items: MenuProps["items"] = tenants?.map((tenant: string) => ({
    key: tenant,
    label: tenant,
    children: entities?.map((entity: string) => ({
      key: entity,
      label: entity,
    })),
  }));

  return (
    <div className="tenant-header-container flex items-center justify-between px-5 py-4 min-h-16 border-b border-gray-100">
      <div className="flex items-center gap-3">
        <Dropdown
          menu={{
            items,
            selectable: true,
            defaultSelectedKeys: [selectedTenant, selectedEntity],
            onClick: ({ key, keyPath }) => {
              if (keyPath.length === 1) {
                setSelectedTenant(key);
              } else if (keyPath.length === 2) {
                setSelectedTenant(keyPath[1]);
                setSelectedEntity(keyPath[0]);
              }
            },
          }}
          trigger={["click"]}
        >
          <Avatar
            shape="square"
            className="cursor-pointer hover:opacity-90 transition-opacity"
            src={BlinkitSvg}
            size={40}
          />
        </Dropdown>

        {!collapsed && (
          <div>
            <div className="font-semibold text-gray-900">
              {startCase(selectedTenant)}
            </div>
            <div className="text-xs text-gray-600">
              {startCase(selectedEntity)}
            </div>
          </div>
        )}
      </div>

      {!collapsed && (
        <Tooltip
          title="More Options"
          overlayInnerStyle={{ padding: "4px 8px", fontSize: "12px" }}
        >
          <Button
            type="text"
            shape="circle"
            icon={<MoreOutlined rotate={90} className="text-xl" />}
            className="hover:bg-gray-50"
          />
        </Tooltip>
      )}
    </div>
  );
}

export default TenantHeader;
