import { useEffect, useState } from "react";
import { Button, Layout, Menu } from "antd";
import { useNavigate, useLocation } from "react-router-dom";
import useUserStore from "../../stores/userStore";
import { Content } from "antd/es/layout/layout";
import TenantHeader from "./TenantHeader";
import UserProfile from "./UserProfile";
import { getNavBarItems } from "../../constants/navBarItems";
import { LeftOutlined, RightOutlined } from "@ant-design/icons";
import { CoreLayoutProps } from "./types";
import "./styles.css";

const { Sider } = Layout;

function CoreLayout({ children }: CoreLayoutProps) {
  const navigate = useNavigate();
  const location = useLocation();

  const {
    currentNav,
    setCurrentNav,
    selectedEntity,
    selectedTenant,
    setSelectedEntity,
    setSelectedTenant,
  } = useUserStore((state) => state);

  const [collapsed, setCollapsed] = useState(true);

  const navBarItems = getNavBarItems();

  const handleMenuClick = ({ key }: { key: string }): void => {
    if (key) {
      navigate(key);
      setCurrentNav(key);
    }
  };

  useEffect(() => {
    const pathName = location?.pathname;

    const [, tenant, entity, tabRoute, category] = pathName?.split("/") || [];

    if (tenant && selectedTenant !== tenant) setSelectedTenant(tenant);

    if (entity && selectedEntity !== entity) setSelectedEntity(entity);

    if (
      pathName !== null &&
      pathName !== undefined &&
      currentNav !== pathName.toString()
    ) {
      const path =
        "/" + tenant + "/" + entity + "/" + tabRoute + "/" + category;
      setCurrentNav(path.toString());
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location?.pathname, setCurrentNav, selectedTenant, selectedEntity]);

  return (
    <Layout
      id="core-layout"
      hasSider
      style={{
        minHeight: "100vh",
      }}
    >
      <Sider
        className="sidebar relative z-10"
        theme="light"
        collapsible
        collapsed={collapsed}
        onCollapse={(value) => setCollapsed(value)}
        collapsedWidth={80}
        width={240}
        trigger={null}
      >
        <div className="sidebar-container">
          <div className="sidebar-top">
            <TenantHeader collapsed={collapsed} />
            <Menu
              theme="light"
              items={navBarItems}
              selectedKeys={[currentNav]}
              onClick={handleMenuClick}
              mode="inline"
            />
          </div>

          <div className="sidebar-bottom relative">
            <UserProfile collapsed={collapsed} />

            <div className="absolute right-[-1rem] top-[1.25rem]">
              <Button
                onClick={() => setCollapsed(!collapsed)}
                shape="circle"
                icon={collapsed ? <RightOutlined /> : <LeftOutlined />}
                className="border-[0.5px] !border-blue !bg-white !text-blue"
              />
            </div>
          </div>
        </div>
      </Sider>
      <Layout>
        <Content className="content-container">{children}</Content>
      </Layout>
    </Layout>
  );
}

export default CoreLayout;
