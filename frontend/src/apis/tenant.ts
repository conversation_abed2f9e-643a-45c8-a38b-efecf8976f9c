import axios, { AxiosResponse } from "axios";
import { apiUrl } from "../../env";

const axiosTenantApi = axios.create({
  baseURL: `${apiUrl}/v1/auth`,
  headers: {
    "Content-Type": "application/json",
  },
});

//* gives redirecting google url
export async function getTenants() {
  // : Promise<AxiosResponse> {
  // return axiosTenantApi.get("/tenants");
  return Promise.resolve({
    data: {
      status: "success",
      data: ["blinkit"],
    },
  });
}
