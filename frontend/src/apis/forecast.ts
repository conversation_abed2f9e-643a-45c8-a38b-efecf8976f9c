import axios, { AxiosResponse } from "axios";
import { apiUrl } from "../../env";
import { DirtyField } from "../stores/plannerStore";
import useUserStore from "../stores/userStore";
import { ApprovalInputsInterface } from "../components/EditableTable/types";

// * When you create a custom Axios instance like axiosForecaseApi, it does not inherit the global axios.defaults at times.
// * so if you get 401, add withCredentials: true to the axios instance

const getAxiosForecaseApi = (hasCategory = false, categoryId = "") => {
  const { selectedTenant, selectedEntity } = useUserStore.getState();

  if (hasCategory) {
    return axios.create({
      baseURL: `${apiUrl}/v1/${selectedTenant}/${selectedEntity}/${categoryId}/forecasts`,
      withCredentials: true,
      headers: {
        "Content-Type": "application/json",
      },
    });
  }

  return axios.create({
    baseURL: `${apiUrl}/v1/${selectedTenant}/${selectedEntity}/forecasts`,
    withCredentials: true,
    headers: {
      "Content-Type": "application/json",
    },
  });
};

const axiosForecaseApi = getAxiosForecaseApi();

export interface Forecast {
  forecast_id: number;
  multi_aggregation_value?: [string];
  multi_aggregation_grain?: string;
  single_aggregation_value?: [string];
  single_aggregation_grain?: string;
  start_time: string;
  time_grain: string;
}

export async function getForecast(
  signal: AbortSignal,
  categoryId: string,
  forecast_id: number,
  params: Forecast
): Promise<AxiosResponse> {
  const axiosCategoryForecaseApi = getAxiosForecaseApi(true, categoryId);
  return axiosCategoryForecaseApi.get(`/${forecast_id}`, { signal, params });
}

export async function getDetailedForecast(
  signal: AbortSignal,
  categoryId: string,
  forecast_id: number,
  params: Forecast
): Promise<AxiosResponse> {
  const axiosCategoryForecaseApi = getAxiosForecaseApi(true, categoryId);
  return axiosCategoryForecaseApi.get(`/${forecast_id}/detailed`, {
    signal,
    params,
  });
}

export async function getForecastInputs(
  signal: AbortSignal,
  categoryId: string,
  forecast_id: number,
  params: Forecast
): Promise<AxiosResponse> {
  const axiosCategoryForecaseApi = getAxiosForecaseApi(true, categoryId);
  return axiosCategoryForecaseApi.get(`/${forecast_id}/inputs`, {
    signal,
    params: {
      ...params,
      get_outputs: true, //* to get the output values for the inputs
    },
  });
}

export async function getForecastModels(
  signal: AbortSignal,
  categoryId: string,
  forecast_id: number
): Promise<AxiosResponse> {
  const axiosCategoryForecaseApi = getAxiosForecaseApi(true, categoryId);
  return axiosCategoryForecaseApi.get(`/${forecast_id}/models`, {
    signal,
    params: {
      forecast_id,
    },
  });
}

export interface InputsInterface {
  entity_id: number;
  start_time: string;
  time_grain: string;
  values: [
    {
      metric_name: string;
      value: number;
    }
  ];
}

export async function postForecastInputs(payload: {
  categoryId: string;
  forecast_id: number;
  inputs: InputsInterface[];
}): Promise<AxiosResponse> {
  const { categoryId, forecast_id, inputs } = payload;
  const axiosCategoryForecaseApi = getAxiosForecaseApi(true, categoryId);
  return axiosCategoryForecaseApi.post(`/${forecast_id}/inputs`, {
    forecast_id,
    inputs,
  });
}

export async function updateInputStatus(payload: {
  categoryId: string;
  forecast_id: number;
  inputs: ApprovalInputsInterface[];
}): Promise<AxiosResponse> {
  const { categoryId, forecast_id, inputs } = payload;
  const axiosCategoryForecaseApi = getAxiosForecaseApi(true, categoryId);
  return axiosCategoryForecaseApi.post(
    `/${forecast_id}/inputs/update_status`,
    inputs
  );
}

export interface modelChangeInterface {
  entity_value: string;
  model_id: string;
}

export async function updateEntityModel(payload: {
  categoryId: string;
  forecast_id: number;
  modelChange: modelChangeInterface;
}): Promise<AxiosResponse> {
  const { categoryId, forecast_id, modelChange } = payload;
  const axiosCategoryForecaseApi = getAxiosForecaseApi(true, categoryId);
  return axiosCategoryForecaseApi.post(
    `/${forecast_id}/update_entity_model`,
    modelChange
  );
}
