import axios, { AxiosResponse } from "axios";
import { apiUrl } from "../../env";
import { ParamsInterface } from "./stores";

const axiosOrdersApi = axios.create({
  baseURL: `${apiUrl}/v1/orders`,
  withCredentials: true,
  headers: {
    "Content-Type": "application/json",
  },
});

// Todo: move store_id from route to params as it can be an array also (if multi select is introduced)
export async function getOrderMetrics(
  signal: AbortSignal,
  params: ParamsInterface
): Promise<AxiosResponse> {
  return axiosOrdersApi.get(`/metrics`, { signal, params });
}
