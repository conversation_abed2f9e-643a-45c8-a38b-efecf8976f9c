import axios, { AxiosResponse } from "axios";
import { apiUrl } from "../../env";
import useUserStore from "../stores/userStore";
import { ParamsInterface } from "./stores";

const getAxiosMetricsApi = (hasCategory = false, categoryId = "") => {
  const { selectedTenant, selectedEntity } = useUserStore.getState();

  if (hasCategory) {
    return axios.create({
      baseURL: `${apiUrl}/v1/${selectedTenant}/${selectedEntity}/${categoryId}/metrics`,
      withCredentials: true,
      headers: {
        "Content-Type": "application/json",
      },
    });
  }

  return axios.create({
    baseURL: `${apiUrl}/v1/${selectedTenant}/${selectedEntity}/metrics`,
    withCredentials: true,
    headers: {
      "Content-Type": "application/json",
    },
  });
};

const axiosMetricsApi = getAxiosMetricsApi();

export async function getAllOrderMetrics(
  signal: AbortSignal
): Promise<AxiosResponse> {
  return axiosMetricsApi.get("/orders", { signal });
}

export async function getAllStoreMetrics(
  signal: AbortSignal
): Promise<AxiosResponse> {
  return axiosMetricsApi.get("/stores", { signal });
}

export async function getVizMetrics(
  signal: AbortSignal,
  categoryId: string,
  params: ParamsInterface
): Promise<AxiosResponse> {
  const axiosCategoryMetricsApi = getAxiosMetricsApi(true, categoryId);
  return axiosCategoryMetricsApi.get(`/viz`, { signal, params });
}
