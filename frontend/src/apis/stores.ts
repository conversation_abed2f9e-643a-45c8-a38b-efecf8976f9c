import axios, { AxiosResponse } from "axios";
import { apiUrl } from "../../env";

const axiosStoresApi = axios.create({
  baseURL: `${apiUrl}/v1/stores`,
  withCredentials: true,
  headers: {
    "Content-Type": "application/json",
  },
});

export async function getAllStores(
  signal: AbortSignal
): Promise<AxiosResponse> {
  return axiosStoresApi.get("/", { signal });
}

export interface ParamsInterface {
  detailed?: boolean;
  start_time: Date;
  end_time: Date;
  comparison: string[];
  metric_list?: string[];
  event_date?: string;
  categories?: number[]; //* not being sent to backend, maintained on frontend for overall page (where category selector is present instead of metric selector)
}

// Todo: move store_id from route to params as it can be an array also (if multi select is introduced)
export async function getStoreMetrics(
  signal: AbortSignal,
  params: ParamsInterface
): Promise<AxiosResponse> {
  return axiosStoresApi.get(`/metrics`, { signal, params });
}
