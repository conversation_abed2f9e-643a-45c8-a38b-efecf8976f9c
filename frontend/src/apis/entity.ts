import axios, { AxiosResponse } from "axios";
import { apiUrl } from "../../env";

const axiosEntityApi = axios.create({
  baseURL: `${apiUrl}/v1/auth`,
  headers: {
    "Content-Type": "application/json",
  },
});

//* gives redirecting google url
export async function getEntities() {
  // : Promise<AxiosResponse> {
  // return axiosEntityApi.get("/entities");
  return Promise.resolve({
    data: {
      status: "success",
      data: ["storeops", "general"],
    },
  });
}
