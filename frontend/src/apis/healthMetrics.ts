import axios, { AxiosResponse } from "axios";
import { apiUrl } from "../../env";
import { ParamsInterface } from "./stores";

const axiosHealthApi = axios.create({
  baseURL: `${apiUrl}/v1/`,
  withCredentials: true,
  headers: {
    "Content-Type": "application/json",
  },
});

export async function getStoreHealth(
  signal: AbortSignal,
  params: ParamsInterface
): Promise<AxiosResponse> {
  return axiosHealthApi.get("/blinkit/store/health/", { signal, params });
}
