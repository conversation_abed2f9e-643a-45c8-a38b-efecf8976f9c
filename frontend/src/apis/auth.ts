import axios, { AxiosResponse } from "axios";
import { apiUrl } from "../../env";

const axiosAuthApi = axios.create({
  baseURL: `${apiUrl}/v1/auth`,
  headers: {
    "Content-Type": "application/json",
  },
});

//* gives redirecting google url
export async function login(): Promise<AxiosResponse> {
  return axiosAuthApi.get("/google/login");
}

// * callback to google to get the access token
export async function validateUser(params: {
  code: string;
}): Promise<AxiosResponse> {
  return axiosAuthApi.get("/google/callback", { params });
}

// * callback to google to get the access token
export async function logout(): Promise<AxiosResponse> {
  return axiosAuthApi.post("/logout");
}
