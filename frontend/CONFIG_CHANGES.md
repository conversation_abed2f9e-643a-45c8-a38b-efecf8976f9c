# Configuration Changes

## Pre-fetch forecasts on selection of department

1. Filters: `[{id: 1, name: "Forecast 1"}] : forecast[]`
2. API endpoint: `/planner/{forecast_id}`
3. Component: `Planner`
   - Takes `forecast_id` as prop
   - Calls API with `forecast_id`
   - Displays data

## Layers

- Authentication layers
- Authorization layers
- Entity department layers (config layers)

## Project Structure

```
src/
│
├── app/                   # App-level setup
│   └── App.tsx
│
├── auth/                  # Authentication & Authorization layer
│   └── ProtectedRoute.tsx (AuthenticationWrapper)
│   └── AuthorizationWrapper.tsx
│
├── configWrappers/        # Tenant/entity/category level wrappers
│   ├── TenantWrapper.tsx
│   ├── EntityWrapper.tsx
│   └── CategoryWrapper.tsx (need to be added)
│
├── configuration/                # Configuration for tenants/entities/categories
│   ├── tenants/
|   |    └── Entity.ts
|   └── tableConfigs
│
├── components/              # Feature-specific modules
│
├── hooks/                 # Reusable hooks
│
├── layouts/               # Layouts and shell UI
│   └── CoreLayout
│
├── pages/                 # Page-level route components
│
├── stores/                # Zustand stores
│
├── utils/                 # Utility functions and helpers
│
├── types/                 # Global TypeScript types and interfaces
│
└── index.tsx              # App entry
```
