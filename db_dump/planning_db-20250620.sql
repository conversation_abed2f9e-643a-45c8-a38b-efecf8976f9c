--
-- PostgreSQL database dump
--

-- Dumped from database version 17.5 (Debian 17.5-1.pgdg120+1)
-- Dumped by pg_dump version 17.5 (Debian 17.5-1.pgdg120+1)

-- Started on 2025-06-20 09:39:56 UTC

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- TOC entry 907 (class 1247 OID 16593)
-- Name: inputstatus; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.inputstatus AS ENUM (
    'PENDING',
    'APPROVED',
    'REJECTED'
);


ALTER TYPE public.inputstatus OWNER TO postgres;

--
-- TOC entry 883 (class 1247 OID 16450)
-- Name: metrictype; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.metrictype AS ENUM (
    'COUNT',
    'AVERAGE',
    'PERCENTAGE',
    'SECONDS',
    'MINUTES',
    'HOURS',
    'RUPEES',
    'DAYS'
);


ALTER TYPE public.metrictype OWNER TO postgres;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- TOC entry 217 (class 1259 OID 16385)
-- Name: alembic_version; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.alembic_version (
    version_num character varying(32) NOT NULL
);


ALTER TABLE public.alembic_version OWNER TO postgres;

--
-- TOC entry 223 (class 1259 OID 16418)
-- Name: categories; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.categories (
    id integer NOT NULL,
    name character varying,
    entity_id integer,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public.categories OWNER TO postgres;

--
-- TOC entry 222 (class 1259 OID 16417)
-- Name: categories_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.categories_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.categories_id_seq OWNER TO postgres;

--
-- TOC entry 3527 (class 0 OID 0)
-- Dependencies: 222
-- Name: categories_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.categories_id_seq OWNED BY public.categories.id;


--
-- TOC entry 221 (class 1259 OID 16402)
-- Name: entities; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.entities (
    id integer NOT NULL,
    name character varying,
    tenant_id integer,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public.entities OWNER TO postgres;

--
-- TOC entry 220 (class 1259 OID 16401)
-- Name: entities_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.entities_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.entities_id_seq OWNER TO postgres;

--
-- TOC entry 3528 (class 0 OID 0)
-- Dependencies: 220
-- Name: entities_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.entities_id_seq OWNED BY public.entities.id;


--
-- TOC entry 230 (class 1259 OID 16498)
-- Name: forecast_metrics; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.forecast_metrics (
    id integer NOT NULL,
    forecast_id integer NOT NULL,
    metric_id integer NOT NULL,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now(),
    editable boolean NOT NULL
);


ALTER TABLE public.forecast_metrics OWNER TO postgres;

--
-- TOC entry 229 (class 1259 OID 16497)
-- Name: forecast_metrics_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.forecast_metrics_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.forecast_metrics_id_seq OWNER TO postgres;

--
-- TOC entry 3529 (class 0 OID 0)
-- Dependencies: 229
-- Name: forecast_metrics_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.forecast_metrics_id_seq OWNED BY public.forecast_metrics.id;


--
-- TOC entry 236 (class 1259 OID 16558)
-- Name: forecast_model_entities; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.forecast_model_entities (
    id integer NOT NULL,
    forecast_models_id integer NOT NULL,
    entity_id integer NOT NULL,
    entity_value integer NOT NULL,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public.forecast_model_entities OWNER TO postgres;

--
-- TOC entry 235 (class 1259 OID 16557)
-- Name: forecast_model_entities_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.forecast_model_entities_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.forecast_model_entities_id_seq OWNER TO postgres;

--
-- TOC entry 3530 (class 0 OID 0)
-- Dependencies: 235
-- Name: forecast_model_entities_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.forecast_model_entities_id_seq OWNED BY public.forecast_model_entities.id;


--
-- TOC entry 232 (class 1259 OID 16519)
-- Name: forecast_models; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.forecast_models (
    id integer NOT NULL,
    forecast_id integer NOT NULL,
    model_id character varying NOT NULL,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public.forecast_models OWNER TO postgres;

--
-- TOC entry 231 (class 1259 OID 16518)
-- Name: forecast_models_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.forecast_models_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.forecast_models_id_seq OWNER TO postgres;

--
-- TOC entry 3531 (class 0 OID 0)
-- Dependencies: 231
-- Name: forecast_models_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.forecast_models_id_seq OWNED BY public.forecast_models.id;


--
-- TOC entry 225 (class 1259 OID 16434)
-- Name: forecasts; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.forecasts (
    id integer NOT NULL,
    category_id integer,
    table_name character varying,
    display_name character varying,
    is_active boolean,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public.forecasts OWNER TO postgres;

--
-- TOC entry 224 (class 1259 OID 16433)
-- Name: forecasts_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.forecasts_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.forecasts_id_seq OWNER TO postgres;

--
-- TOC entry 3532 (class 0 OID 0)
-- Dependencies: 224
-- Name: forecasts_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.forecasts_id_seq OWNED BY public.forecasts.id;


--
-- TOC entry 239 (class 1259 OID 16605)
-- Name: input_values; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.input_values (
    input_id integer NOT NULL,
    metric_id integer NOT NULL,
    value double precision NOT NULL
);


ALTER TABLE public.input_values OWNER TO postgres;

--
-- TOC entry 234 (class 1259 OID 16542)
-- Name: inputs; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.inputs (
    id integer NOT NULL,
    forecast_id integer NOT NULL,
    time_grain character varying(255) NOT NULL,
    start_time timestamp without time zone NOT NULL,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now(),
    is_approved public.inputstatus NOT NULL,
    entity_id integer NOT NULL
);


ALTER TABLE public.inputs OWNER TO postgres;

--
-- TOC entry 233 (class 1259 OID 16541)
-- Name: inputs_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.inputs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.inputs_id_seq OWNER TO postgres;

--
-- TOC entry 3533 (class 0 OID 0)
-- Dependencies: 233
-- Name: inputs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.inputs_id_seq OWNED BY public.inputs.id;


--
-- TOC entry 227 (class 1259 OID 16468)
-- Name: metrics; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.metrics (
    id integer NOT NULL,
    category_id integer,
    select_query character varying,
    aggregate_query character varying,
    name character varying,
    display_name character varying,
    metric_type public.metrictype,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now(),
    grain character varying
);


ALTER TABLE public.metrics OWNER TO postgres;

--
-- TOC entry 226 (class 1259 OID 16467)
-- Name: metrics_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.metrics_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.metrics_id_seq OWNER TO postgres;

--
-- TOC entry 3534 (class 0 OID 0)
-- Dependencies: 226
-- Name: metrics_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.metrics_id_seq OWNED BY public.metrics.id;


--
-- TOC entry 228 (class 1259 OID 16483)
-- Name: models; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.models (
    id character varying NOT NULL,
    category_id integer,
    display_name character varying,
    is_active boolean,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public.models OWNER TO postgres;

--
-- TOC entry 219 (class 1259 OID 16391)
-- Name: tenants; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.tenants (
    id integer NOT NULL,
    name character varying,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public.tenants OWNER TO postgres;

--
-- TOC entry 218 (class 1259 OID 16390)
-- Name: tenants_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.tenants_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.tenants_id_seq OWNER TO postgres;

--
-- TOC entry 3535 (class 0 OID 0)
-- Dependencies: 218
-- Name: tenants_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.tenants_id_seq OWNED BY public.tenants.id;


--
-- TOC entry 238 (class 1259 OID 16579)
-- Name: users; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.users (
    id integer NOT NULL,
    full_name character varying,
    email character varying NOT NULL,
    is_allowed boolean DEFAULT false,
    created_at timestamp without time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.users OWNER TO postgres;

--
-- TOC entry 237 (class 1259 OID 16578)
-- Name: users_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.users_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.users_id_seq OWNER TO postgres;

--
-- TOC entry 3536 (class 0 OID 0)
-- Dependencies: 237
-- Name: users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.users_id_seq OWNED BY public.users.id;


--
-- TOC entry 3279 (class 2604 OID 16421)
-- Name: categories id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.categories ALTER COLUMN id SET DEFAULT nextval('public.categories_id_seq'::regclass);


--
-- TOC entry 3276 (class 2604 OID 16405)
-- Name: entities id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.entities ALTER COLUMN id SET DEFAULT nextval('public.entities_id_seq'::regclass);


--
-- TOC entry 3290 (class 2604 OID 16501)
-- Name: forecast_metrics id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_metrics ALTER COLUMN id SET DEFAULT nextval('public.forecast_metrics_id_seq'::regclass);


--
-- TOC entry 3299 (class 2604 OID 16561)
-- Name: forecast_model_entities id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_model_entities ALTER COLUMN id SET DEFAULT nextval('public.forecast_model_entities_id_seq'::regclass);


--
-- TOC entry 3293 (class 2604 OID 16522)
-- Name: forecast_models id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_models ALTER COLUMN id SET DEFAULT nextval('public.forecast_models_id_seq'::regclass);


--
-- TOC entry 3282 (class 2604 OID 16437)
-- Name: forecasts id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecasts ALTER COLUMN id SET DEFAULT nextval('public.forecasts_id_seq'::regclass);


--
-- TOC entry 3296 (class 2604 OID 16545)
-- Name: inputs id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.inputs ALTER COLUMN id SET DEFAULT nextval('public.inputs_id_seq'::regclass);


--
-- TOC entry 3285 (class 2604 OID 16471)
-- Name: metrics id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.metrics ALTER COLUMN id SET DEFAULT nextval('public.metrics_id_seq'::regclass);


--
-- TOC entry 3273 (class 2604 OID 16394)
-- Name: tenants id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tenants ALTER COLUMN id SET DEFAULT nextval('public.tenants_id_seq'::regclass);


--
-- TOC entry 3302 (class 2604 OID 16582)
-- Name: users id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users ALTER COLUMN id SET DEFAULT nextval('public.users_id_seq'::regclass);


--
-- TOC entry 3499 (class 0 OID 16385)
-- Dependencies: 217
-- Data for Name: alembic_version; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.alembic_version (version_num) FROM stdin;
e1884b8acbb3
\.


--
-- TOC entry 3505 (class 0 OID 16418)
-- Dependencies: 223
-- Data for Name: categories; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.categories (id, name, entity_id, created_at, updated_at) FROM stdin;
1	instore	1	2025-05-30 10:45:18.519518	2025-05-30 10:45:18.519518
2	core	1	2025-05-30 10:45:18.519518	2025-05-30 10:45:18.519518
\.


--
-- TOC entry 3503 (class 0 OID 16402)
-- Dependencies: 221
-- Data for Name: entities; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.entities (id, name, tenant_id, created_at, updated_at) FROM stdin;
1	storeops	1	2025-05-30 10:44:53.558898	2025-05-30 10:44:53.558898
\.


--
-- TOC entry 3512 (class 0 OID 16498)
-- Dependencies: 230
-- Data for Name: forecast_metrics; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.forecast_metrics (id, forecast_id, metric_id, created_at, updated_at, editable) FROM stdin;
1	1	166	2025-05-30 10:49:22.43191	2025-05-30 10:49:22.43191	t
2	1	167	2025-05-30 10:49:22.43191	2025-05-30 10:49:22.43191	t
3	1	169	2025-05-30 10:49:22.43191	2025-05-30 10:49:22.43191	t
4	1	172	2025-05-30 10:49:22.43191	2025-05-30 10:49:22.43191	t
5	1	173	2025-05-30 10:49:22.43191	2025-05-30 10:49:22.43191	t
6	1	174	2025-05-30 10:49:22.43191	2025-05-30 10:49:22.43191	t
7	1	175	2025-05-30 10:49:22.43191	2025-05-30 10:49:22.43191	t
8	1	176	2025-05-30 10:49:22.43191	2025-05-30 10:49:22.43191	t
9	1	186	2025-05-30 10:49:22.43191	2025-05-30 10:49:22.43191	t
10	1	201	2025-05-30 10:49:22.43191	2025-05-30 10:49:22.43191	t
11	1	202	2025-05-30 10:49:22.43191	2025-05-30 10:49:22.43191	t
12	1	203	2025-05-30 10:49:22.43191	2025-05-30 10:49:22.43191	t
13	2	152	2025-05-30 11:30:30.599902	2025-05-30 11:30:30.599902	t
14	2	153	2025-05-30 11:30:30.599902	2025-05-30 11:30:30.599902	t
15	2	154	2025-05-30 11:30:30.599902	2025-05-30 11:30:30.599902	t
16	2	155	2025-05-30 11:30:30.599902	2025-05-30 11:30:30.599902	t
\.


--
-- TOC entry 3518 (class 0 OID 16558)
-- Dependencies: 236
-- Data for Name: forecast_model_entities; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.forecast_model_entities (id, forecast_models_id, entity_id, entity_value, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 3514 (class 0 OID 16519)
-- Dependencies: 232
-- Data for Name: forecast_models; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.forecast_models (id, forecast_id, model_id, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 3507 (class 0 OID 16434)
-- Dependencies: 225
-- Data for Name: forecasts; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.forecasts (id, category_id, table_name, display_name, is_active, created_at, updated_at) FROM stdin;
1	1	\N	Instore Forecasts	t	2025-05-30 10:49:22.43191	2025-05-30 10:49:22.43191
2	2	\N	Core Forecasts	t	2025-05-30 11:30:30.599902	2025-05-30 11:30:30.599902
\.


--
-- TOC entry 3521 (class 0 OID 16605)
-- Dependencies: 239
-- Data for Name: input_values; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.input_values (input_id, metric_id, value) FROM stdin;
1	186	1
2	202	46192234
\.


--
-- TOC entry 3516 (class 0 OID 16542)
-- Dependencies: 234
-- Data for Name: inputs; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.inputs (id, forecast_id, time_grain, start_time, created_at, updated_at, is_approved, entity_id) FROM stdin;
1	1	weekly	2025-06-02 00:00:00	2025-05-30 11:01:50.802456	2025-05-30 11:01:50.802456	PENDING	1122
2	1	weekly	2025-06-02 00:00:00	2025-05-30 11:02:02.862167	2025-05-30 11:02:02.862167	PENDING	1122
\.


--
-- TOC entry 3509 (class 0 OID 16468)
-- Dependencies: 227
-- Data for Name: metrics; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.metrics (id, category_id, select_query, aggregate_query, name, display_name, metric_type, created_at, updated_at, grain) FROM stdin;
152	2	items_per_order	CAST(SUM(total_items_quantity_ordered) AS DOUBLE) / NULLIF(SUM(instore_orders), 0)	items_per_order	Avg. number of items per order	COUNT	2025-05-30 10:48:11.86954	2025-05-30 10:48:11.86954	weekly
153	2	instore_orders	SUM(instore_orders)	instore_orders	Orders placed	COUNT	2025-05-30 10:48:11.885905	2025-05-30 10:48:11.885905	weekly
154	2	total_items_quantity_ordered	SUM(total_items_quantity_ordered)	total_items_quantity_ordered	Items Quantity Ordered	COUNT	2025-05-30 10:48:11.893579	2025-05-30 10:48:11.893579	weekly
155	2	total_quantity_indent	SUM(total_quantity_indent)	total_quantity_indent	Total Quantity Indent	COUNT	2025-05-30 10:48:11.901019	2025-05-30 10:48:11.901019	weekly
156	1	auditor_active_time_mins	SUM(auditor_active_time_mins)	auditor_active_time_mins	Auditor Active Time	MINUTES	2025-05-30 10:48:11.907669	2025-05-30 10:48:11.907669	
157	1	c2a_within_10_orders	SUM(c2a_within_10_orders)	c2a_within_10_orders	C2A Within 10 Orders	COUNT	2025-05-30 10:48:11.919478	2025-05-30 10:48:11.919478	
158	1	c2a_within_10_sec	CAST(SUM(c2a_within_10_orders) AS DOUBLE) * 100 / NULLIF(SUM(orders), 0)	c2a_within_10_sec	C2A % under 10 sec	PERCENTAGE	2025-05-30 10:48:11.929389	2025-05-30 10:48:11.929389	
159	1	carts	SUM(carts)	carts	Total Carts	COUNT	2025-05-30 10:48:11.937076	2025-05-30 10:48:11.937076	
160	1	dh_pct	CAST(SUM(direct_handover_orders) AS DOUBLE) * 100.0 / NULLIF(SUM(instore_orders), 0)	dh_pct	DH %	PERCENTAGE	2025-05-30 10:48:11.943923	2025-05-30 10:48:11.943923	
161	1	direct_handover_orders	SUM(direct_handover_orders)	direct_handover_orders	Direct Handover Orders	COUNT	2025-05-30 10:48:11.950349	2025-05-30 10:48:11.950349	
162	1	fixed_absent_pct	CAST(SUM(fixed_employee_absent_count) AS DOUBLE) * 100 / NULLIF(SUM(planned_employee_count), 0)	fixed_absent_pct	Fixed Absenteeism %	PERCENTAGE	2025-05-30 10:48:11.956844	2025-05-30 10:48:11.956844	
163	1	fixed_employee_absent_count	SUM(fixed_employee_absent_count)	fixed_employee_absent_count	Fixed Employee Absent Count	COUNT	2025-05-30 10:48:11.964231	2025-05-30 10:48:11.964231	
164	1	manhours_fnv	SUM(manhours_fnv)	manhours_fnv	manhours_fnv	HOURS	2025-05-30 10:48:11.971691	2025-05-30 10:48:11.971691	
165	1	gsp_surge_carts	SUM(gsp_surge_carts)	gsp_surge_carts	GSP Surge Carts	COUNT	2025-05-30 10:48:11.979438	2025-05-30 10:48:11.979438	
166	1	headcount	SUM(headcount)	headcount	Headcount	COUNT	2025-05-30 10:48:11.987161	2025-05-30 10:48:11.987161	weekly,daily
167	1	headcount_shift_start	SUM(headcount_shift_start)	headcount_shift_start	Headcount Shift Start	COUNT	2025-05-30 10:48:11.994402	2025-05-30 10:48:11.994402	hourly
168	1	instore_orders	SUM(instore_orders)	instore_orders	Instore Orders	COUNT	2025-05-30 10:48:12.001505	2025-05-30 10:48:12.001505	
169	1	items_put_away_per_hour	CAST(SUM(putaway_qty) AS FLOAT) / NULLIF(CAST(SUM(putter_active_time_mins) AS FLOAT) / 60, 0)	items_put_away_per_hour	IPH	COUNT	2025-05-30 10:48:12.008158	2025-05-30 10:48:12.008158	weekly
170	1	login_hours	SUM(login_hours)	login_hours	Total login hours	HOURS	2025-05-30 10:48:12.015192	2025-05-30 10:48:12.015192	
171	1	login_mins	SUM(login_mins)	login_mins	Total Login Minutes	MINUTES	2025-05-30 10:48:12.033537	2025-05-30 10:48:12.033537	
172	1	manhours_fixed	SUM(manhours_fixed)	manhours_fixed	Fixed Manhours	HOURS	2025-05-30 10:48:12.045386	2025-05-30 10:48:12.045386	hourly
173	1	manhours_od	SUM(manhours_od)	manhours_od	OD Manhours	HOURS	2025-05-30 10:48:12.053392	2025-05-30 10:48:12.053392	daily,hourly
174	1	manhours_picking	SUM(manhours_picking)	manhours_picking	Picking Manhours	HOURS	2025-05-30 10:48:12.060909	2025-05-30 10:48:12.060909	hourly
175	1	manhours_putaway	SUM(manhours_putaway)	manhours_putaway	Putaway Manhours	HOURS	2025-05-30 10:48:12.06862	2025-05-30 10:48:12.06862	hourly
176	1	od_contribution_pct	SUM(qty_ordered_od)/SUM(qty_ordered_fixed)	od_contribution_pct	% OD Contribution	PERCENTAGE	2025-05-30 10:48:12.075634	2025-05-30 10:48:12.075634	weekly
177	1	od_eph	CAST(SUM(total_payout) AS DOUBLE) * 100 / NULLIF(SUM(login_mins), 0)	od_eph	OD Slot level EPH	PERCENTAGE	2025-05-30 10:48:12.08232	2025-05-30 10:48:12.08232	
178	1	overall_surge_pct	CAST(SUM(picker_surge_carts + gsp_surge_carts + rain_surge_carts) AS DOUBLE) * 100.0 / NULLIF(SUM(carts), 0)	overall_surge_pct	Overall Surge %	PERCENTAGE	2025-05-30 10:48:12.090268	2025-05-30 10:48:12.090268	
179	1	picker_active_time_fixed_sec	SUM(picker_active_time_fixed_sec)	picker_active_time_fixed_sec	Picker Active Time Fixed (Sec)	SECONDS	2025-05-30 10:48:12.098736	2025-05-30 10:48:12.098736	
180	1	picker_active_time_mins	SUM(picker_active_time_mins)	picker_active_time_mins	Picker Active Time	MINUTES	2025-05-30 10:48:12.106444	2025-05-30 10:48:12.106444	
181	1	picker_busy_time_fixed_sec	SUM(picker_busy_time_fixed_sec)	picker_busy_time_fixed_sec	Picker Busy Time Fixed (Sec)	SECONDS	2025-05-30 10:48:12.113965	2025-05-30 10:48:12.113965	
182	1	picker_surge_carts	SUM(picker_surge_carts)	picker_surge_carts	Picker Surge Carts	COUNT	2025-05-30 10:48:12.121738	2025-05-30 10:48:12.121738	
183	1	picker_surge_pct	CAST(SUM(picker_surge_carts) AS DOUBLE) * 100.0 / NULLIF(SUM(carts), 0)	picker_surge_pct	Picker Surge %	PERCENTAGE	2025-05-30 10:48:12.129517	2025-05-30 10:48:12.129517	
184	1	picker_utilization_fixed	CAST(SUM(picker_busy_time_fixed_sec) AS DOUBLE) * 100 / NULLIF(SUM(picker_active_time_fixed_sec), 0)	picker_utilization_fixed	Fixed Picker Utilization	PERCENTAGE	2025-05-30 10:48:12.137793	2025-05-30 10:48:12.137793	
185	1	planned_employee_count	SUM(planned_employee_count)	planned_employee_count	Planned Employee Count	COUNT	2025-05-30 10:48:12.144441	2025-05-30 10:48:12.144441	
186	1	ppi	SUM(total_picking_time_sec)/SUM(total_items_quantity_ordered)	ppi	PPI	SECONDS	2025-05-30 10:48:12.152506	2025-05-30 10:48:12.152506	weekly
187	1	putaway_qty	SUM(putaway_qty)	putaway_qty	Putaway Quantity	COUNT	2025-05-30 10:48:12.159834	2025-05-30 10:48:12.159834	
188	1	putter_active_time_mins	SUM(putter_active_time_mins)	putter_active_time_mins	Putter Active Time	MINUTES	2025-05-30 10:48:12.174728	2025-05-30 10:48:12.174728	
189	1	qty_ordered_fixed	SUM(qty_ordered_fixed)	qty_ordered_fixed	Quantity Ordered Fixed	COUNT	2025-05-30 10:48:12.186561	2025-05-30 10:48:12.186561	
190	1	qty_ordered_new_employee	SUM(qty_ordered_new_employee)	qty_ordered_new_employee	Quantity Ordered New Employee	COUNT	2025-05-30 10:48:12.196296	2025-05-30 10:48:12.196296	
191	1	qty_ordered_new_employee_od	SUM(qty_ordered_new_employee_od)	qty_ordered_new_employee_od	Quantity Ordered New Employee OD	COUNT	2025-05-30 10:48:12.203759	2025-05-30 10:48:12.203759	
192	1	qty_ordered_od	SUM(qty_ordered_od)	qty_ordered_od	Quantity Ordered OD	COUNT	2025-05-30 10:48:12.210408	2025-05-30 10:48:12.210408	
193	1	qty_ordered_old_employee	SUM(qty_ordered_old_employee)	qty_ordered_old_employee	Quantity Ordered Old Employee	COUNT	2025-05-30 10:48:12.219818	2025-05-30 10:48:12.219818	
194	1	qty_ordered_old_employee_od	SUM(qty_ordered_old_employee_od)	qty_ordered_old_employee_od	Quantity Ordered Old Employee OD	COUNT	2025-05-30 10:48:12.227313	2025-05-30 10:48:12.227313	
195	1	r2a_within_10_orders	SUM(r2a_within_10_orders)	r2a_within_10_orders	R2A Within 10 Orders	COUNT	2025-05-30 10:48:12.235021	2025-05-30 10:48:12.235021	
196	1	r2a_within_10_sec	CAST(SUM(r2a_within_10_orders) AS DOUBLE) * 100 / NULLIF(SUM(orders), 0)	r2a_within_10_sec	R2A % under 10 sec	PERCENTAGE	2025-05-30 10:48:12.25143	2025-05-30 10:48:12.25143	
197	1	rain_surge_carts	SUM(rain_surge_carts)	rain_surge_carts	Rain Surge Carts	COUNT	2025-05-30 10:48:12.332594	2025-05-30 10:48:12.332594	
198	1	total_items_quantity_ordered	SUM(total_items_quantity_ordered)	total_items_quantity_ordered	Total quantity ordered	COUNT	2025-05-30 10:48:12.527778	2025-05-30 10:48:12.527778	
199	1	total_payout	SUM(total_payout)	total_payout	Total Payout	RUPEES	2025-05-30 10:48:12.571072	2025-05-30 10:48:12.571072	
200	1	total_picking_time_sec	SUM(total_picking_time_sec)	total_picking_time_sec	Total Picking Time (Sec)	SECONDS	2025-05-30 10:48:12.579504	2025-05-30 10:48:12.579504	
201	1	mandays_fixed	SUM(mandays_fixed)	mandays_fixed	Mandays Fixed	COUNT	2025-05-30 10:48:12.587574	2025-05-30 10:48:12.587574	daily
202	1	picker_util_numerator_fixed	SUM(total_items_quantity_ordered*picker_utilization_fixed)	picker_util_numerator_fixed	Picker utilisation numerator fixed	COUNT	2025-05-30 10:48:12.608239	2025-05-30 10:48:12.608239	weekly
203	1	picker_util_fixed_wt_avg	SUM(picker_util_numerator_fixed/total_items_quantity_ordered)	picker_util_fixed_wt_avg	Picker Util fixed wt_avg	AVERAGE	2025-05-30 10:48:12.616286	2025-05-30 10:48:12.616286	weekly
\.


--
-- TOC entry 3510 (class 0 OID 16483)
-- Dependencies: 228
-- Data for Name: models; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.models (id, category_id, display_name, is_active, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 3501 (class 0 OID 16391)
-- Dependencies: 219
-- Data for Name: tenants; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.tenants (id, name, created_at, updated_at) FROM stdin;
1	blinkit	2025-05-30 10:44:40.674999	2025-05-30 10:44:40.674999
\.


--
-- TOC entry 3520 (class 0 OID 16579)
-- Dependencies: 238
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.users (id, full_name, email, is_allowed, created_at) FROM stdin;
1	Shubham Gupta	<EMAIL>	t	2025-06-10 08:48:02.313414
\.


--
-- TOC entry 3537 (class 0 OID 0)
-- Dependencies: 222
-- Name: categories_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.categories_id_seq', 2, true);


--
-- TOC entry 3538 (class 0 OID 0)
-- Dependencies: 220
-- Name: entities_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.entities_id_seq', 1, true);


--
-- TOC entry 3539 (class 0 OID 0)
-- Dependencies: 229
-- Name: forecast_metrics_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.forecast_metrics_id_seq', 28, true);


--
-- TOC entry 3540 (class 0 OID 0)
-- Dependencies: 235
-- Name: forecast_model_entities_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.forecast_model_entities_id_seq', 1, false);


--
-- TOC entry 3541 (class 0 OID 0)
-- Dependencies: 231
-- Name: forecast_models_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.forecast_models_id_seq', 1, false);


--
-- TOC entry 3542 (class 0 OID 0)
-- Dependencies: 224
-- Name: forecasts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.forecasts_id_seq', 3, true);


--
-- TOC entry 3543 (class 0 OID 0)
-- Dependencies: 233
-- Name: inputs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.inputs_id_seq', 2, true);


--
-- TOC entry 3544 (class 0 OID 0)
-- Dependencies: 226
-- Name: metrics_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.metrics_id_seq', 203, true);


--
-- TOC entry 3545 (class 0 OID 0)
-- Dependencies: 218
-- Name: tenants_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.tenants_id_seq', 1, true);


--
-- TOC entry 3546 (class 0 OID 0)
-- Dependencies: 237
-- Name: users_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.users_id_seq', 1, true);


--
-- TOC entry 3306 (class 2606 OID 16389)
-- Name: alembic_version alembic_version_pkc; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.alembic_version
    ADD CONSTRAINT alembic_version_pkc PRIMARY KEY (version_num);


--
-- TOC entry 3312 (class 2606 OID 16427)
-- Name: categories categories_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.categories
    ADD CONSTRAINT categories_pkey PRIMARY KEY (id);


--
-- TOC entry 3310 (class 2606 OID 16411)
-- Name: entities entities_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.entities
    ADD CONSTRAINT entities_pkey PRIMARY KEY (id);


--
-- TOC entry 3320 (class 2606 OID 16505)
-- Name: forecast_metrics forecast_metrics_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_metrics
    ADD CONSTRAINT forecast_metrics_pkey PRIMARY KEY (id);


--
-- TOC entry 3330 (class 2606 OID 16565)
-- Name: forecast_model_entities forecast_model_entities_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_model_entities
    ADD CONSTRAINT forecast_model_entities_pkey PRIMARY KEY (id);


--
-- TOC entry 3324 (class 2606 OID 16528)
-- Name: forecast_models forecast_models_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_models
    ADD CONSTRAINT forecast_models_pkey PRIMARY KEY (id);


--
-- TOC entry 3314 (class 2606 OID 16443)
-- Name: forecasts forecasts_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecasts
    ADD CONSTRAINT forecasts_pkey PRIMARY KEY (id);


--
-- TOC entry 3339 (class 2606 OID 16609)
-- Name: input_values input_values_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.input_values
    ADD CONSTRAINT input_values_pkey PRIMARY KEY (input_id, metric_id);


--
-- TOC entry 3328 (class 2606 OID 16551)
-- Name: inputs inputs_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.inputs
    ADD CONSTRAINT inputs_pkey PRIMARY KEY (id);


--
-- TOC entry 3316 (class 2606 OID 16477)
-- Name: metrics metrics_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.metrics
    ADD CONSTRAINT metrics_pkey PRIMARY KEY (id);


--
-- TOC entry 3318 (class 2606 OID 16491)
-- Name: models models_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.models
    ADD CONSTRAINT models_pkey PRIMARY KEY (id);


--
-- TOC entry 3308 (class 2606 OID 16400)
-- Name: tenants tenants_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tenants
    ADD CONSTRAINT tenants_pkey PRIMARY KEY (id);


--
-- TOC entry 3322 (class 2606 OID 16507)
-- Name: forecast_metrics uq_forecast_metric; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_metrics
    ADD CONSTRAINT uq_forecast_metric UNIQUE (forecast_id, metric_id);


--
-- TOC entry 3326 (class 2606 OID 16530)
-- Name: forecast_models uq_forecast_model; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_models
    ADD CONSTRAINT uq_forecast_model UNIQUE (forecast_id, model_id);


--
-- TOC entry 3332 (class 2606 OID 16567)
-- Name: forecast_model_entities uq_forecast_model_entity; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_model_entities
    ADD CONSTRAINT uq_forecast_model_entity UNIQUE (forecast_models_id, entity_id, entity_value);


--
-- TOC entry 3337 (class 2606 OID 16588)
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- TOC entry 3333 (class 1259 OID 16589)
-- Name: ix_users_email; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX ix_users_email ON public.users USING btree (email);


--
-- TOC entry 3334 (class 1259 OID 16590)
-- Name: ix_users_full_name; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_users_full_name ON public.users USING btree (full_name);


--
-- TOC entry 3335 (class 1259 OID 16591)
-- Name: ix_users_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_users_id ON public.users USING btree (id);


--
-- TOC entry 3341 (class 2606 OID 16428)
-- Name: categories categories_entity_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.categories
    ADD CONSTRAINT categories_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES public.entities(id);


--
-- TOC entry 3340 (class 2606 OID 16412)
-- Name: entities entities_tenant_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.entities
    ADD CONSTRAINT entities_tenant_id_fkey FOREIGN KEY (tenant_id) REFERENCES public.tenants(id);


--
-- TOC entry 3345 (class 2606 OID 16508)
-- Name: forecast_metrics forecast_metrics_forecast_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_metrics
    ADD CONSTRAINT forecast_metrics_forecast_id_fkey FOREIGN KEY (forecast_id) REFERENCES public.forecasts(id);


--
-- TOC entry 3346 (class 2606 OID 16513)
-- Name: forecast_metrics forecast_metrics_metric_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_metrics
    ADD CONSTRAINT forecast_metrics_metric_id_fkey FOREIGN KEY (metric_id) REFERENCES public.metrics(id);


--
-- TOC entry 3350 (class 2606 OID 16568)
-- Name: forecast_model_entities forecast_model_entities_entity_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_model_entities
    ADD CONSTRAINT forecast_model_entities_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES public.entities(id);


--
-- TOC entry 3351 (class 2606 OID 16573)
-- Name: forecast_model_entities forecast_model_entities_forecast_models_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_model_entities
    ADD CONSTRAINT forecast_model_entities_forecast_models_id_fkey FOREIGN KEY (forecast_models_id) REFERENCES public.forecast_models(id);


--
-- TOC entry 3347 (class 2606 OID 16531)
-- Name: forecast_models forecast_models_forecast_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_models
    ADD CONSTRAINT forecast_models_forecast_id_fkey FOREIGN KEY (forecast_id) REFERENCES public.forecasts(id);


--
-- TOC entry 3348 (class 2606 OID 16536)
-- Name: forecast_models forecast_models_model_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_models
    ADD CONSTRAINT forecast_models_model_id_fkey FOREIGN KEY (model_id) REFERENCES public.models(id);


--
-- TOC entry 3342 (class 2606 OID 16444)
-- Name: forecasts forecasts_category_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecasts
    ADD CONSTRAINT forecasts_category_id_fkey FOREIGN KEY (category_id) REFERENCES public.categories(id);


--
-- TOC entry 3352 (class 2606 OID 16610)
-- Name: input_values input_values_input_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.input_values
    ADD CONSTRAINT input_values_input_id_fkey FOREIGN KEY (input_id) REFERENCES public.inputs(id);


--
-- TOC entry 3353 (class 2606 OID 16615)
-- Name: input_values input_values_metric_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.input_values
    ADD CONSTRAINT input_values_metric_id_fkey FOREIGN KEY (metric_id) REFERENCES public.metrics(id);


--
-- TOC entry 3349 (class 2606 OID 16552)
-- Name: inputs inputs_forecast_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.inputs
    ADD CONSTRAINT inputs_forecast_id_fkey FOREIGN KEY (forecast_id) REFERENCES public.forecasts(id);


--
-- TOC entry 3343 (class 2606 OID 16478)
-- Name: metrics metrics_category_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.metrics
    ADD CONSTRAINT metrics_category_id_fkey FOREIGN KEY (category_id) REFERENCES public.categories(id);


--
-- TOC entry 3344 (class 2606 OID 16492)
-- Name: models models_category_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.models
    ADD CONSTRAINT models_category_id_fkey FOREIGN KEY (category_id) REFERENCES public.categories(id);


-- Completed on 2025-06-20 09:39:56 UTC

--
-- PostgreSQL database dump complete
--

