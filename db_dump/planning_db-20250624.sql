--
-- PostgreSQL database dump
--

-- Dumped from database version 17.5 (Debian 17.5-1.pgdg120+1)
-- Dumped by pg_dump version 17.5 (Debian 17.5-1.pgdg120+1)

-- Started on 2025-06-24 08:49:40 UTC

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- TOC entry 908 (class 1247 OID 16593)
-- Name: inputstatus; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.inputstatus AS ENUM (
    'PENDING',
    'APPROVED',
    'REJECTED'
);


ALTER TYPE public.inputstatus OWNER TO postgres;

--
-- TOC entry 884 (class 1247 OID 16450)
-- Name: metrictype; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.metrictype AS ENUM (
    'COUNT',
    'AVERAGE',
    'PERCENTAGE',
    'SECONDS',
    'MINUTES',
    'HOURS',
    'RUPEES',
    'DAYS'
);


ALTER TYPE public.metrictype OWNER TO postgres;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- TOC entry 217 (class 1259 OID 16385)
-- Name: alembic_version; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.alembic_version (
    version_num character varying(32) NOT NULL
);


ALTER TABLE public.alembic_version OWNER TO postgres;

--
-- TOC entry 223 (class 1259 OID 16418)
-- Name: categories; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.categories (
    id integer NOT NULL,
    name character varying,
    entity_id integer,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public.categories OWNER TO postgres;

--
-- TOC entry 222 (class 1259 OID 16417)
-- Name: categories_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.categories_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.categories_id_seq OWNER TO postgres;

--
-- TOC entry 3536 (class 0 OID 0)
-- Dependencies: 222
-- Name: categories_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.categories_id_seq OWNED BY public.categories.id;


--
-- TOC entry 221 (class 1259 OID 16402)
-- Name: entities; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.entities (
    id integer NOT NULL,
    name character varying,
    tenant_id integer,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public.entities OWNER TO postgres;

--
-- TOC entry 220 (class 1259 OID 16401)
-- Name: entities_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.entities_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.entities_id_seq OWNER TO postgres;

--
-- TOC entry 3537 (class 0 OID 0)
-- Dependencies: 220
-- Name: entities_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.entities_id_seq OWNED BY public.entities.id;


--
-- TOC entry 230 (class 1259 OID 16498)
-- Name: forecast_metrics; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.forecast_metrics (
    id integer NOT NULL,
    forecast_id integer NOT NULL,
    metric_id integer NOT NULL,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now(),
    editable boolean NOT NULL
);


ALTER TABLE public.forecast_metrics OWNER TO postgres;

--
-- TOC entry 229 (class 1259 OID 16497)
-- Name: forecast_metrics_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.forecast_metrics_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.forecast_metrics_id_seq OWNER TO postgres;

--
-- TOC entry 3538 (class 0 OID 0)
-- Dependencies: 229
-- Name: forecast_metrics_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.forecast_metrics_id_seq OWNED BY public.forecast_metrics.id;


--
-- TOC entry 236 (class 1259 OID 16558)
-- Name: forecast_model_entities; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.forecast_model_entities (
    id integer NOT NULL,
    forecast_models_id integer NOT NULL,
    entity_id integer NOT NULL,
    entity_value integer NOT NULL,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public.forecast_model_entities OWNER TO postgres;

--
-- TOC entry 235 (class 1259 OID 16557)
-- Name: forecast_model_entities_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.forecast_model_entities_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.forecast_model_entities_id_seq OWNER TO postgres;

--
-- TOC entry 3539 (class 0 OID 0)
-- Dependencies: 235
-- Name: forecast_model_entities_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.forecast_model_entities_id_seq OWNED BY public.forecast_model_entities.id;


--
-- TOC entry 232 (class 1259 OID 16519)
-- Name: forecast_models; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.forecast_models (
    id integer NOT NULL,
    forecast_id integer NOT NULL,
    model_id character varying NOT NULL,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public.forecast_models OWNER TO postgres;

--
-- TOC entry 231 (class 1259 OID 16518)
-- Name: forecast_models_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.forecast_models_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.forecast_models_id_seq OWNER TO postgres;

--
-- TOC entry 3540 (class 0 OID 0)
-- Dependencies: 231
-- Name: forecast_models_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.forecast_models_id_seq OWNED BY public.forecast_models.id;


--
-- TOC entry 225 (class 1259 OID 16434)
-- Name: forecasts; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.forecasts (
    id integer NOT NULL,
    category_id integer,
    table_name character varying,
    display_name character varying,
    is_active boolean,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public.forecasts OWNER TO postgres;

--
-- TOC entry 224 (class 1259 OID 16433)
-- Name: forecasts_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.forecasts_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.forecasts_id_seq OWNER TO postgres;

--
-- TOC entry 3541 (class 0 OID 0)
-- Dependencies: 224
-- Name: forecasts_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.forecasts_id_seq OWNED BY public.forecasts.id;


--
-- TOC entry 239 (class 1259 OID 16605)
-- Name: input_values; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.input_values (
    input_id integer NOT NULL,
    metric_id integer NOT NULL,
    value double precision NOT NULL
);


ALTER TABLE public.input_values OWNER TO postgres;

--
-- TOC entry 234 (class 1259 OID 16542)
-- Name: inputs; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.inputs (
    id integer NOT NULL,
    forecast_id integer NOT NULL,
    time_grain character varying(255) NOT NULL,
    start_time timestamp without time zone NOT NULL,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now(),
    is_approved public.inputstatus NOT NULL,
    entity_id integer NOT NULL
);


ALTER TABLE public.inputs OWNER TO postgres;

--
-- TOC entry 233 (class 1259 OID 16541)
-- Name: inputs_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.inputs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.inputs_id_seq OWNER TO postgres;

--
-- TOC entry 3542 (class 0 OID 0)
-- Dependencies: 233
-- Name: inputs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.inputs_id_seq OWNED BY public.inputs.id;


--
-- TOC entry 240 (class 1259 OID 16620)
-- Name: metric_dependencies; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.metric_dependencies (
    dependent_metric_id integer NOT NULL,
    dependency_metric_id integer NOT NULL
);


ALTER TABLE public.metric_dependencies OWNER TO postgres;

--
-- TOC entry 227 (class 1259 OID 16468)
-- Name: metrics; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.metrics (
    id integer NOT NULL,
    category_id integer,
    select_query character varying,
    aggregate_query character varying,
    name character varying,
    display_name character varying,
    metric_type public.metrictype,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now(),
    grain character varying
);


ALTER TABLE public.metrics OWNER TO postgres;

--
-- TOC entry 226 (class 1259 OID 16467)
-- Name: metrics_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.metrics_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.metrics_id_seq OWNER TO postgres;

--
-- TOC entry 3543 (class 0 OID 0)
-- Dependencies: 226
-- Name: metrics_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.metrics_id_seq OWNED BY public.metrics.id;


--
-- TOC entry 228 (class 1259 OID 16483)
-- Name: models; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.models (
    id character varying NOT NULL,
    category_id integer,
    display_name character varying,
    is_active boolean,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public.models OWNER TO postgres;

--
-- TOC entry 219 (class 1259 OID 16391)
-- Name: tenants; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.tenants (
    id integer NOT NULL,
    name character varying,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public.tenants OWNER TO postgres;

--
-- TOC entry 218 (class 1259 OID 16390)
-- Name: tenants_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.tenants_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.tenants_id_seq OWNER TO postgres;

--
-- TOC entry 3544 (class 0 OID 0)
-- Dependencies: 218
-- Name: tenants_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.tenants_id_seq OWNED BY public.tenants.id;


--
-- TOC entry 238 (class 1259 OID 16579)
-- Name: users; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.users (
    id integer NOT NULL,
    full_name character varying,
    email character varying NOT NULL,
    is_allowed boolean DEFAULT false,
    created_at timestamp without time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.users OWNER TO postgres;

--
-- TOC entry 237 (class 1259 OID 16578)
-- Name: users_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.users_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.users_id_seq OWNER TO postgres;

--
-- TOC entry 3545 (class 0 OID 0)
-- Dependencies: 237
-- Name: users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.users_id_seq OWNED BY public.users.id;


--
-- TOC entry 3283 (class 2604 OID 16421)
-- Name: categories id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.categories ALTER COLUMN id SET DEFAULT nextval('public.categories_id_seq'::regclass);


--
-- TOC entry 3280 (class 2604 OID 16405)
-- Name: entities id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.entities ALTER COLUMN id SET DEFAULT nextval('public.entities_id_seq'::regclass);


--
-- TOC entry 3294 (class 2604 OID 16501)
-- Name: forecast_metrics id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_metrics ALTER COLUMN id SET DEFAULT nextval('public.forecast_metrics_id_seq'::regclass);


--
-- TOC entry 3303 (class 2604 OID 16561)
-- Name: forecast_model_entities id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_model_entities ALTER COLUMN id SET DEFAULT nextval('public.forecast_model_entities_id_seq'::regclass);


--
-- TOC entry 3297 (class 2604 OID 16522)
-- Name: forecast_models id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_models ALTER COLUMN id SET DEFAULT nextval('public.forecast_models_id_seq'::regclass);


--
-- TOC entry 3286 (class 2604 OID 16437)
-- Name: forecasts id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecasts ALTER COLUMN id SET DEFAULT nextval('public.forecasts_id_seq'::regclass);


--
-- TOC entry 3300 (class 2604 OID 16545)
-- Name: inputs id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.inputs ALTER COLUMN id SET DEFAULT nextval('public.inputs_id_seq'::regclass);


--
-- TOC entry 3289 (class 2604 OID 16471)
-- Name: metrics id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.metrics ALTER COLUMN id SET DEFAULT nextval('public.metrics_id_seq'::regclass);


--
-- TOC entry 3277 (class 2604 OID 16394)
-- Name: tenants id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tenants ALTER COLUMN id SET DEFAULT nextval('public.tenants_id_seq'::regclass);


--
-- TOC entry 3306 (class 2604 OID 16582)
-- Name: users id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users ALTER COLUMN id SET DEFAULT nextval('public.users_id_seq'::regclass);


--
-- TOC entry 3507 (class 0 OID 16385)
-- Dependencies: 217
-- Data for Name: alembic_version; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.alembic_version (version_num) FROM stdin;
c97a23ce502c
\.


--
-- TOC entry 3513 (class 0 OID 16418)
-- Dependencies: 223
-- Data for Name: categories; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.categories (id, name, entity_id, created_at, updated_at) FROM stdin;
10	instore	1	2025-06-24 08:19:20.375826	2025-06-24 08:19:20.375826
11	core	1	2025-06-24 08:19:21.323121	2025-06-24 08:19:21.323121
\.


--
-- TOC entry 3511 (class 0 OID 16402)
-- Dependencies: 221
-- Data for Name: entities; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.entities (id, name, tenant_id, created_at, updated_at) FROM stdin;
1	storeops	\N	2025-06-24 07:14:14.782148	2025-06-24 07:14:14.782148
\.


--
-- TOC entry 3520 (class 0 OID 16498)
-- Dependencies: 230
-- Data for Name: forecast_metrics; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.forecast_metrics (id, forecast_id, metric_id, created_at, updated_at, editable) FROM stdin;
13	6	226	2025-06-24 08:19:21.300242	2025-06-24 08:19:21.300242	t
14	6	227	2025-06-24 08:19:21.300242	2025-06-24 08:19:21.300242	t
15	6	230	2025-06-24 08:19:21.300242	2025-06-24 08:19:21.300242	t
16	6	231	2025-06-24 08:19:21.300242	2025-06-24 08:19:21.300242	t
17	6	232	2025-06-24 08:19:21.300242	2025-06-24 08:19:21.300242	t
18	6	233	2025-06-24 08:19:21.300242	2025-06-24 08:19:21.300242	t
19	6	255	2025-06-24 08:19:21.300242	2025-06-24 08:19:21.300242	t
20	6	256	2025-06-24 08:19:21.300242	2025-06-24 08:19:21.300242	t
21	6	258	2025-06-24 08:19:21.300242	2025-06-24 08:19:21.300242	t
22	6	259	2025-06-24 08:19:21.300242	2025-06-24 08:19:21.300242	t
23	6	260	2025-06-24 08:19:21.300242	2025-06-24 08:19:21.300242	t
24	6	262	2025-06-24 08:19:21.300242	2025-06-24 08:19:21.300242	t
25	7	263	2025-06-24 08:19:21.384508	2025-06-24 08:19:21.384508	t
26	7	264	2025-06-24 08:19:21.384508	2025-06-24 08:19:21.384508	t
27	7	265	2025-06-24 08:19:21.384508	2025-06-24 08:19:21.384508	t
28	7	266	2025-06-24 08:19:21.384508	2025-06-24 08:19:21.384508	t
\.


--
-- TOC entry 3526 (class 0 OID 16558)
-- Dependencies: 236
-- Data for Name: forecast_model_entities; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.forecast_model_entities (id, forecast_models_id, entity_id, entity_value, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 3522 (class 0 OID 16519)
-- Dependencies: 232
-- Data for Name: forecast_models; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.forecast_models (id, forecast_id, model_id, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 3515 (class 0 OID 16434)
-- Dependencies: 225
-- Data for Name: forecasts; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.forecasts (id, category_id, table_name, display_name, is_active, created_at, updated_at) FROM stdin;
6	10	\N	instore forecast	t	2025-06-24 08:19:21.300242	2025-06-24 08:19:21.300242
7	11	\N	core forecast	t	2025-06-24 08:19:21.384508	2025-06-24 08:19:21.384508
\.


--
-- TOC entry 3529 (class 0 OID 16605)
-- Dependencies: 239
-- Data for Name: input_values; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.input_values (input_id, metric_id, value) FROM stdin;
\.


--
-- TOC entry 3524 (class 0 OID 16542)
-- Dependencies: 234
-- Data for Name: inputs; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.inputs (id, forecast_id, time_grain, start_time, created_at, updated_at, is_approved, entity_id) FROM stdin;
\.


--
-- TOC entry 3530 (class 0 OID 16620)
-- Dependencies: 240
-- Data for Name: metric_dependencies; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.metric_dependencies (dependent_metric_id, dependency_metric_id) FROM stdin;
217	214
217	215
220	219
220	216
223	221
223	222
235	234
235	229
240	239
240	218
241	238
241	236
251	250
251	215
255	242
255	243
256	247
256	244
257	239
257	225
257	252
257	218
258	254
258	253
261	253
261	241
262	261
262	253
266	264
266	263
\.


--
-- TOC entry 3517 (class 0 OID 16468)
-- Dependencies: 227
-- Data for Name: metrics; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.metrics (id, category_id, select_query, aggregate_query, name, display_name, metric_type, created_at, updated_at, grain) FROM stdin;
213	10	auditor_active_time_mins	SUM(auditor_active_time_mins)	auditor_active_time_mins	Auditor Active Time	MINUTES	2025-06-24 08:19:20.414856	2025-06-24 08:19:20.414856	
214	10	c2a_within_10_orders	SUM(c2a_within_10_orders)	c2a_within_10_orders	C2A Within 10 Orders	COUNT	2025-06-24 08:19:20.444408	2025-06-24 08:19:20.444408	
215	10	orders	SUM(orders)	orders	Total Orders	COUNT	2025-06-24 08:19:20.464869	2025-06-24 08:19:20.464869	
216	10	instore_orders	SUM(instore_orders)	instore_orders	Instore Orders	COUNT	2025-06-24 08:19:20.480466	2025-06-24 08:19:20.480466	
217	10	c2a_within_10_sec	CAST(SUM(c2a_within_10_orders) AS DOUBLE) * 100 / NULLIF(SUM(orders), 0)	c2a_within_10_sec	C2A % under 10 sec	PERCENTAGE	2025-06-24 08:19:20.49657	2025-06-24 08:19:20.49657	
218	10	carts	SUM(carts)	carts	Total Carts	COUNT	2025-06-24 08:19:20.511786	2025-06-24 08:19:20.511786	
219	10	direct_handover_orders	SUM(direct_handover_orders)	direct_handover_orders	Direct Handover Orders	COUNT	2025-06-24 08:19:20.526168	2025-06-24 08:19:20.526168	
220	10	dh_pct	CAST(SUM(direct_handover_orders) AS DOUBLE) * 100.0 / NULLIF(SUM(instore_orders), 0)	dh_pct	DH %	PERCENTAGE	2025-06-24 08:19:20.540162	2025-06-24 08:19:20.540162	
221	10	fixed_employee_absent_count	SUM(fixed_employee_absent_count)	fixed_employee_absent_count	Fixed Employee Absent Count	COUNT	2025-06-24 08:19:20.560657	2025-06-24 08:19:20.560657	
222	10	planned_employee_count	SUM(planned_employee_count)	planned_employee_count	Planned Employee Count	COUNT	2025-06-24 08:19:20.576362	2025-06-24 08:19:20.576362	
223	10	fixed_absent_pct	CAST(SUM(fixed_employee_absent_count) AS DOUBLE) * 100 / NULLIF(SUM(planned_employee_count), 0)	fixed_absent_pct	Fixed Absenteeism %	PERCENTAGE	2025-06-24 08:19:20.592227	2025-06-24 08:19:20.592227	
224	10	manhours_fnv	SUM(manhours_fnv)	manhours_fnv	Fnv Manhours	HOURS	2025-06-24 08:19:20.607189	2025-06-24 08:19:20.607189	
225	10	gsp_surge_carts	SUM(gsp_surge_carts)	gsp_surge_carts	GSP Surge Carts	COUNT	2025-06-24 08:19:20.623202	2025-06-24 08:19:20.623202	
226	10	headcount	SUM(headcount)	headcount	Headcount	COUNT	2025-06-24 08:19:20.638988	2025-06-24 08:19:20.638988	weekly,daily
227	10	headcount_shift_start	SUM(headcount_shift_start)	headcount_shift_start	Headcount Shift Start	COUNT	2025-06-24 08:19:20.654942	2025-06-24 08:19:20.654942	hourly
228	10	login_hours	SUM(login_hours)	login_hours	Total login hours	HOURS	2025-06-24 08:19:20.67088	2025-06-24 08:19:20.67088	
229	10	login_mins	SUM(login_mins)	login_mins	Total Login Minutes	MINUTES	2025-06-24 08:19:20.686525	2025-06-24 08:19:20.686525	
230	10	manhours_fixed	SUM(manhours_fixed)	manhours_fixed	Fixed Manhours	HOURS	2025-06-24 08:19:20.707579	2025-06-24 08:19:20.707579	hourly
231	10	manhours_od	SUM(manhours_od)	manhours_od	OD Manhours	HOURS	2025-06-24 08:19:20.719306	2025-06-24 08:19:20.719306	daily,hourly
232	10	manhours_picking	SUM(manhours_picking)	manhours_picking	Picking Manhours	HOURS	2025-06-24 08:19:20.732554	2025-06-24 08:19:20.732554	hourly
233	10	manhours_putaway	SUM(manhours_putaway)	manhours_putaway	Putaway Manhours	HOURS	2025-06-24 08:19:20.747697	2025-06-24 08:19:20.747697	hourly
234	10	total_payout	SUM(total_payout)	total_payout	Total Payout	RUPEES	2025-06-24 08:19:20.760779	2025-06-24 08:19:20.760779	
235	10	od_eph	CAST(SUM(total_payout) AS DOUBLE) * 100 / NULLIF(SUM(login_mins), 0)	od_eph	OD Slot level EPH	PERCENTAGE	2025-06-24 08:19:20.776283	2025-06-24 08:19:20.776283	
236	10	picker_active_time_fixed_sec	SUM(picker_active_time_fixed_sec)	picker_active_time_fixed_sec	Picker Active Time Fixed (Sec)	SECONDS	2025-06-24 08:19:20.790767	2025-06-24 08:19:20.790767	
237	10	picker_active_time_mins	SUM(picker_active_time_mins)	picker_active_time_mins	Picker Active Time	MINUTES	2025-06-24 08:19:20.805995	2025-06-24 08:19:20.805995	
238	10	picker_busy_time_fixed_sec	SUM(picker_busy_time_fixed_sec)	picker_busy_time_fixed_sec	Picker Busy Time Fixed (Sec)	SECONDS	2025-06-24 08:19:20.820923	2025-06-24 08:19:20.820923	
239	10	picker_surge_carts	SUM(picker_surge_carts)	picker_surge_carts	Picker Surge Carts	COUNT	2025-06-24 08:19:20.835777	2025-06-24 08:19:20.835777	
240	10	picker_surge_pct	CAST(SUM(picker_surge_carts) AS DOUBLE) * 100.0 / NULLIF(SUM(carts), 0)	picker_surge_pct	Picker Surge %	PERCENTAGE	2025-06-24 08:19:20.852778	2025-06-24 08:19:20.852778	
241	10	picker_utilization_fixed	CAST(SUM(picker_busy_time_fixed_sec) AS DOUBLE) * 100 / NULLIF(SUM(picker_active_time_fixed_sec), 0)	picker_utilization_fixed	Fixed Picker Utilization	PERCENTAGE	2025-06-24 08:19:20.868767	2025-06-24 08:19:20.868767	
242	10	putaway_qty	SUM(putaway_qty)	putaway_qty	Putaway Quantity	COUNT	2025-06-24 08:19:20.882412	2025-06-24 08:19:20.882412	
243	10	putter_active_time_mins	SUM(putter_active_time_mins)	putter_active_time_mins	Putter Active Time	MINUTES	2025-06-24 08:19:20.893424	2025-06-24 08:19:20.893424	
244	10	qty_ordered_fixed	SUM(qty_ordered_fixed)	qty_ordered_fixed	Quantity Ordered Fixed	COUNT	2025-06-24 08:19:20.912472	2025-06-24 08:19:20.912472	
245	10	qty_ordered_new_employee	SUM(qty_ordered_new_employee)	qty_ordered_new_employee	Quantity Ordered New Employee	COUNT	2025-06-24 08:19:20.927793	2025-06-24 08:19:20.927793	
246	10	qty_ordered_new_employee_od	SUM(qty_ordered_new_employee_od)	qty_ordered_new_employee_od	Quantity Ordered New Employee OD	COUNT	2025-06-24 08:19:20.942806	2025-06-24 08:19:20.942806	
247	10	qty_ordered_od	SUM(qty_ordered_od)	qty_ordered_od	Quantity Ordered OD	COUNT	2025-06-24 08:19:20.953192	2025-06-24 08:19:20.953192	
248	10	qty_ordered_old_employee	SUM(qty_ordered_old_employee)	qty_ordered_old_employee	Quantity Ordered Old Employee	COUNT	2025-06-24 08:19:20.964884	2025-06-24 08:19:20.964884	
249	10	qty_ordered_old_employee_od	SUM(qty_ordered_old_employee_od)	qty_ordered_old_employee_od	Quantity Ordered Old Employee OD	COUNT	2025-06-24 08:19:20.980428	2025-06-24 08:19:20.980428	
250	10	r2a_within_10_orders	SUM(r2a_within_10_orders)	r2a_within_10_orders	R2A Within 10 Orders	COUNT	2025-06-24 08:19:20.992036	2025-06-24 08:19:20.992036	
251	10	r2a_within_10_sec	CAST(SUM(r2a_within_10_orders) AS DOUBLE) * 100 / NULLIF(SUM(orders), 0)	r2a_within_10_sec	R2A % under 10 sec	PERCENTAGE	2025-06-24 08:19:21.005513	2025-06-24 08:19:21.005513	
252	10	rain_surge_carts	SUM(rain_surge_carts)	rain_surge_carts	Rain Surge Carts	COUNT	2025-06-24 08:19:21.018678	2025-06-24 08:19:21.018678	
253	10	total_items_quantity_ordered	SUM(total_items_quantity_ordered)	total_items_quantity_ordered	Total quantity ordered	COUNT	2025-06-24 08:19:21.029652	2025-06-24 08:19:21.029652	
254	10	total_picking_time_sec	SUM(total_picking_time_sec)	total_picking_time_sec	Total Picking Time (Sec)	SECONDS	2025-06-24 08:19:21.040554	2025-06-24 08:19:21.040554	
255	10	items_put_away_per_hour	CAST(SUM(putaway_qty) AS FLOAT) / NULLIF(CAST(SUM(putter_active_time_mins) AS FLOAT) / 60, 0)	items_put_away_per_hour	IPH	COUNT	2025-06-24 08:19:21.0538	2025-06-24 08:19:21.0538	weekly
256	10	od_contribution_pct	SUM(qty_ordered_od)/SUM(qty_ordered_fixed)	od_contribution_pct	% OD Contribution	PERCENTAGE	2025-06-24 08:19:21.20851	2025-06-24 08:19:21.20851	weekly
257	10	overall_surge_pct	CAST(SUM(picker_surge_carts + gsp_surge_carts + rain_surge_carts) AS DOUBLE) * 100.0 / NULLIF(SUM(carts), 0)	overall_surge_pct	Overall Surge %	PERCENTAGE	2025-06-24 08:19:21.223275	2025-06-24 08:19:21.223275	
258	10	ppi	SUM(total_picking_time_sec)/SUM(total_items_quantity_ordered)	ppi	PPI	SECONDS	2025-06-24 08:19:21.237296	2025-06-24 08:19:21.237296	weekly
259	10	mandays_fixed	SUM(mandays_fixed)	mandays_fixed	Mandays Fixed	COUNT	2025-06-24 08:19:21.252177	2025-06-24 08:19:21.252177	daily
260	10	headcount	SUM(headcount)	headcount	Head Count	COUNT	2025-06-24 08:19:21.263118	2025-06-24 08:19:21.263118	daily,weekly
261	10	picker_util_numerator_fixed	SUM(total_items_quantity_ordered*picker_utilization_fixed)	picker_util_numerator_fixed	Picker utilisation numerator fixed	COUNT	2025-06-24 08:19:21.274552	2025-06-24 08:19:21.274552	
262	10	picker_util_fixed_wt_avg	SUM(picker_util_numerator_fixed/total_items_quantity_ordered)	picker_util_fixed_wt_avg	Picker Util Fixed	AVERAGE	2025-06-24 08:19:21.287884	2025-06-24 08:19:21.287884	weekly
263	11	instore_orders	SUM(instore_orders)	instore_orders	Orders placed	COUNT	2025-06-24 08:19:21.335632	2025-06-24 08:19:21.335632	weekly
264	11	total_items_quantity_ordered	SUM(total_items_quantity_ordered)	total_items_quantity_ordered	Items Quantity Ordered	COUNT	2025-06-24 08:19:21.347695	2025-06-24 08:19:21.347695	weekly
265	11	total_quantity_indent	SUM(total_quantity_indent)	total_quantity_indent	Total Quantity Indent	COUNT	2025-06-24 08:19:21.359429	2025-06-24 08:19:21.359429	weekly
266	11	items_per_order	CAST(SUM(total_items_quantity_ordered) AS DOUBLE) / NULLIF(SUM(instore_orders), 0)	items_per_order	Avg. number of items per order	COUNT	2025-06-24 08:19:21.374115	2025-06-24 08:19:21.374115	weekly
\.


--
-- TOC entry 3518 (class 0 OID 16483)
-- Dependencies: 228
-- Data for Name: models; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.models (id, category_id, display_name, is_active, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 3509 (class 0 OID 16391)
-- Dependencies: 219
-- Data for Name: tenants; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.tenants (id, name, created_at, updated_at) FROM stdin;
1	blinkit	2025-06-24 07:14:06.544781	2025-06-24 07:14:06.544781
\.


--
-- TOC entry 3528 (class 0 OID 16579)
-- Dependencies: 238
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.users (id, full_name, email, is_allowed, created_at) FROM stdin;
\.


--
-- TOC entry 3546 (class 0 OID 0)
-- Dependencies: 222
-- Name: categories_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.categories_id_seq', 11, true);


--
-- TOC entry 3547 (class 0 OID 0)
-- Dependencies: 220
-- Name: entities_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.entities_id_seq', 1, true);


--
-- TOC entry 3548 (class 0 OID 0)
-- Dependencies: 229
-- Name: forecast_metrics_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.forecast_metrics_id_seq', 28, true);


--
-- TOC entry 3549 (class 0 OID 0)
-- Dependencies: 235
-- Name: forecast_model_entities_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.forecast_model_entities_id_seq', 1, false);


--
-- TOC entry 3550 (class 0 OID 0)
-- Dependencies: 231
-- Name: forecast_models_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.forecast_models_id_seq', 1, false);


--
-- TOC entry 3551 (class 0 OID 0)
-- Dependencies: 224
-- Name: forecasts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.forecasts_id_seq', 7, true);


--
-- TOC entry 3552 (class 0 OID 0)
-- Dependencies: 233
-- Name: inputs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.inputs_id_seq', 1, false);


--
-- TOC entry 3553 (class 0 OID 0)
-- Dependencies: 226
-- Name: metrics_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.metrics_id_seq', 266, true);


--
-- TOC entry 3554 (class 0 OID 0)
-- Dependencies: 218
-- Name: tenants_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.tenants_id_seq', 1, true);


--
-- TOC entry 3555 (class 0 OID 0)
-- Dependencies: 237
-- Name: users_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.users_id_seq', 1, false);


--
-- TOC entry 3310 (class 2606 OID 16389)
-- Name: alembic_version alembic_version_pkc; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.alembic_version
    ADD CONSTRAINT alembic_version_pkc PRIMARY KEY (version_num);


--
-- TOC entry 3316 (class 2606 OID 16427)
-- Name: categories categories_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.categories
    ADD CONSTRAINT categories_pkey PRIMARY KEY (id);


--
-- TOC entry 3314 (class 2606 OID 16411)
-- Name: entities entities_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.entities
    ADD CONSTRAINT entities_pkey PRIMARY KEY (id);


--
-- TOC entry 3324 (class 2606 OID 16505)
-- Name: forecast_metrics forecast_metrics_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_metrics
    ADD CONSTRAINT forecast_metrics_pkey PRIMARY KEY (id);


--
-- TOC entry 3334 (class 2606 OID 16565)
-- Name: forecast_model_entities forecast_model_entities_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_model_entities
    ADD CONSTRAINT forecast_model_entities_pkey PRIMARY KEY (id);


--
-- TOC entry 3328 (class 2606 OID 16528)
-- Name: forecast_models forecast_models_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_models
    ADD CONSTRAINT forecast_models_pkey PRIMARY KEY (id);


--
-- TOC entry 3318 (class 2606 OID 16443)
-- Name: forecasts forecasts_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecasts
    ADD CONSTRAINT forecasts_pkey PRIMARY KEY (id);


--
-- TOC entry 3343 (class 2606 OID 16609)
-- Name: input_values input_values_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.input_values
    ADD CONSTRAINT input_values_pkey PRIMARY KEY (input_id, metric_id);


--
-- TOC entry 3332 (class 2606 OID 16551)
-- Name: inputs inputs_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.inputs
    ADD CONSTRAINT inputs_pkey PRIMARY KEY (id);


--
-- TOC entry 3345 (class 2606 OID 16624)
-- Name: metric_dependencies metric_dependencies_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.metric_dependencies
    ADD CONSTRAINT metric_dependencies_pkey PRIMARY KEY (dependent_metric_id, dependency_metric_id);


--
-- TOC entry 3320 (class 2606 OID 16477)
-- Name: metrics metrics_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.metrics
    ADD CONSTRAINT metrics_pkey PRIMARY KEY (id);


--
-- TOC entry 3322 (class 2606 OID 16491)
-- Name: models models_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.models
    ADD CONSTRAINT models_pkey PRIMARY KEY (id);


--
-- TOC entry 3312 (class 2606 OID 16400)
-- Name: tenants tenants_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tenants
    ADD CONSTRAINT tenants_pkey PRIMARY KEY (id);


--
-- TOC entry 3326 (class 2606 OID 16507)
-- Name: forecast_metrics uq_forecast_metric; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_metrics
    ADD CONSTRAINT uq_forecast_metric UNIQUE (forecast_id, metric_id);


--
-- TOC entry 3330 (class 2606 OID 16530)
-- Name: forecast_models uq_forecast_model; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_models
    ADD CONSTRAINT uq_forecast_model UNIQUE (forecast_id, model_id);


--
-- TOC entry 3336 (class 2606 OID 16567)
-- Name: forecast_model_entities uq_forecast_model_entity; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_model_entities
    ADD CONSTRAINT uq_forecast_model_entity UNIQUE (forecast_models_id, entity_id, entity_value);


--
-- TOC entry 3341 (class 2606 OID 16588)
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- TOC entry 3337 (class 1259 OID 16589)
-- Name: ix_users_email; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX ix_users_email ON public.users USING btree (email);


--
-- TOC entry 3338 (class 1259 OID 16590)
-- Name: ix_users_full_name; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_users_full_name ON public.users USING btree (full_name);


--
-- TOC entry 3339 (class 1259 OID 16591)
-- Name: ix_users_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_users_id ON public.users USING btree (id);


--
-- TOC entry 3347 (class 2606 OID 16428)
-- Name: categories categories_entity_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.categories
    ADD CONSTRAINT categories_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES public.entities(id);


--
-- TOC entry 3346 (class 2606 OID 16412)
-- Name: entities entities_tenant_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.entities
    ADD CONSTRAINT entities_tenant_id_fkey FOREIGN KEY (tenant_id) REFERENCES public.tenants(id);


--
-- TOC entry 3351 (class 2606 OID 16508)
-- Name: forecast_metrics forecast_metrics_forecast_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_metrics
    ADD CONSTRAINT forecast_metrics_forecast_id_fkey FOREIGN KEY (forecast_id) REFERENCES public.forecasts(id);


--
-- TOC entry 3352 (class 2606 OID 16513)
-- Name: forecast_metrics forecast_metrics_metric_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_metrics
    ADD CONSTRAINT forecast_metrics_metric_id_fkey FOREIGN KEY (metric_id) REFERENCES public.metrics(id);


--
-- TOC entry 3356 (class 2606 OID 16568)
-- Name: forecast_model_entities forecast_model_entities_entity_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_model_entities
    ADD CONSTRAINT forecast_model_entities_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES public.entities(id);


--
-- TOC entry 3357 (class 2606 OID 16573)
-- Name: forecast_model_entities forecast_model_entities_forecast_models_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_model_entities
    ADD CONSTRAINT forecast_model_entities_forecast_models_id_fkey FOREIGN KEY (forecast_models_id) REFERENCES public.forecast_models(id);


--
-- TOC entry 3353 (class 2606 OID 16531)
-- Name: forecast_models forecast_models_forecast_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_models
    ADD CONSTRAINT forecast_models_forecast_id_fkey FOREIGN KEY (forecast_id) REFERENCES public.forecasts(id);


--
-- TOC entry 3354 (class 2606 OID 16536)
-- Name: forecast_models forecast_models_model_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_models
    ADD CONSTRAINT forecast_models_model_id_fkey FOREIGN KEY (model_id) REFERENCES public.models(id);


--
-- TOC entry 3348 (class 2606 OID 16444)
-- Name: forecasts forecasts_category_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecasts
    ADD CONSTRAINT forecasts_category_id_fkey FOREIGN KEY (category_id) REFERENCES public.categories(id);


--
-- TOC entry 3358 (class 2606 OID 16610)
-- Name: input_values input_values_input_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.input_values
    ADD CONSTRAINT input_values_input_id_fkey FOREIGN KEY (input_id) REFERENCES public.inputs(id);


--
-- TOC entry 3359 (class 2606 OID 16615)
-- Name: input_values input_values_metric_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.input_values
    ADD CONSTRAINT input_values_metric_id_fkey FOREIGN KEY (metric_id) REFERENCES public.metrics(id);


--
-- TOC entry 3355 (class 2606 OID 16552)
-- Name: inputs inputs_forecast_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.inputs
    ADD CONSTRAINT inputs_forecast_id_fkey FOREIGN KEY (forecast_id) REFERENCES public.forecasts(id);


--
-- TOC entry 3360 (class 2606 OID 16625)
-- Name: metric_dependencies metric_dependencies_dependency_metric_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.metric_dependencies
    ADD CONSTRAINT metric_dependencies_dependency_metric_id_fkey FOREIGN KEY (dependency_metric_id) REFERENCES public.metrics(id);


--
-- TOC entry 3361 (class 2606 OID 16630)
-- Name: metric_dependencies metric_dependencies_dependent_metric_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.metric_dependencies
    ADD CONSTRAINT metric_dependencies_dependent_metric_id_fkey FOREIGN KEY (dependent_metric_id) REFERENCES public.metrics(id);


--
-- TOC entry 3349 (class 2606 OID 16478)
-- Name: metrics metrics_category_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.metrics
    ADD CONSTRAINT metrics_category_id_fkey FOREIGN KEY (category_id) REFERENCES public.categories(id);


--
-- TOC entry 3350 (class 2606 OID 16492)
-- Name: models models_category_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.models
    ADD CONSTRAINT models_category_id_fkey FOREIGN KEY (category_id) REFERENCES public.categories(id);


-- Completed on 2025-06-24 08:49:40 UTC

--
-- PostgreSQL database dump complete
--

