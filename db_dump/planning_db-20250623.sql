--
-- PostgreSQL database dump
--

-- Dumped from database version 17.5 (Debian 17.5-1.pgdg120+1)
-- Dumped by pg_dump version 17.5 (Debian 17.5-1.pgdg120+1)

-- Started on 2025-06-23 10:41:22 UTC

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- TOC entry 908 (class 1247 OID 16601)
-- Name: inputstatus; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.inputstatus AS ENUM (
    'PENDING',
    'APPROVED',
    'REJECTED'
);


ALTER TYPE public.inputstatus OWNER TO postgres;

--
-- TOC entry 884 (class 1247 OID 16459)
-- Name: metrictype; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public.metrictype AS ENUM (
    'COUNT',
    'AVERAGE',
    'PERCENTAGE',
    'SECONDS',
    'MINUTES',
    'HOURS',
    'RUPEES',
    'DAYS'
);


ALTER TYPE public.metrictype OWNER TO postgres;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- TOC entry 217 (class 1259 OID 16394)
-- Name: alembic_version; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.alembic_version (
    version_num character varying(32) NOT NULL
);


ALTER TABLE public.alembic_version OWNER TO postgres;

--
-- TOC entry 223 (class 1259 OID 16427)
-- Name: categories; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.categories (
    id integer NOT NULL,
    name character varying,
    entity_id integer,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public.categories OWNER TO postgres;

--
-- TOC entry 222 (class 1259 OID 16426)
-- Name: categories_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.categories_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.categories_id_seq OWNER TO postgres;

--
-- TOC entry 3536 (class 0 OID 0)
-- Dependencies: 222
-- Name: categories_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.categories_id_seq OWNED BY public.categories.id;


--
-- TOC entry 221 (class 1259 OID 16411)
-- Name: entities; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.entities (
    id integer NOT NULL,
    name character varying,
    tenant_id integer,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public.entities OWNER TO postgres;

--
-- TOC entry 220 (class 1259 OID 16410)
-- Name: entities_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.entities_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.entities_id_seq OWNER TO postgres;

--
-- TOC entry 3537 (class 0 OID 0)
-- Dependencies: 220
-- Name: entities_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.entities_id_seq OWNED BY public.entities.id;


--
-- TOC entry 230 (class 1259 OID 16506)
-- Name: forecast_metrics; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.forecast_metrics (
    id integer NOT NULL,
    forecast_id integer NOT NULL,
    metric_id integer NOT NULL,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now(),
    editable boolean NOT NULL
);


ALTER TABLE public.forecast_metrics OWNER TO postgres;

--
-- TOC entry 229 (class 1259 OID 16505)
-- Name: forecast_metrics_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.forecast_metrics_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.forecast_metrics_id_seq OWNER TO postgres;

--
-- TOC entry 3538 (class 0 OID 0)
-- Dependencies: 229
-- Name: forecast_metrics_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.forecast_metrics_id_seq OWNED BY public.forecast_metrics.id;


--
-- TOC entry 236 (class 1259 OID 16566)
-- Name: forecast_model_entities; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.forecast_model_entities (
    id integer NOT NULL,
    forecast_models_id integer NOT NULL,
    entity_id integer NOT NULL,
    entity_value integer NOT NULL,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public.forecast_model_entities OWNER TO postgres;

--
-- TOC entry 235 (class 1259 OID 16565)
-- Name: forecast_model_entities_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.forecast_model_entities_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.forecast_model_entities_id_seq OWNER TO postgres;

--
-- TOC entry 3539 (class 0 OID 0)
-- Dependencies: 235
-- Name: forecast_model_entities_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.forecast_model_entities_id_seq OWNED BY public.forecast_model_entities.id;


--
-- TOC entry 232 (class 1259 OID 16527)
-- Name: forecast_models; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.forecast_models (
    id integer NOT NULL,
    forecast_id integer NOT NULL,
    model_id character varying NOT NULL,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public.forecast_models OWNER TO postgres;

--
-- TOC entry 231 (class 1259 OID 16526)
-- Name: forecast_models_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.forecast_models_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.forecast_models_id_seq OWNER TO postgres;

--
-- TOC entry 3540 (class 0 OID 0)
-- Dependencies: 231
-- Name: forecast_models_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.forecast_models_id_seq OWNED BY public.forecast_models.id;


--
-- TOC entry 225 (class 1259 OID 16443)
-- Name: forecasts; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.forecasts (
    id integer NOT NULL,
    category_id integer,
    table_name character varying,
    display_name character varying,
    is_active boolean,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public.forecasts OWNER TO postgres;

--
-- TOC entry 224 (class 1259 OID 16442)
-- Name: forecasts_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.forecasts_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.forecasts_id_seq OWNER TO postgres;

--
-- TOC entry 3541 (class 0 OID 0)
-- Dependencies: 224
-- Name: forecasts_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.forecasts_id_seq OWNED BY public.forecasts.id;


--
-- TOC entry 239 (class 1259 OID 16613)
-- Name: input_values; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.input_values (
    input_id integer NOT NULL,
    metric_id integer NOT NULL,
    value double precision NOT NULL
);


ALTER TABLE public.input_values OWNER TO postgres;

--
-- TOC entry 234 (class 1259 OID 16550)
-- Name: inputs; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.inputs (
    id integer NOT NULL,
    forecast_id integer NOT NULL,
    time_grain character varying(255) NOT NULL,
    start_time timestamp without time zone NOT NULL,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now(),
    is_approved public.inputstatus NOT NULL,
    entity_id integer NOT NULL
);


ALTER TABLE public.inputs OWNER TO postgres;

--
-- TOC entry 233 (class 1259 OID 16549)
-- Name: inputs_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.inputs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.inputs_id_seq OWNER TO postgres;

--
-- TOC entry 3542 (class 0 OID 0)
-- Dependencies: 233
-- Name: inputs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.inputs_id_seq OWNED BY public.inputs.id;


--
-- TOC entry 240 (class 1259 OID 16629)
-- Name: metric_dependencies; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.metric_dependencies (
    dependent_metric_id integer NOT NULL,
    dependency_metric_id integer NOT NULL
);


ALTER TABLE public.metric_dependencies OWNER TO postgres;

--
-- TOC entry 227 (class 1259 OID 16476)
-- Name: metrics; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.metrics (
    id integer NOT NULL,
    category_id integer,
    select_query character varying,
    aggregate_query character varying,
    name character varying,
    display_name character varying,
    metric_type public.metrictype,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now(),
    grain character varying
);


ALTER TABLE public.metrics OWNER TO postgres;

--
-- TOC entry 226 (class 1259 OID 16475)
-- Name: metrics_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.metrics_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.metrics_id_seq OWNER TO postgres;

--
-- TOC entry 3543 (class 0 OID 0)
-- Dependencies: 226
-- Name: metrics_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.metrics_id_seq OWNED BY public.metrics.id;


--
-- TOC entry 228 (class 1259 OID 16491)
-- Name: models; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.models (
    id character varying NOT NULL,
    category_id integer,
    display_name character varying,
    is_active boolean,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public.models OWNER TO postgres;

--
-- TOC entry 219 (class 1259 OID 16400)
-- Name: tenants; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.tenants (
    id integer NOT NULL,
    name character varying,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public.tenants OWNER TO postgres;

--
-- TOC entry 218 (class 1259 OID 16399)
-- Name: tenants_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.tenants_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.tenants_id_seq OWNER TO postgres;

--
-- TOC entry 3544 (class 0 OID 0)
-- Dependencies: 218
-- Name: tenants_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.tenants_id_seq OWNED BY public.tenants.id;


--
-- TOC entry 238 (class 1259 OID 16587)
-- Name: users; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.users (
    id integer NOT NULL,
    full_name character varying,
    email character varying NOT NULL,
    is_allowed boolean DEFAULT false,
    created_at timestamp without time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.users OWNER TO postgres;

--
-- TOC entry 237 (class 1259 OID 16586)
-- Name: users_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.users_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.users_id_seq OWNER TO postgres;

--
-- TOC entry 3545 (class 0 OID 0)
-- Dependencies: 237
-- Name: users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.users_id_seq OWNED BY public.users.id;


--
-- TOC entry 3283 (class 2604 OID 16644)
-- Name: categories id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.categories ALTER COLUMN id SET DEFAULT nextval('public.categories_id_seq'::regclass);


--
-- TOC entry 3280 (class 2604 OID 16645)
-- Name: entities id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.entities ALTER COLUMN id SET DEFAULT nextval('public.entities_id_seq'::regclass);


--
-- TOC entry 3294 (class 2604 OID 16646)
-- Name: forecast_metrics id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_metrics ALTER COLUMN id SET DEFAULT nextval('public.forecast_metrics_id_seq'::regclass);


--
-- TOC entry 3303 (class 2604 OID 16647)
-- Name: forecast_model_entities id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_model_entities ALTER COLUMN id SET DEFAULT nextval('public.forecast_model_entities_id_seq'::regclass);


--
-- TOC entry 3297 (class 2604 OID 16648)
-- Name: forecast_models id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_models ALTER COLUMN id SET DEFAULT nextval('public.forecast_models_id_seq'::regclass);


--
-- TOC entry 3286 (class 2604 OID 16649)
-- Name: forecasts id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecasts ALTER COLUMN id SET DEFAULT nextval('public.forecasts_id_seq'::regclass);


--
-- TOC entry 3300 (class 2604 OID 16650)
-- Name: inputs id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.inputs ALTER COLUMN id SET DEFAULT nextval('public.inputs_id_seq'::regclass);


--
-- TOC entry 3289 (class 2604 OID 16651)
-- Name: metrics id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.metrics ALTER COLUMN id SET DEFAULT nextval('public.metrics_id_seq'::regclass);


--
-- TOC entry 3277 (class 2604 OID 16652)
-- Name: tenants id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tenants ALTER COLUMN id SET DEFAULT nextval('public.tenants_id_seq'::regclass);


--
-- TOC entry 3306 (class 2604 OID 16653)
-- Name: users id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users ALTER COLUMN id SET DEFAULT nextval('public.users_id_seq'::regclass);


--
-- TOC entry 3507 (class 0 OID 16394)
-- Dependencies: 217
-- Data for Name: alembic_version; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.alembic_version (version_num) FROM stdin;
c97a23ce502c
e1884b8acbb3
\.


--
-- TOC entry 3513 (class 0 OID 16427)
-- Dependencies: 223
-- Data for Name: categories; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.categories (id, name, entity_id, created_at, updated_at) FROM stdin;
1	core	1	2025-06-20 08:44:13.726051	2025-06-20 08:44:13.726051
2	instore	1	2025-06-20 08:44:13.726051	2025-06-20 08:44:13.726051
\.


--
-- TOC entry 3511 (class 0 OID 16411)
-- Dependencies: 221
-- Data for Name: entities; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.entities (id, name, tenant_id, created_at, updated_at) FROM stdin;
1	storeops	1	2025-06-20 08:43:36.51096	2025-06-20 08:43:36.51096
\.


--
-- TOC entry 3520 (class 0 OID 16506)
-- Dependencies: 230
-- Data for Name: forecast_metrics; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.forecast_metrics (id, forecast_id, metric_id, created_at, updated_at, editable) FROM stdin;
29	4	593	2025-06-23 10:19:22.101184	2025-06-23 10:19:22.101184	t
30	4	594	2025-06-23 10:19:22.101184	2025-06-23 10:19:22.101184	t
31	4	595	2025-06-23 10:19:22.101184	2025-06-23 10:19:22.101184	t
32	5	603	2025-06-23 10:20:36.452688	2025-06-23 10:20:36.452688	t
33	5	604	2025-06-23 10:20:36.452688	2025-06-23 10:20:36.452688	t
34	5	608	2025-06-23 10:20:36.452688	2025-06-23 10:20:36.452688	t
35	5	609	2025-06-23 10:20:36.452688	2025-06-23 10:20:36.452688	t
36	5	610	2025-06-23 10:20:36.452688	2025-06-23 10:20:36.452688	t
37	5	611	2025-06-23 10:20:36.452688	2025-06-23 10:20:36.452688	t
38	5	632	2025-06-23 10:20:36.452688	2025-06-23 10:20:36.452688	t
39	5	633	2025-06-23 10:20:36.452688	2025-06-23 10:20:36.452688	t
\.


--
-- TOC entry 3526 (class 0 OID 16566)
-- Dependencies: 236
-- Data for Name: forecast_model_entities; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.forecast_model_entities (id, forecast_models_id, entity_id, entity_value, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 3522 (class 0 OID 16527)
-- Dependencies: 232
-- Data for Name: forecast_models; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.forecast_models (id, forecast_id, model_id, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 3515 (class 0 OID 16443)
-- Dependencies: 225
-- Data for Name: forecasts; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.forecasts (id, category_id, table_name, display_name, is_active, created_at, updated_at) FROM stdin;
4	1	\N	Core Forecast	t	2025-06-23 10:19:22.101184	2025-06-23 10:19:22.101184
5	2	\N	Instore Forecast	t	2025-06-23 10:20:36.452688	2025-06-23 10:20:36.452688
\.


--
-- TOC entry 3529 (class 0 OID 16613)
-- Dependencies: 239
-- Data for Name: input_values; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.input_values (input_id, metric_id, value) FROM stdin;
\.


--
-- TOC entry 3524 (class 0 OID 16550)
-- Dependencies: 234
-- Data for Name: inputs; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.inputs (id, forecast_id, time_grain, start_time, created_at, updated_at, is_approved, entity_id) FROM stdin;
\.


--
-- TOC entry 3530 (class 0 OID 16629)
-- Dependencies: 240
-- Data for Name: metric_dependencies; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.metric_dependencies (dependent_metric_id, dependency_metric_id) FROM stdin;
616	615
616	598
617	614
617	612
634	629
634	617
635	634
635	629
\.


--
-- TOC entry 3517 (class 0 OID 16476)
-- Dependencies: 227
-- Data for Name: metrics; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.metrics (id, category_id, select_query, aggregate_query, name, display_name, metric_type, created_at, updated_at, grain) FROM stdin;
593	1	instore_orders	SUM(instore_orders)	instore_orders	Orders placed	COUNT	2025-06-23 10:16:56.841169	2025-06-23 10:16:56.841169	weekly
594	1	total_items_quantity_ordered	SUM(total_items_quantity_ordered)	total_items_quantity_ordered	Items Quantity Ordered	COUNT	2025-06-23 10:16:56.868671	2025-06-23 10:16:56.868671	weekly
595	1	total_quantity_indent	SUM(total_quantity_indent)	total_quantity_indent	Total Quantity Indent	COUNT	2025-06-23 10:16:56.879936	2025-06-23 10:16:56.879936	weekly
596	2	auditor_active_time_mins	SUM(auditor_active_time_mins)	auditor_active_time_mins	Auditor Active Time	MINUTES	2025-06-23 10:16:56.890159	2025-06-23 10:16:56.890159	
597	2	c2a_within_10_orders	SUM(c2a_within_10_orders)	c2a_within_10_orders	C2A Within 10 Orders	COUNT	2025-06-23 10:16:56.900884	2025-06-23 10:16:56.900884	
598	2	carts	SUM(carts)	carts	Total Carts	COUNT	2025-06-23 10:16:56.918343	2025-06-23 10:16:56.918343	
599	2	direct_handover_orders	SUM(direct_handover_orders)	direct_handover_orders	Direct Handover Orders	COUNT	2025-06-23 10:16:56.930216	2025-06-23 10:16:56.930216	
600	2	fixed_employee_absent_count	SUM(fixed_employee_absent_count)	fixed_employee_absent_count	Fixed Employee Absent Count	COUNT	2025-06-23 10:16:56.941971	2025-06-23 10:16:56.941971	
601	2	manhours_fnv	SUM(manhours_fnv)	manhours_fnv	Fnv Manhours	HOURS	2025-06-23 10:16:56.951397	2025-06-23 10:16:56.951397	
602	2	gsp_surge_carts	SUM(gsp_surge_carts)	gsp_surge_carts	GSP Surge Carts	COUNT	2025-06-23 10:16:56.962092	2025-06-23 10:16:56.962092	
603	2	headcount	SUM(headcount)	headcount	Headcount	COUNT	2025-06-23 10:16:56.975238	2025-06-23 10:16:56.975238	weekly,daily
604	2	headcount_shift_start	SUM(headcount_shift_start)	headcount_shift_start	Headcount Shift Start	COUNT	2025-06-23 10:16:56.985023	2025-06-23 10:16:56.985023	hourly
605	2	instore_orders	SUM(instore_orders)	instore_orders	Instore Orders	COUNT	2025-06-23 10:16:56.994856	2025-06-23 10:16:56.994856	
606	2	login_hours	SUM(login_hours)	login_hours	Total login hours	HOURS	2025-06-23 10:16:57.007506	2025-06-23 10:16:57.007506	
607	2	login_mins	SUM(login_mins)	login_mins	Total Login Minutes	MINUTES	2025-06-23 10:16:57.017149	2025-06-23 10:16:57.017149	
608	2	manhours_fixed	SUM(manhours_fixed)	manhours_fixed	Fixed Manhours	HOURS	2025-06-23 10:16:57.028194	2025-06-23 10:16:57.028194	hourly
609	2	manhours_od	SUM(manhours_od)	manhours_od	OD Manhours	HOURS	2025-06-23 10:16:57.038515	2025-06-23 10:16:57.038515	daily,hourly
610	2	manhours_picking	SUM(manhours_picking)	manhours_picking	Picking Manhours	HOURS	2025-06-23 10:16:57.05513	2025-06-23 10:16:57.05513	hourly
611	2	manhours_putaway	SUM(manhours_putaway)	manhours_putaway	Putaway Manhours	HOURS	2025-06-23 10:16:57.068356	2025-06-23 10:16:57.068356	hourly
612	2	picker_active_time_fixed_sec	SUM(picker_active_time_fixed_sec)	picker_active_time_fixed_sec	Picker Active Time Fixed (Sec)	SECONDS	2025-06-23 10:16:57.083083	2025-06-23 10:16:57.083083	
613	2	picker_active_time_mins	SUM(picker_active_time_mins)	picker_active_time_mins	Picker Active Time	MINUTES	2025-06-23 10:16:57.094422	2025-06-23 10:16:57.094422	
614	2	picker_busy_time_fixed_sec	SUM(picker_busy_time_fixed_sec)	picker_busy_time_fixed_sec	Picker Busy Time Fixed (Sec)	SECONDS	2025-06-23 10:16:57.10651	2025-06-23 10:16:57.10651	
615	2	picker_surge_carts	SUM(picker_surge_carts)	picker_surge_carts	Picker Surge Carts	COUNT	2025-06-23 10:16:57.117109	2025-06-23 10:16:57.117109	
616	2	picker_surge_pct	CAST(SUM(picker_surge_carts) AS DOUBLE) * 100.0 / NULLIF(SUM(carts), 0)	picker_surge_pct	Picker Surge %	PERCENTAGE	2025-06-23 10:16:57.130701	2025-06-23 10:16:57.130701	
617	2	picker_utilization_fixed	CAST(SUM(picker_busy_time_fixed_sec) AS DOUBLE) * 100 / NULLIF(SUM(picker_active_time_fixed_sec), 0)	picker_utilization_fixed	Fixed Picker Utilization	PERCENTAGE	2025-06-23 10:16:57.159237	2025-06-23 10:16:57.159237	
618	2	planned_employee_count	SUM(planned_employee_count)	planned_employee_count	Planned Employee Count	COUNT	2025-06-23 10:16:57.171549	2025-06-23 10:16:57.171549	
619	2	putaway_qty	SUM(putaway_qty)	putaway_qty	Putaway Quantity	COUNT	2025-06-23 10:16:57.182972	2025-06-23 10:16:57.182972	
620	2	putter_active_time_mins	SUM(putter_active_time_mins)	putter_active_time_mins	Putter Active Time	MINUTES	2025-06-23 10:16:57.196776	2025-06-23 10:16:57.196776	
621	2	qty_ordered_fixed	SUM(qty_ordered_fixed)	qty_ordered_fixed	Quantity Ordered Fixed	COUNT	2025-06-23 10:16:57.206571	2025-06-23 10:16:57.206571	
622	2	qty_ordered_new_employee	SUM(qty_ordered_new_employee)	qty_ordered_new_employee	Quantity Ordered New Employee	COUNT	2025-06-23 10:16:57.218533	2025-06-23 10:16:57.218533	
623	2	qty_ordered_new_employee_od	SUM(qty_ordered_new_employee_od)	qty_ordered_new_employee_od	Quantity Ordered New Employee OD	COUNT	2025-06-23 10:16:57.22918	2025-06-23 10:16:57.22918	
624	2	qty_ordered_od	SUM(qty_ordered_od)	qty_ordered_od	Quantity Ordered OD	COUNT	2025-06-23 10:16:57.241164	2025-06-23 10:16:57.241164	
625	2	qty_ordered_old_employee	SUM(qty_ordered_old_employee)	qty_ordered_old_employee	Quantity Ordered Old Employee	COUNT	2025-06-23 10:16:57.252327	2025-06-23 10:16:57.252327	
626	2	qty_ordered_old_employee_od	SUM(qty_ordered_old_employee_od)	qty_ordered_old_employee_od	Quantity Ordered Old Employee OD	COUNT	2025-06-23 10:16:57.261975	2025-06-23 10:16:57.261975	
627	2	r2a_within_10_orders	SUM(r2a_within_10_orders)	r2a_within_10_orders	R2A Within 10 Orders	COUNT	2025-06-23 10:16:57.272632	2025-06-23 10:16:57.272632	
628	2	rain_surge_carts	SUM(rain_surge_carts)	rain_surge_carts	Rain Surge Carts	COUNT	2025-06-23 10:16:57.283014	2025-06-23 10:16:57.283014	
629	2	total_items_quantity_ordered	SUM(total_items_quantity_ordered)	total_items_quantity_ordered	Total quantity ordered	COUNT	2025-06-23 10:16:57.292639	2025-06-23 10:16:57.292639	
630	2	total_payout	SUM(total_payout)	total_payout	Total Payout	RUPEES	2025-06-23 10:16:57.303028	2025-06-23 10:16:57.303028	
631	2	total_picking_time_sec	SUM(total_picking_time_sec)	total_picking_time_sec	Total Picking Time (Sec)	SECONDS	2025-06-23 10:16:57.315423	2025-06-23 10:16:57.315423	
632	2	mandays_fixed	SUM(mandays_fixed)	mandays_fixed	Mandays Fixed	COUNT	2025-06-23 10:16:57.3261	2025-06-23 10:16:57.3261	daily
633	2	headcount	SUM(headcount)	headcount	Head Count	COUNT	2025-06-23 10:16:57.33864	2025-06-23 10:16:57.33864	daily,weekly
634	2	picker_util_numerator_fixed	SUM(total_items_quantity_ordered*picker_utilization_fixed)	picker_util_numerator_fixed	Picker utilisation numerator fixed	COUNT	2025-06-23 10:16:57.350038	2025-06-23 10:16:57.350038	
635	2	picker_util_fixed_wt_avg	SUM(picker_util_numerator_fixed/total_items_quantity_ordered)	picker_util_fixed_wt_avg	Picker Util Fixed	AVERAGE	2025-06-23 10:16:57.366603	2025-06-23 10:16:57.366603	weekly
\.


--
-- TOC entry 3518 (class 0 OID 16491)
-- Dependencies: 228
-- Data for Name: models; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.models (id, category_id, display_name, is_active, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 3509 (class 0 OID 16400)
-- Dependencies: 219
-- Data for Name: tenants; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.tenants (id, name, created_at, updated_at) FROM stdin;
1	blinkit	2025-06-20 08:08:12.628919	2025-06-20 08:08:12.628919
\.


--
-- TOC entry 3528 (class 0 OID 16587)
-- Dependencies: 238
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.users (id, full_name, email, is_allowed, created_at) FROM stdin;
1	Shubham Gupta	<EMAIL>	t	2025-06-10 08:48:02.313414
\.


--
-- TOC entry 3546 (class 0 OID 0)
-- Dependencies: 222
-- Name: categories_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.categories_id_seq', 2, true);


--
-- TOC entry 3547 (class 0 OID 0)
-- Dependencies: 220
-- Name: entities_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.entities_id_seq', 1, true);


--
-- TOC entry 3548 (class 0 OID 0)
-- Dependencies: 229
-- Name: forecast_metrics_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.forecast_metrics_id_seq', 40, true);


--
-- TOC entry 3549 (class 0 OID 0)
-- Dependencies: 235
-- Name: forecast_model_entities_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.forecast_model_entities_id_seq', 1, false);


--
-- TOC entry 3550 (class 0 OID 0)
-- Dependencies: 231
-- Name: forecast_models_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.forecast_models_id_seq', 1, false);


--
-- TOC entry 3551 (class 0 OID 0)
-- Dependencies: 224
-- Name: forecasts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.forecasts_id_seq', 5, true);


--
-- TOC entry 3552 (class 0 OID 0)
-- Dependencies: 233
-- Name: inputs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.inputs_id_seq', 2, true);


--
-- TOC entry 3553 (class 0 OID 0)
-- Dependencies: 226
-- Name: metrics_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.metrics_id_seq', 635, true);


--
-- TOC entry 3554 (class 0 OID 0)
-- Dependencies: 218
-- Name: tenants_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.tenants_id_seq', 1, true);


--
-- TOC entry 3555 (class 0 OID 0)
-- Dependencies: 237
-- Name: users_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.users_id_seq', 1, true);


--
-- TOC entry 3310 (class 2606 OID 16398)
-- Name: alembic_version alembic_version_pkc; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.alembic_version
    ADD CONSTRAINT alembic_version_pkc PRIMARY KEY (version_num);


--
-- TOC entry 3316 (class 2606 OID 16436)
-- Name: categories categories_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.categories
    ADD CONSTRAINT categories_pkey PRIMARY KEY (id);


--
-- TOC entry 3314 (class 2606 OID 16420)
-- Name: entities entities_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.entities
    ADD CONSTRAINT entities_pkey PRIMARY KEY (id);


--
-- TOC entry 3324 (class 2606 OID 16513)
-- Name: forecast_metrics forecast_metrics_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_metrics
    ADD CONSTRAINT forecast_metrics_pkey PRIMARY KEY (id);


--
-- TOC entry 3334 (class 2606 OID 16573)
-- Name: forecast_model_entities forecast_model_entities_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_model_entities
    ADD CONSTRAINT forecast_model_entities_pkey PRIMARY KEY (id);


--
-- TOC entry 3328 (class 2606 OID 16536)
-- Name: forecast_models forecast_models_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_models
    ADD CONSTRAINT forecast_models_pkey PRIMARY KEY (id);


--
-- TOC entry 3318 (class 2606 OID 16452)
-- Name: forecasts forecasts_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecasts
    ADD CONSTRAINT forecasts_pkey PRIMARY KEY (id);


--
-- TOC entry 3343 (class 2606 OID 16617)
-- Name: input_values input_values_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.input_values
    ADD CONSTRAINT input_values_pkey PRIMARY KEY (input_id, metric_id);


--
-- TOC entry 3332 (class 2606 OID 16559)
-- Name: inputs inputs_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.inputs
    ADD CONSTRAINT inputs_pkey PRIMARY KEY (id);


--
-- TOC entry 3345 (class 2606 OID 16633)
-- Name: metric_dependencies metric_dependencies_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.metric_dependencies
    ADD CONSTRAINT metric_dependencies_pkey PRIMARY KEY (dependent_metric_id, dependency_metric_id);


--
-- TOC entry 3320 (class 2606 OID 16485)
-- Name: metrics metrics_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.metrics
    ADD CONSTRAINT metrics_pkey PRIMARY KEY (id);


--
-- TOC entry 3322 (class 2606 OID 16499)
-- Name: models models_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.models
    ADD CONSTRAINT models_pkey PRIMARY KEY (id);


--
-- TOC entry 3312 (class 2606 OID 16409)
-- Name: tenants tenants_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tenants
    ADD CONSTRAINT tenants_pkey PRIMARY KEY (id);


--
-- TOC entry 3326 (class 2606 OID 16515)
-- Name: forecast_metrics uq_forecast_metric; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_metrics
    ADD CONSTRAINT uq_forecast_metric UNIQUE (forecast_id, metric_id);


--
-- TOC entry 3330 (class 2606 OID 16538)
-- Name: forecast_models uq_forecast_model; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_models
    ADD CONSTRAINT uq_forecast_model UNIQUE (forecast_id, model_id);


--
-- TOC entry 3336 (class 2606 OID 16575)
-- Name: forecast_model_entities uq_forecast_model_entity; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_model_entities
    ADD CONSTRAINT uq_forecast_model_entity UNIQUE (forecast_models_id, entity_id, entity_value);


--
-- TOC entry 3341 (class 2606 OID 16596)
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- TOC entry 3337 (class 1259 OID 16597)
-- Name: ix_users_email; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX ix_users_email ON public.users USING btree (email);


--
-- TOC entry 3338 (class 1259 OID 16598)
-- Name: ix_users_full_name; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_users_full_name ON public.users USING btree (full_name);


--
-- TOC entry 3339 (class 1259 OID 16599)
-- Name: ix_users_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_users_id ON public.users USING btree (id);


--
-- TOC entry 3347 (class 2606 OID 16437)
-- Name: categories categories_entity_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.categories
    ADD CONSTRAINT categories_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES public.entities(id);


--
-- TOC entry 3346 (class 2606 OID 16421)
-- Name: entities entities_tenant_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.entities
    ADD CONSTRAINT entities_tenant_id_fkey FOREIGN KEY (tenant_id) REFERENCES public.tenants(id);


--
-- TOC entry 3351 (class 2606 OID 16516)
-- Name: forecast_metrics forecast_metrics_forecast_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_metrics
    ADD CONSTRAINT forecast_metrics_forecast_id_fkey FOREIGN KEY (forecast_id) REFERENCES public.forecasts(id);


--
-- TOC entry 3352 (class 2606 OID 16521)
-- Name: forecast_metrics forecast_metrics_metric_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_metrics
    ADD CONSTRAINT forecast_metrics_metric_id_fkey FOREIGN KEY (metric_id) REFERENCES public.metrics(id);


--
-- TOC entry 3356 (class 2606 OID 16576)
-- Name: forecast_model_entities forecast_model_entities_entity_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_model_entities
    ADD CONSTRAINT forecast_model_entities_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES public.entities(id);


--
-- TOC entry 3357 (class 2606 OID 16581)
-- Name: forecast_model_entities forecast_model_entities_forecast_models_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_model_entities
    ADD CONSTRAINT forecast_model_entities_forecast_models_id_fkey FOREIGN KEY (forecast_models_id) REFERENCES public.forecast_models(id);


--
-- TOC entry 3353 (class 2606 OID 16539)
-- Name: forecast_models forecast_models_forecast_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_models
    ADD CONSTRAINT forecast_models_forecast_id_fkey FOREIGN KEY (forecast_id) REFERENCES public.forecasts(id);


--
-- TOC entry 3354 (class 2606 OID 16544)
-- Name: forecast_models forecast_models_model_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecast_models
    ADD CONSTRAINT forecast_models_model_id_fkey FOREIGN KEY (model_id) REFERENCES public.models(id);


--
-- TOC entry 3348 (class 2606 OID 16453)
-- Name: forecasts forecasts_category_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.forecasts
    ADD CONSTRAINT forecasts_category_id_fkey FOREIGN KEY (category_id) REFERENCES public.categories(id);


--
-- TOC entry 3358 (class 2606 OID 16618)
-- Name: input_values input_values_input_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.input_values
    ADD CONSTRAINT input_values_input_id_fkey FOREIGN KEY (input_id) REFERENCES public.inputs(id);


--
-- TOC entry 3359 (class 2606 OID 16623)
-- Name: input_values input_values_metric_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.input_values
    ADD CONSTRAINT input_values_metric_id_fkey FOREIGN KEY (metric_id) REFERENCES public.metrics(id);


--
-- TOC entry 3355 (class 2606 OID 16560)
-- Name: inputs inputs_forecast_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.inputs
    ADD CONSTRAINT inputs_forecast_id_fkey FOREIGN KEY (forecast_id) REFERENCES public.forecasts(id);


--
-- TOC entry 3360 (class 2606 OID 16634)
-- Name: metric_dependencies metric_dependencies_dependency_metric_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.metric_dependencies
    ADD CONSTRAINT metric_dependencies_dependency_metric_id_fkey FOREIGN KEY (dependency_metric_id) REFERENCES public.metrics(id);


--
-- TOC entry 3361 (class 2606 OID 16639)
-- Name: metric_dependencies metric_dependencies_dependent_metric_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.metric_dependencies
    ADD CONSTRAINT metric_dependencies_dependent_metric_id_fkey FOREIGN KEY (dependent_metric_id) REFERENCES public.metrics(id);


--
-- TOC entry 3349 (class 2606 OID 16486)
-- Name: metrics metrics_category_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.metrics
    ADD CONSTRAINT metrics_category_id_fkey FOREIGN KEY (category_id) REFERENCES public.categories(id);


--
-- TOC entry 3350 (class 2606 OID 16500)
-- Name: models models_category_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.models
    ADD CONSTRAINT models_category_id_fkey FOREIGN KEY (category_id) REFERENCES public.categories(id);


-- Completed on 2025-06-23 10:41:23 UTC

--
-- PostgreSQL database dump complete
--

