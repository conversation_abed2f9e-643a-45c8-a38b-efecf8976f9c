"""added separate table for input values

Revision ID: 15d0605ab0a7
Revises: 732a2e3998f0
Create Date: 2025-05-23 16:12:26.008189

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "15d0605ab0a7"
down_revision: Union[str, None] = "732a2e3998f0"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "input_values",
        sa.Column("input_id", sa.Integer(), nullable=False),
        sa.<PERSON>umn("metric_id", sa.Integer(), nullable=False),
        sa.<PERSON>umn("value", sa.Float(), nullable=False),
        sa.ForeignKeyConstraint(
            ["input_id"],
            ["inputs.id"],
        ),
        sa.ForeignKeyConstraint(
            ["metric_id"],
            ["metrics.id"],
        ),
        sa.PrimaryKeyConstraint("input_id", "metric_id"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("input_values")
    # ### end Alembic commands ###
