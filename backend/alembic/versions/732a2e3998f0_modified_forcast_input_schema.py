"""modified forcast input schema

Revision ID: 732a2e3998f0
Revises: e3e6656d682d
Create Date: 2025-05-23 16:10:33.754982

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "732a2e3998f0"
down_revision: Union[str, None] = "e3e6656d682d"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("inputs", sa.Column("entity_id", sa.Integer(), nullable=False))
    op.drop_column("inputs", "metric")
    op.drop_column("inputs", "entity")
    op.drop_column("inputs", "value")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "inputs",
        sa.Column(
            "value",
            postgresql.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=False,
        ),
    )
    op.add_column(
        "inputs",
        sa.Column(
            "entity", sa.VARCHAR(length=255), autoincrement=False, nullable=False
        ),
    )
    op.add_column(
        "inputs",
        sa.Column(
            "metric", sa.VARCHAR(length=255), autoincrement=False, nullable=False
        ),
    )
    op.drop_column("inputs", "entity_id")
    # ### end Alembic commands ###
