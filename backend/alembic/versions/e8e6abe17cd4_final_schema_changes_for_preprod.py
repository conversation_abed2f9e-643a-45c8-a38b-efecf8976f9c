"""final schema changes for preprod

Revision ID: e8e6abe17cd4
Revises: 15d0605ab0a7
Create Date: 2025-05-28 15:52:48.696826

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "e8e6abe17cd4"
down_revision: Union[str, None] = "15d0605ab0a7"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
