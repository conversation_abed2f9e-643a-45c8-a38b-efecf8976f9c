"""made is_approved an enum

Revision ID: e3e6656d682d
Revises: e1fded9069f1
Create Date: 2025-05-22 18:57:51.343492

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "e3e6656d682d"
down_revision: Union[str, None] = "e1fded9069f1"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    inputstatus_enum = sa.Enum("PENDING", "APPROVED", "REJECTED", name="inputstatus")
    inputstatus_enum.create(op.get_bind(), checkfirst=True)

    op.alter_column(
        "inputs",
        "is_approved",
        type_=inputstatus_enum,
        postgresql_using="""
            CASE 
                WHEN is_approved THEN 'APPROVED'::inputstatus 
                ELSE 'PENDING'::inputstatus 
            END
        """,
        nullable=False,
    )


def downgrade() -> None:
    op.alter_column(
        "inputs",
        "is_approved",
        type_=sa.BOOLEAN(),
        postgresql_using="""
            CASE 
                WHEN is_approved = 'APPROVED' THEN TRUE 
                ELSE FALSE 
            END
        """,
        nullable=True,
    )

    sa.Enum(name="inputstatus").drop(op.get_bind(), checkfirst=True)
