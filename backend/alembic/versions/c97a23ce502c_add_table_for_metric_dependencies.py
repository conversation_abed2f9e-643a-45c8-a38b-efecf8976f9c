"""add table for metric dependencies

Revision ID: c97a23ce502c
Revises: e1884b8acbb3
Create Date: 2025-06-20 12:20:04.486113

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "c97a23ce502c"
down_revision: Union[str, None] = "e1884b8acbb3"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "metric_dependencies",
        sa.Column("dependent_metric_id", sa.Integer(), nullable=False),
        sa.<PERSON>umn("dependency_metric_id", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(
            ["dependency_metric_id"],
            ["metrics.id"],
        ),
        sa.ForeignKeyConstraint(
            ["dependent_metric_id"],
            ["metrics.id"],
        ),
        sa.PrimaryKeyConstraint("dependent_metric_id", "dependency_metric_id"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("metric_dependencies")
    # ### end Alembic commands ###
