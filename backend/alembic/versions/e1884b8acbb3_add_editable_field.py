"""add editable field

Revision ID: e1884b8acbb3
Revises: e8e6abe17cd4
Create Date: 2025-05-28 18:29:57.277501

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "e1884b8acbb3"
down_revision: Union[str, None] = "e8e6abe17cd4"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "forecast_metrics", sa.<PERSON>umn("editable", sa.<PERSON>(), nullable=False)
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("forecast_metrics", "editable")
    # ### end Alembic commands ###
