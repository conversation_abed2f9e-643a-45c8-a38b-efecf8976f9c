from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
import jwt
from datetime import datetime, timedelta
from backend.app.core.config import settings
from backend.app.api.v1.auth_utils import create_jwt_pair, set_cookie
from backend.app.models.user import User
import logging

logger = logging.getLogger(__name__)


async def auth_middleware(request: Request, call_next):
    """
    Middleware to check if user is authenticated via JWT tokens in cookies.
    Handles token validation and refresh when needed.
    """
    # Skip auth for these paths
    excluded_paths = [
        "/api/v1/auth/google/login",
        "/api/v1/auth/google/callback",
        "/api/v1/auth/logout",
        "/docs",
        "/redoc",
        "/"
        # "/health",
        # "/openapi.json"
    ]

    # Skip auth for specific paths or OPTIONS requests (preflight doesn't contain cookies)
    if request.method == "OPTIONS" or any(
        request.url.path.startswith(path) for path in excluded_paths
    ):
        return await call_next(request)

    # Check for access token
    access_token = request.cookies.get("access_token")
    refresh_token = request.cookies.get("refresh_token")

    if not access_token:
        if refresh_token:
            try:
                return await refresh_access_token(request, call_next, refresh_token)
            except Exception as refresh_error:
                logger.error(f"Failed to refresh expired token: {str(refresh_error)}")
                return JSONResponse(
                    status_code=401,
                    content={"detail": "Token expired and refresh failed"},
                )
        return JSONResponse(
            status_code=401, content={"detail": "Authentication failed"}
        )

    # Validate access token
    try:
        # Decode the token with signature verification
        try:
            decoded_access_token = jwt.decode(
                access_token,
                settings.JWT_SECRET_KEY,
                algorithms=["HS256"],
                # "verify_exp": False --> PyJWT will not raise an error even if the token is expired.
                options={"verify_exp": False},
            )
        except jwt.InvalidSignatureError:
            logger.error("Invalid token signature")
            return JSONResponse(
                status_code=401, content={"detail": "Invalid token signature"}
            )

        # Manually check expiration
        exp = decoded_access_token.get("exp")
        if not exp or datetime.fromtimestamp(exp) < datetime.utcnow():
            # Token expired, try to refresh
            if refresh_token:
                return await refresh_access_token(request, call_next, refresh_token)
            else:
                return JSONResponse(
                    status_code=401, content={"detail": "Token expired"}
                )

        # Add user info to request state
        request.state.user = decoded_access_token

        # Continue with the request
        return await call_next(request)

    except jwt.InvalidTokenError as e:
        logger.error(f"Invalid token format: {str(e)}")
        # Check specifically for expired token errors
        if "expired" in str(e).lower():
            # Try to refresh if we have a refresh token
            if refresh_token:
                try:
                    return await refresh_access_token(request, call_next, refresh_token)
                except Exception as refresh_error:
                    logger.error(
                        f"Failed to refresh expired token: {str(refresh_error)}"
                    )
                    return JSONResponse(
                        status_code=401,
                        content={"detail": "Token expired and refresh failed"},
                    )
            else:
                return JSONResponse(
                    status_code=401, content={"detail": "Token expired"}
                )
        # Handle other invalid token errors
        return JSONResponse(status_code=401, content={"detail": "Invalid token format"})
    except jwt.PyJWTError as e:
        logger.error(f"JWT validation error: {str(e)}")
        return JSONResponse(
            status_code=401, content={"detail": "Invalid authentication token"}
        )


async def refresh_access_token(request: Request, call_next, refresh_token):
    try:
        decoded_token = jwt.decode(
            refresh_token, settings.JWT_SECRET_KEY, algorithms=["HS256"]
        )

        exp = decoded_token.get("exp")
        if not exp or datetime.fromtimestamp(exp) < datetime.utcnow():
            return JSONResponse(status_code=401, content={"detail": "Token expired"})

        user = User(
            email=decoded_token.get("email"),
            is_allowed=decoded_token.get("is_allowed", False),
        )

        if not user.is_allowed:
            raise HTTPException(status_code=403, detail="Not allowed")

        # Generate new token pair
        new_access_token, new_refresh_token, error = create_jwt_pair(user)

        if error:
            raise HTTPException(status_code=401, detail="Failed to refresh token")

        # Add user to request state. #? is this needed??
        request.state.user = jwt.decode(
            new_access_token,
            settings.JWT_SECRET_KEY,
            algorithms=["HS256"],
            options={"verify_exp": False},
        )

        # Process the request
        response = await call_next(request)

        # Update cookies in response
        set_cookie(
            response,
            new_access_token,
            "access",
            settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        )

        return response
    except Exception as e:
        logger.error(f"Error refreshing token: {str(e)}")
        raise
