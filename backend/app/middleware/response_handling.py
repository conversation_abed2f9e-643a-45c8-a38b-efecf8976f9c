from fastapi import Request
from fastapi.responses import JSONResponse
import logging
import json

logger = logging.getLogger(__name__)


def remove_nulls(obj):
    if isinstance(obj, dict):
        return {k: remove_nulls(v) for k, v in obj.items() if v is not None}
    elif isinstance(obj, list):
        return [remove_nulls(item) for item in obj]
    else:
        return obj


async def response_sanitazation_middleware(request: Request, call_next):
    response = await call_next(request)
    if response.headers.get("content-type") == "application/json":
        body = [section async for section in response.body_iterator]
        raw_body = b"".join(body)
        try:
            data = json.loads(raw_body)
            cleaned = remove_nulls(data)
            return JSONResponse(content=cleaned, status_code=response.status_code)
        except Exception:
            return response
    return response
