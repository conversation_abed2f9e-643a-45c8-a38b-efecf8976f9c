import asyncio
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import RedirectResponse
import logging
import uvicorn
import pymysql
from pydantic import ValidationError
from backend.app.api.v1.exception_handlers import (
    db_connection_error_handler,
    validation_exception_handler,
    http_exception_handler,
    generic_error_handler,
    query_timeout_handler,
)
from backend.app.api.v1.api import api_router
from backend.app.core.logging import setup_logging
from backend.app.middleware.auth import auth_middleware
from backend.app.middleware.response_handling import (
    response_sanitazation_middleware,
)

setup_logging()
logger = logging.getLogger(__name__)

app = FastAPI(
    title="Planning Platform",
    description="Manpower Planning Tool for Quick Commerce",
    version="1.0.0",
)

# CORS middleware with more specific configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # Update this to match your frontend URL
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add auth middleware
app.middleware("http")(auth_middleware)

# commented middleware: as they mess with the auth
# app.middleware("http")(response_sanitazation_middleware)

# Add exception handlers
app.add_exception_handler(HTTPException, http_exception_handler)
app.add_exception_handler(RequestValidationError, validation_exception_handler)
app.add_exception_handler(ValidationError, validation_exception_handler)
app.add_exception_handler(pymysql.err.OperationalError, db_connection_error_handler)
app.add_exception_handler(pymysql.err.ProgrammingError, db_connection_error_handler)
app.add_exception_handler(pymysql.err.InternalError, db_connection_error_handler)
app.add_exception_handler(asyncio.TimeoutError, query_timeout_handler)
app.add_exception_handler(Exception, generic_error_handler)


@app.get("/")
async def root():
    return RedirectResponse(url="/docs")


@app.get("/health")
async def health_check():
    logger.debug("Health check passed")
    return {"status": "healthy"}


app.include_router(api_router, prefix="/api/v1")

if __name__ == "__main__":
    uvicorn.run("backend.app.main:app", host="0.0.0.0", port=8000, reload=True)
