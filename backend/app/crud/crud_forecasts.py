from datetime import datetime, timedelta
from typing import List, Tu<PERSON>, Dict, Union, Optional
import logging
from collections import namedtuple, defaultdict
from sqlalchemy import desc
from sqlalchemy.dialects import postgresql
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm.exc import NoResultFound
from backend.app.db.rds_sync_client import RDSSyncClient
from backend.app.db.starrocks_async_client import StarRocksAsyncClient
from backend.app.models.forecasts import Forecasts as ForecastModel
from backend.app.models.categories import Categories as CategoryModel
from backend.app.models.metrics import Metrics as MetricModel, metric_dependencies
from backend.app.models.models import Models as ModelModel
from backend.app.models.entities import Entities as EntityModel
from backend.app.models.forecast_metrics import ForecastMetrics as ForecastMetricModel
from backend.app.models.forecast_models import ForecastModels as ForecastModelModel
from backend.app.models.inputs import Inputs as InputModel
from backend.app.models.input_values import InputValues as InputValueModel
from backend.app.models.forecast_model_entities import (
    ForecastModelEntities as ForecastModelEntityModel,
)
from backend.app.schemas.inputs import (
    InputStatus,
    InputValue,
    ForecastInput,
    CreateForecastInputRequest,
    UpdateInputStatus,
)
from backend.app.schemas.utils import AggregationGranularity
from backend.app.schemas.models import Model
from backend.app.schemas.base import ForecastSchema
from backend.app.schemas.forecasts import (
    Forecast,
    ForecastData,
    ForecastDataRequest,
    DetailedForecastDataRequest,
    SingleAggregation,
)
from backend.app.schemas.utils import TimeGranularity
from backend.app.crud.queries.query_forecasts import (
    QueryForecasts,
)
import backend.app.crud.utils as utils

logger = logging.getLogger(__name__)


class CrudForecasts:
    def __init__(self):
        self.query_forecasts = QueryForecasts()
        self.rds_client = RDSSyncClient()
        self.starrocks_client = StarRocksAsyncClient()


    async def __get_forecast_last_updated_ts(self, forecast_table: str) -> datetime:
        query, params = self.query_forecasts.get_forecast_last_updated_ts_query(
            forecast_table
        )
        result = await self.starrocks_client.execute_query(query, params)
        rows = result.fetchall()
        try:
            res = rows[0][0]
        except IndexError:
            raise ValueError("No data found for the forecast")
        datetime_obj = datetime.strptime(res, "%Y-%m-%d %H:%M:%S")
        return datetime_obj

    async def __get_city_entity_map(self, cities: List[str]) -> Dict[str, List[int]]:
        query, params = self.query_forecasts.get_city_entity_map_query(cities)
        result = await self.starrocks_client.execute_query(query, params)

        rows = result.fetchall()
        columns = list(result.keys())

        city_idx = columns.index("city")
        entity_idx = columns.index("entity_id")

        city_entity_map = defaultdict(list)
        for row in rows:
            city = utils.parse_value_from_db(row[city_idx], "str")
            entity = utils.parse_value_from_db(row[entity_idx], "int")
            city_entity_map[city].append(entity)

        return city_entity_map

    async def __get_forecast_metrics(self, forecast_ids: List[int]) -> Dict[int, bool]:
        """Returns the metrics to display for the forecast_id(s), and whether they are editable or not"""

        logger.debug(f"Getting forecast metrics for forecast_id(s): {forecast_ids}")
        forecast_metric_associations = None
        with self.rds_client.session_scope() as db:
            query = db.query(ForecastMetricModel).filter(
                ForecastMetricModel.forecast_id.in_(forecast_ids)
            )
            forecast_metrics = query.all()
            if len(forecast_metrics) != len(
                set([metric.metric_id for metric in forecast_metrics])
            ):
                raise ValueError(
                    f"Duplicate metrics found for forecast_id(s): {forecast_ids}"
                )
            forecast_metric_associations = {
                forecast_metric.metric_id: forecast_metric.editable
                for forecast_metric in forecast_metrics
            }
        if forecast_metric_associations is None:
            logger.debug(
                f"Forecast metrics not found for forecast_id(s): {forecast_ids}"
            )
            return {}
        return forecast_metric_associations

    async def __get_forecast_schema(
        self,
        forecast_ids: List[int],
        time_grain: TimeGranularity,
    ) -> ForecastSchema:
        logger.debug(
            f"Getting {time_grain.value} forecast schema for forecast_ids: {forecast_ids}"
        )

        category_names = {}
        with self.rds_client.session_scope() as db:
            query = (
                db.query(CategoryModel)
                .join(ForecastModel)
                .filter(ForecastModel.id.in_(forecast_ids))
            )
            categories = query.all()
            category_names = {category.id: category.name for category in categories}

        forecast_metrics = await self.__get_forecast_metrics(forecast_ids)

        with self.rds_client.session_scope() as db:
            query = db.query(MetricModel).filter(
                MetricModel.id.in_(forecast_metrics),
                MetricModel.grain.contains(time_grain.value),
            )
            metrics = query.all()
            metric_model = {
                metric.name: {
                    "metric_name": metric.name,
                    "display_name": metric.display_name,
                    "select_query": metric.select_query,
                    "aggregate_query": metric.aggregate_query,
                    "metric_type": metric.metric_type,
                    "editable": forecast_metrics[metric.id],
                    # NOTE: this works if metrics and forecast belong to the same category (which was assumed in the LLD)
                    "category_name": category_names[metric.category_id],
                    "dependency_metric": False,
                }
                for metric in metrics
            }

            query = (
                db.query(MetricModel)
                .join(
                    metric_dependencies,
                    metric_dependencies.c.dependency_metric_id == MetricModel.id,
                )
                .filter(
                    metric_dependencies.c.dependent_metric_id.in_(forecast_metrics),
                    # TODO: the dependency metric should also have the same time grain and we should add checks while creating the metric
                    # MetricModel.grain.contains(time_grain.value),
                )
            )
            dependency_metrics = query.all()
            for metric in dependency_metrics:
                if metric.name in metric_model:
                    continue
                metric_model[metric.name] = {
                    "metric_name": metric.name,
                    "display_name": metric.display_name,
                    "select_query": metric.select_query,
                    "aggregate_query": metric.aggregate_query,
                    "metric_type": metric.metric_type,
                    "editable": False,
                    "category_name": category_names[metric.category_id],
                    "dependency_metric": True,
                }

        logger.debug(f"Found {metric_model} metrics")

        if len(metric_model) == 0:
            raise ValueError(f"No metrics found for forecast {forecast_ids}")

        return ForecastSchema(metric_model=metric_model)

    async def get_forecasts(
        self, tenant: str, entity: str, category_id: Optional[int]
    ) -> List[Forecast]:
        logger.debug(f"Getting forecasts for category_id: {category_id}")

        with self.rds_client.session_scope() as db:

            required_categories = {}
            if category_id:
                required_categories[category_id] = (
                    db.query(CategoryModel)
                    .filter(CategoryModel.id == category_id)
                    .one()
                    .name
                )
            else:
                query = (
                    db.query(CategoryModel)
                    .join(EntityModel)
                    .filter(EntityModel.name == entity)
                )
                required_categories = {
                    category.id: category.name for category in query.all()
                }

            category_wise_forecasts = {}
            for category_id, category_name in required_categories.items():
                forecast = None
                query = db.query(ForecastModel).filter(
                    ForecastModel.category_id == category_id,
                    ForecastModel.is_active.is_(True),
                )
                try:
                    forecasts = query.all()
                    if len(forecasts) > 1:
                        logger.info(
                            f"Multiple forecasts found for category {category_id}"
                        )
                    forecast = forecasts[0]
                except NoResultFound:
                    logger.error(f"Forecast not found for category {category_id}")
                    continue
                category_wise_forecasts[category_name] = Forecast(
                    id=forecast.id,
                    display_name=forecast.display_name,
                    default_model="m1",
                    sla="",
                    approvers=[],
                )
                forecast_metrics = await self.__get_forecast_metrics([forecast.id])
                category_wise_forecasts[category_name].metrics = list(
                    forecast_metrics.keys()
                )
            return {
                "tenant": tenant,
                "entity": entity,
                "forecasts": category_wise_forecasts,
            }

    async def register_forecast(self, create_forecast: Forecast) -> Forecast:
        with self.rds_client.session_scope() as db:
            forecast = ForecastModel(
                category_id=create_forecast.category,
                display_name=create_forecast.display_name,
                is_active=True,
            )
            db.add(forecast)
            db.flush()
            db.refresh(forecast)
            logger.debug(f"Created forecast: {forecast.display_name}")
            create_forecast.id = forecast.id

            forecast_metrics = [
                ForecastMetricModel(forecast_id=forecast.id, metric_id=metric_id)
                for metric_id in create_forecast.metrics
            ]
            db.add_all(forecast_metrics)

        return create_forecast

    async def get_entity_forecast(
        self, get_forecast_request: ForecastDataRequest
    ) -> ForecastData:
        start_time, end_time = utils.get_time_period(
            get_forecast_request.start_time, TimeGranularity.WEEKLY
        )

        forecast_schema = await self.__get_forecast_schema(
            get_forecast_request.forecast_ids,
            get_forecast_request.time_grain,
        )
        query, params = self.query_forecasts.get_forecast_query(
            get_forecast_request.forecast_ids,
            get_forecast_request.multi_aggregation,
            get_forecast_request.time_grain,
            start_time,
            end_time,
            forecast_schema,
        )

        result = await self.starrocks_client.execute_query(query, params)

        rows = result.fetchall()
        columns = list(result.keys())
        logger.debug(f"Columns: {columns}")
        logger.debug(f"Rows: {rows}")

        # TODO: There can be entities other than store/city, need to set the identity based on the api endpoint
        resp_idx_cols = {
            "entity": {"display_name": "Store/City Names", "metric_type": "str"}
        }
        resp_cols = utils.parse_cols_to_dict(columns, forecast_schema.metric_model)
        resp_values = utils.parse_db_output_forecasts(
            rows,
            columns,
            forecast_schema.metric_model,
            forecast_schema.metric_schema,
            None,
            get_forecast_request.multi_aggregation.grain,
        )

        return ForecastData(idx_cols=resp_idx_cols, cols=resp_cols, values=resp_values)

    async def get_detailed_entity_forecast(
        self, get_forecast_detailed_request: DetailedForecastDataRequest
    ) -> ForecastData:
        start_time, end_time = utils.get_time_period(
            get_forecast_detailed_request.start_time,
            get_forecast_detailed_request.time_grain.next_grain,
        )
        forecast_schema = await self.__get_forecast_schema(
            get_forecast_detailed_request.forecast_ids,
            get_forecast_detailed_request.time_grain,
        )
        query, params = self.query_forecasts.get_forecast_detailed_query(
            get_forecast_detailed_request.forecast_ids,
            get_forecast_detailed_request.single_aggregation,
            get_forecast_detailed_request.time_grain,
            start_time,
            end_time,
            forecast_schema,
        )

        result = await self.starrocks_client.execute_query(query, params)

        rows = result.fetchall()
        columns = list(result.keys())
        logger.debug(f"Columns: {columns}")
        logger.debug(f"Rows: {rows}")

        resp_idx_cols = {"time": {"display_name": "Time", "metric_type": "str"}}
        resp_cols = utils.parse_cols_to_dict(columns, forecast_schema.metric_model)
        resp_values = utils.parse_db_output_forecasts(
            rows,
            columns,
            forecast_schema.metric_model,
            forecast_schema.metric_schema,
            get_forecast_detailed_request.time_grain,
            None,
        )

        return ForecastData(idx_cols=resp_idx_cols, cols=resp_cols, values=resp_values)

    async def get_entity_forecast_input(
        self, get_forecast_inputs_request: ForecastDataRequest, get_outputs:bool = False
    ) -> List[ForecastInput]:
        logger.debug(
            f"Getting inputs for: Forecast ids: {get_forecast_inputs_request.forecast_ids}, time grain: {get_forecast_inputs_request.time_grain.value}, start time: {get_forecast_inputs_request.start_time}"
        )

        category_name = None
        with self.rds_client.session_scope() as db:
            query = db.query(CategoryModel).filter(
                CategoryModel.id == get_forecast_inputs_request.category_id
            )
            try:
                category = query.one()
            except NoResultFound:
                raise Exception(
                    f"Category not found: {get_forecast_inputs_request.category_id}"
                )
            category_name = category.name
        table_name = f"planner_{category_name}_forecasts_{get_forecast_inputs_request.time_grain.value}"
        updated_ts = await self.__get_forecast_last_updated_ts(table_name)
        # TODO: can optimise this when we need entity level data for pan-india
        if type(get_forecast_inputs_request.multi_aggregation.values) is int:
            city_entity_map: Dict[str, List[int]] = await self.__get_city_entity_map(get_forecast_inputs_request.multi_aggregation.values)

        with self.rds_client.session_scope() as db:
            query = db.query(InputModel).filter(
                InputModel.forecast_id.in_(get_forecast_inputs_request.forecast_ids),
                InputModel.time_grain == get_forecast_inputs_request.time_grain.value,
                InputModel.start_time == get_forecast_inputs_request.start_time,
                # TODO: temporarily modifying this check since we might need to process stale inputs, will add this once we have immediate model execution
                InputModel.updated_at + timedelta(weeks=1) >= updated_ts,
                InputModel.is_approved != InputStatus.REJECTED.name,
            )

            inputs = query.order_by(
                desc(InputModel.updated_at), InputModel.entity_id
            ).all()

            logger.debug(f"Found {len(inputs)} inputs")

            InputCell = namedtuple(
                "InputCell",
                "entity_id, start_time, time_grain, metric_name, is_approved",
            )
            cell_wise_input_timestamps: dict[InputCell, datetime] = {}

            forecast_inputs = []
            for input_obj in inputs:

                query = (
                    db.query(InputValueModel.value, MetricModel.name)
                    .join(MetricModel, InputValueModel.metric_id == MetricModel.id)
                    .filter(InputValueModel.input_id == input_obj.id)
                )
                input_values = query.all()

                compressed_input_values = []
                for value, metric_name in input_values:
                    input_cell = InputCell(
                        input_obj.entity_id,
                        input_obj.start_time,
                        input_obj.time_grain,
                        metric_name,
                        input_obj.is_approved.value,
                    )

                    # Ignore older inputs
                    if (
                        cell_wise_input_timestamps.get(input_cell, datetime.min)
                        > input_obj.updated_at
                    ):
                        continue

                    # Ignore pending inputs if there is an approved input after it
                    if (
                        input_obj.is_approved.value == InputStatus.PENDING.value
                        and cell_wise_input_timestamps.get(
                            input_cell._replace(is_approved=InputStatus.APPROVED.value),
                            datetime.min,
                        )
                        > input_obj.updated_at
                    ):
                        continue

                    # Fetched all inputs upto a week before updated_ts, but only pending inputs before updated_ts are sent to the model
                    if not ((input_obj.is_approved.value == InputStatus.APPROVED.value) and (cell_wise_input_timestamps.get(input_cell, datetime.min) < updated_ts)):
                        compressed_input_values.append(
                            InputValue(metric_name=metric_name, value=value)
                        )
                    cell_wise_input_timestamps[input_cell] = input_obj.updated_at

                if len(compressed_input_values) == 0:
                    continue

                forecast_input = ForecastInput(
                    input_id=input_obj.id,
                    entity_id=input_obj.entity_id,
                    start_time=input_obj.start_time.strftime("%Y-%m-%d %H:%M:%S")
                    if hasattr(input_obj.start_time, "strftime")
                    else input_obj.start_time,
                    time_grain=get_forecast_inputs_request.time_grain,
                    status=input_obj.is_approved.value,
                    updated_at=input_obj.updated_at.strftime("%Y-%m-%d %H:%M:%S"),
                    values=compressed_input_values,
                )
                forecast_inputs.append(forecast_input)

            if not get_outputs:
                logger.debug(f"Returning {len(forecast_inputs)} inputs")
                return forecast_inputs

            logger.debug(f"Getting outputs after {updated_ts} inputs for forecast_id(s): {get_forecast_inputs_request.forecast_ids}")
            
            # NOTE: we instead use time_grain.next_grain for detailed forecasts, but hardcoding since no inputs are added there.
            start_time, end_time = utils.get_time_period(
                get_forecast_inputs_request.start_time,
                get_forecast_inputs_request.time_grain,
            )
            forecast_schema = await self.__get_forecast_schema(
                get_forecast_inputs_request.forecast_ids,
                get_forecast_inputs_request.time_grain,
            )
            query, params = self.query_forecasts.get_forecast_outputs_query(
                get_forecast_inputs_request.forecast_ids,
                get_forecast_inputs_request.multi_aggregation,
                get_forecast_inputs_request.time_grain,
                start_time,
                end_time,
                forecast_schema,
                updated_ts,
            )

            result = await self.starrocks_client.execute_query(query, params)

            rows = result.fetchall()
            columns = list(result.keys())
            logger.debug(f"Columns: {columns}")
            logger.debug(f"Rows: {rows}")

            forecast_outputs = utils.parse_db_forecast_output(
                rows,
                columns,
                get_forecast_inputs_request.time_grain,
                start_time,
                forecast_schema.metric_model,
            )

            return forecast_inputs + forecast_outputs

    async def create_entity_forecast_input(
        self, create_forecast_input_request: CreateForecastInputRequest
    ) -> List[ForecastInput]:

        logger.debug(
            f"Creating inputs for forecast_id: {create_forecast_input_request.forecast_id}"
        )

        metrics = []
        for input_data in create_forecast_input_request.inputs:
            for value in input_data.values:
                metrics.append(value.metric_name)
        metric_name_to_ids = {}
        with self.rds_client.session_scope() as db:
            query = db.query(MetricModel).filter(MetricModel.name.in_(metrics))
            for metric in query.all():
                metric_name_to_ids[metric.name] = metric.id

        inputs, input_values = [None] * len(create_forecast_input_request.inputs), {}
        for idx, input_data in enumerate(create_forecast_input_request.inputs):

            input_obj = InputModel()
            input_obj.forecast_id = create_forecast_input_request.forecast_id
            input_obj.time_grain = input_data.time_grain.value
            input_obj.entity_id = input_data.entity_id
            input_obj.start_time = datetime.strptime(
                input_data.start_time, "%Y-%m-%d %H:%M:%S"
            )
            # TODO: alembic auto-creation led to incorrect values
            input_obj.is_approved = InputStatus.PENDING.name
            inputs[idx] = input_obj

            input_values[idx] = []
            for value in input_data.values:
                input_value = InputValueModel()
                try:
                    input_value.metric_id = metric_name_to_ids[value.metric_name]
                except KeyError:
                    logger.error(f"Metric not found: {value.metric_name}")
                    raise ValueError(f"Invalid metric name: {value.metric_name}")
                input_value.value = value.value
                input_values[idx].append(input_value)

        if not inputs:
            return []

        all_input_values = []

        try:
            with self.rds_client.session_scope() as db:
                db.add_all(inputs)
                db.flush()

                for idx, input_obj in enumerate(inputs):
                    db.refresh(input_obj)
                    for input_value in input_values[idx]:
                        input_value.input_id = input_obj.id
                        all_input_values.append(input_value)

                db.add_all(all_input_values)

                logger.debug(f"Created {len(inputs)} inputs")

                return create_forecast_input_request.inputs
        except Exception as e:
            logger.error(f"Error creating inputs: {e}")
            raise e

    def update_input_status(self, inputs: List[UpdateInputStatus]):
        with self.rds_client.session_scope() as db:
            for input_data in inputs:
                query = db.query(InputModel).filter(
                    InputModel.id == input_data.input_id
                )
                try:
                    input_obj = query.one()
                except NoResultFound:
                    logger.error(f"Input not found: {input_data.input_id}")
                    raise ValueError(f"Invalid input id: {input_data.input_id}")
                input_obj.is_approved = input_data.status.name
            db.flush()
            logger.debug(f"Updated {len(inputs)} inputs")
            return inputs

    def get_entity_forecast_models(self, forecast_id: int) -> List[Model]:
        with self.rds_client.session_scope() as db:
            query = (
                db.query(ModelModel)
                .join(ForecastModelModel)
                .filter(ForecastModelModel.forecast_id == forecast_id)
            )
            models = query.all()
            return [
                Model(
                    model_id=model.id,
                    model_name=model.display_name,
                    model_description=model.display_name,
                )
                for model in models
            ]

    def update_forecast_model(self, forecast_id: int, model_id: str) -> int:
        with self.rds_client.session_scope() as db:
            try:
                model = ForecastModelModel(forecast_id=forecast_id, model_id=model_id)
                db.add(model)
                db.flush()
                db.refresh(model)
            except IntegrityError as e:
                db.rollback()
                error_msg = str(e)
                logger.error(f"Failed to add forecast model: {error_msg}")
                raise ValueError(f"Model already exists for forecast: {forecast_id}")

            logger.debug(f"Updated forecast model: {model}")

            return model.id

    def update_forecast_entity_model(
        self,
        entity: str,
        forecast_id: int,
        entity_value: int,
        model_id: str,
    ) -> int:
        with self.rds_client.session_scope() as db:

            query = (
                db.query(ForecastModelModel)
                .join(ModelModel)
                .filter(
                    ForecastModelModel.forecast_id == forecast_id,
                    ModelModel.id == model_id,
                )
            )
            forecast_model = query.one()

            query = db.query(EntityModel).filter(EntityModel.id == entity)
            entity = query.one()

            model = ForecastModelEntityModel(
                forecast_models_id=forecast_model.id,
                entity_id=entity.id,
                entity_value=entity_value,
            )
            db.add(model)
            db.flush()
            db.refresh(model)

            logger.debug(f"Updated forecast entity model: {model}")

            return model.id
