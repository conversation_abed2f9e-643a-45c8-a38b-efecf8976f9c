import logging
from typing import Union
from backend.app.core.config import Settings
import backend.app.crud.utils as utils
from backend.app.db.starrocks_async_client import StarRocksAsyncClient
from backend.app.crud.queries.query_health_metrics import (
    HealthMetrics as HealthQueryMetrics,
)
from backend.app.schemas.utils import AggregationGranularity
from backend.app.schemas.metrics import Metric
from backend.app.schemas.definitions.store_metrics import StoreMetricsModel
from backend.app.schemas.store_health import StoreHealth, StoreHealthStatus

logger = logging.getLogger(__name__)
settings = Settings()


class HealthMetrics:
    def __init__(self):
        store_model = StoreMetricsModel()
        self.query_metrics = HealthQueryMetrics(
            store_model,
            f"{settings.ICEBERG_CATALOG}.storeops_etls.planner_instore_metrics_hourly",
        )
        self.starrocks_client = StarRocksAsyncClient()
        self.metrics_model = store_model

    async def get_health(
        self,
        aggregation_granularity: AggregationGranularity,
        aggregation_id: Union[str, int, None],
        start_time: str,
        end_time: str,
    ) -> StoreHealth:

        query, params = self.query_metrics.get_health_metrics(
            aggregation_granularity, aggregation_id, start_time, end_time
        )
        logger.debug(f"Executing query: {query}")
        logger.debug(f"Params: {params}")

        result = await self.starrocks_client.execute_query(query, params)
        rows = result.fetchall()
        cols = list(result.keys())
        row = rows[0]

        logger.debug(f"Result: {row}")
        logger.debug(f"Columns: {cols}")

        store_health = StoreHealth(
            store_health=StoreHealthStatus[row[cols.index("store_health")]],
            picker_surge_pct=Metric(
                metric_name=self.metrics_model.health_metrics["picker_surge_pct"][
                    "metric_name"
                ],
                display_name=self.metrics_model.health_metrics["picker_surge_pct"][
                    "display_name"
                ],
                metric_type=self.metrics_model.health_metrics["picker_surge_pct"][
                    "metric_type"
                ],
                metric_value=utils.parse_value_from_db(
                    row[cols.index("picker_surge_pct")],
                    self.metrics_model.health_metrics["picker_surge_pct"][
                        "metric_type"
                    ],
                ),
            ),
            picker_utilization_fixed=Metric(
                metric_name=self.metrics_model.health_metrics[
                    "picker_utilization_fixed"
                ]["metric_name"],
                display_name=self.metrics_model.health_metrics[
                    "picker_utilization_fixed"
                ]["display_name"],
                metric_type=self.metrics_model.health_metrics[
                    "picker_utilization_fixed"
                ]["metric_type"],
                metric_value=utils.parse_value_from_db(
                    row[cols.index("picker_utilization_fixed")],
                    self.metrics_model.health_metrics["picker_utilization_fixed"][
                        "metric_type"
                    ],
                ),
            ),
            r2a_within_10_sec=Metric(
                metric_name=self.metrics_model.health_metrics["r2a_within_10_sec"][
                    "metric_name"
                ],
                display_name=self.metrics_model.health_metrics["r2a_within_10_sec"][
                    "display_name"
                ],
                metric_type=self.metrics_model.health_metrics["r2a_within_10_sec"][
                    "metric_type"
                ],
                metric_value=utils.parse_value_from_db(
                    row[cols.index("r2a_within_10_sec")],
                    self.metrics_model.health_metrics["r2a_within_10_sec"][
                        "metric_type"
                    ],
                ),
            ),
        )

        return store_health
