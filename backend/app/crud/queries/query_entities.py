from backend.app.core.config import Settings
from backend.app.crud.utils import convert_to_sql_row_string
from backend.app.schemas.definitions.stores import StoreModel
from backend.app.schemas.definitions.events import EventModel


class Dims:

    store_model = StoreModel()
    event_model = EventModel()

    def get_query_dim_outlets(self):
        # TODO: Hardcoded since distinct was not implemented dynamically, modify this
        query = f"""
        SELECT
            DISTINCT(id) as entity_id, name as entity_name, city_id as city_id, city_name as city_name
        FROM {self.store_model.table_name}
        """
        return query

    def get_query_dim_events(self):
        # TODO: Hardcoded since query was too complex, make this as dynamic as possible
        query = f"""
        SELECT
            a.event_dt,
            a.event_id,
            a.event_name,
            b.event_dt_last_yr
        FROM (
            SELECT
                date AS event_dt,
                event_id,
                event_name,
                ROW_NUMBER() OVER (
                    PARTITION BY event_name
                    ORDER BY date DESC
                ) AS rn
            FROM {self.event_model.table_name}
        ) a
        LEFT JOIN (
            SELECT
                date AS event_dt_last_yr,
                event_name AS base_event_name,
                EXTRACT(YEAR FROM date) AS year,
                ROW_NUMBER() OVER (
                    PARTITION BY
                        event_name,
                        EXTRACT(YEAR FROM date)
                    ORDER BY date DESC
                ) AS rn
            FROM
                {self.event_model.table_name}
        ) b
            ON a.event_name = b.base_event_name
            AND EXTRACT(YEAR FROM a.event_dt) = b.year + 1
            AND b.rn = 1
        WHERE
            a.rn = 1
        ORDER BY
            a.event_dt DESC;
        """
        return query
