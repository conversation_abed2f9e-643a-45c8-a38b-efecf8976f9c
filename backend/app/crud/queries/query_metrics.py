from typing import List, Dict, Any, Union, Type
from functools import cached_property
from backend.app.schemas.definitions.stores import StoreModel
from backend.app.schemas.utils import ComparisonPeriod, AggregationGranularity
import backend.app.crud.utils as utils


class QueryMetrics:
    def __init__(self, entity: str, metrics_model: Dict[str, Dict]):
        self.entity = entity
        self.metrics_model = metrics_model
        self.entity_filter_table = StoreModel.table_name

    @cached_property
    def __get_base_table(self) -> str:
        category_names = set(
            [metric["category_name"] for metric in self.metrics_model.values()]
        )
        category_names = list(category_names)
        get_table_name = (
            lambda category_name: f"{self.entity}.planner_{category_name}_metrics_hourly"
        )
        res = f"""
        FROM
            {get_table_name(category_names[0])} AS {category_names[0]}
        """
        for category_name in category_names[1:]:
            res += f"""
            OUTER JOIN
                {get_table_name(category_name)} AS {category_name}
            ON
                {category_names[0]}.date = {category_name}.date
                AND {category_names[0]}.hour = {category_name}.hour
                AND {category_names[0]}.entity_id = {category_name}.entity_id
            """
        return res

    def get_period_query(
        self,
        aggregation_granularity: AggregationGranularity,
        aggregation_id: Union[str, int, None],
        start_time: str,
        end_time: str,
        select_cols: List[str],
        period: str,
        detailed: bool,
    ) -> tuple[str, Dict[str, Any]]:

        params = {f"period_{period}": period}

        location_clause, location_params = utils.get_filter_clause_location(
            aggregation_granularity, period, aggregation_id
        )
        params.update(location_params)
        period_clause, period_params = utils.get_filter_clause_period(
            period, start_time, end_time
        )
        params.update(period_params)

        select_func = (
            utils.convert_to_sql_row_string
            if ((aggregation_granularity == AggregationGranularity.ENTITY) and detailed)
            else utils.convert_to_sql_aggregate_row_string
        )

        query = f"""(
        SELECT
            {"date, hour," if detailed else ""}
            {select_func(select_cols, self.metrics_model)},
            :period_{period} as period
        FROM
            {self.__get_base_table}
        INNER JOIN
                (SELECT DISTINCT id, city_name from {self.entity_filter_table}) as city_filter
            ON
                city_filter.id = base_table.entity_id
        WHERE
            {utils.combine_filter_clauses([location_clause, period_clause])}
        {
            f"GROUP BY date, hour" if (aggregation_granularity != AggregationGranularity.ENTITY and detailed) else ""
        }
        )"""

        return query, params

    def get_query(
        self,
        aggregation_granularity: AggregationGranularity,
        aggregation_id: Union[str, int, None],
        comparison_periods: List[ComparisonPeriod],
        select_cols: List[str],
        detailed: bool,
    ) -> tuple[str, Dict[str, Any]]:

        queries_and_params = [
            self.get_period_query(
                aggregation_granularity,
                aggregation_id,
                comparison_period.start_time,
                comparison_period.end_time,
                select_cols,
                comparison_period.name,
                detailed,
            )
            for comparison_period in comparison_periods
        ]

        select_queries = []
        all_params = {}
        for query, params in queries_and_params:
            select_queries.append(query)
            all_params.update(params)

        select_query = "UNION ALL".join(select_queries)
        order_clause = "ORDER BY date, hour asc" if detailed else ""

        query = select_query + order_clause

        return query, all_params

    def get_detailed_query(
        self,
        aggregation_granularity: AggregationGranularity,
        aggregation_id: Union[str, int, None],
        comparison_periods: List[ComparisonPeriod],
        select_cols: List[str],
    ) -> tuple[str, Dict[str, Any]]:
        return self.get_query(
            aggregation_granularity,
            aggregation_id,
            comparison_periods,
            select_cols,
            True,
        )

    def get_aggregated_query(
        self,
        aggregation_granularity: AggregationGranularity,
        aggregation_id: Union[str, int, None],
        comparison_periods: List[ComparisonPeriod],
        select_cols: List[str],
    ) -> tuple[str, Dict[str, Any]]:
        return self.get_query(
            aggregation_granularity,
            aggregation_id,
            comparison_periods,
            select_cols,
            False,
        )
