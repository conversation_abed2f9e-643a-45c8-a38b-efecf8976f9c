from typing import Dict, Any, Union
from backend.app.schemas.utils import AggregationGranularity
import backend.app.crud.utils as utils
from backend.app.schemas.definitions.stores import StoreModel


class HealthMetrics:
    def __init__(self, metrics_model, table_name):
        self.metrics_model = metrics_model
        self.table_name = table_name
        self.entity_filter_table = StoreModel.table_name

    def get_health_metrics(
        self,
        aggregation_granularity: AggregationGranularity,
        aggregation_id: Union[str, int, None],
        start_time: str,
        end_time: str,
    ) -> tuple[str, Dict[str, Any]]:
        params = {}
        location_clause, location_params = utils.get_filter_clause_location(
            aggregation_granularity, "current", aggregation_id
        )
        params.update(location_params)
        period_clause, period_params = utils.get_filter_clause_period(
            "current", start_time, end_time
        )
        params.update(period_params)

        select_cols = [
            metric["metric_name"]
            for metric in self.metrics_model.health_metrics.values()
        ]

        metrics_subquery = f"""
        SELECT
            {utils.convert_to_sql_aggregate_row_string(select_cols, self.metrics_model.health_metrics)}
        FROM {self.table_name} AS sub_table
        LEFT JOIN
            {self.entity_filter_table}
        ON
            {self.entity_filter_table}.id = sub_table.entity_id
        WHERE {utils.combine_filter_clauses([location_clause, period_clause])}
        """

        critical_clause: str = utils.get_threshold_clauses(
            select_cols, self.metrics_model.health_metrics, "critical"
        )
        okay_clause: str = utils.get_threshold_clauses(
            select_cols, self.metrics_model.health_metrics, "okay"
        )

        query = f"""
        SELECT
            *,
            CASE
                WHEN {critical_clause} THEN 'CRITICAL'
                WHEN {okay_clause} THEN 'OKAY'
                ELSE 'GOOD'
            END as store_health
        FROM ({metrics_subquery}) AS metrics
        """

        return query, params
