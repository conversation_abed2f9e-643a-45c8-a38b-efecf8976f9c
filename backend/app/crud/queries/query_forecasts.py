from typing import Tuple, Dict, List, Callable, Any, Optional
from collections import defaultdict
from datetime import datetime
from backend.app.schemas.definitions.stores import StoreModel
from backend.app.schemas.base import ForecastSchema
from backend.app.schemas.utils import TimeGranularity, AggregationGranularity
from backend.app.schemas.forecasts import MultiAggregation, SingleAggregation
import backend.app.crud.utils as utils


class QueryForecasts:
    def __init__(self):
        self.entity_filter_table = StoreModel.table_name

    # TODO: Add entity name to decide database, currently using storeops_etls as default
    def __get_table_name(self, category_name: str, time_grain: TimeGranularity, outputs: bool = False) -> str:
        return f"planner_{category_name}_forecasts_{time_grain.value}" + ("_temp" if outputs else "")

    def __get_select_cols(self, forecast_schema: ForecastSchema) -> List[str]:
        return utils.parse_metrics([], forecast_schema.metric_model)

    def __get_time_cols(self, time_grain: TimeGranularity) -> List[str]:
        # TODO: Look for a better place to store this mapping
        time_cols_map = {
            TimeGranularity.WEEKLY.value: "week_start_date",
            TimeGranularity.DAILY.value: "date",
            TimeGranularity.HOURLY.value: "hour",
        }

        cols = []
        while time_grain is not None:
            cols.append(time_cols_map[time_grain.value])
            time_grain = time_grain.next_grain

        return cols

    def get_base_table_query(
        self, metric_model: Dict[str, Dict], time_grain: TimeGranularity, outputs: bool = False
    ) -> str:
        category_metric_map = defaultdict(list)
        for metric in metric_model.values():
            if "category_name" in metric:
                category_metric_map[metric["category_name"]].append(
                    [metric["metric_name"], metric["select_query"]]
                )
        if len(category_metric_map) == 0:
            raise ValueError("No metrics to return")
        categories = list(category_metric_map.keys())
        time_cols = self.__get_time_cols(time_grain)
        idx_cols = time_cols + ["entity_id", "model_id"]

        def get_coalesce_predicate(tables, col):
            return f"COALESCE({', '.join([f'{table}.{col}' for table in tables])})"

        def get_select_predicate_idx_cols(tables, cols):
            return "".join(
                [f"{get_coalesce_predicate(tables, col)} AS {col}," for col in cols]
            )

        def get_select_predicate_metrics(category, category_metrics):
            return ", ".join(
                [
                    f"{category}.{metric[1]} AS {metric[0]}"
                    for metric in category_metrics

                ]
            )

        def get_update_ts_clause(categories):
            # TODO: Fix this logic, it breaks with multiple categories
            return f'COALESCE({", ".join([f"{category}.updated_ts" for category in categories])}) AS updated_ts,'

        def get_join_predicate(table, comp_tables, comp_cols):
            return f"OUTER JOIN {self.__get_table_name(table, time_grain, outputs)} {table} ON {' AND '.join([get_coalesce_predicate(comp_tables, col) for col in comp_cols])}"

        query = f"""
            SELECT
                {get_select_predicate_idx_cols(categories, idx_cols)}
                {get_update_ts_clause(categories) if outputs else ""}
                {", ".join([get_select_predicate_metrics(category, category_metrics) for category, category_metrics in category_metric_map.items()])}
            FROM
                {self.__get_table_name(categories[0], time_grain, outputs)} {categories[0]}
            {"".join([get_join_predicate(category, categories[:i], idx_cols) for i, category in enumerate(categories[1:])])}
        """
        return query

    def get_forecast_base_query(
        self,
        forecast_ids: List[int],
        multi_aggregation: MultiAggregation,
        time_grain: TimeGranularity,
        start_time: str,
        end_time: str,
        forecast_schema: ForecastSchema,
        detailed: bool = False,
        outputs: bool = False,
        updated_ts: Optional[datetime] = None,
    ) -> Tuple[str, Dict[str, Any]]:
        is_single_entity = (
            multi_aggregation.grain == AggregationGranularity.ENTITY
            and len(multi_aggregation.values) == 1
        )
        forecast_schema = forecast_schema.model_copy(deep=True)

        base_table_query = self.get_base_table_query(
            forecast_schema.metric_model, time_grain, outputs
        )

        time_cols: str = ", ".join(self.__get_time_cols(time_grain))
        select_func: Callable = (
            utils.convert_to_sql_row_string
            if (
                (multi_aggregation.grain == AggregationGranularity.ENTITY)
                and (not detailed)
            )
            or (detailed and is_single_entity)
            else utils.convert_to_sql_aggregate_row_string
        )
        select_cols: List[str] = self.__get_select_cols(forecast_schema)

        period_clause, period_params = utils.get_filter_clause_period(
            "current", start_time, end_time, time_grain
        )
        location_clause, location_params = utils.get_filter_clause_locations(
            multi_aggregation
        )
        updated_ts_clause = ""

        params = {}
        params.update(period_params)
        params.update(location_params)

        if outputs:
            updated_ts_clause = "updated_ts >= :updated_ts"
            updated_ts_param = {"updated_ts": updated_ts}
            params.update(updated_ts_param)

        query = f"""
            WITH base AS (
                {base_table_query}
            )
            SELECT
                {f"{time_cols}," if detailed else ""}
                {f"{f'city_filter.city_name as city,' if multi_aggregation.grain != AggregationGranularity.ENTITY else 'entity_id, model_id,'}" if not detailed else ""}
                {select_func(select_cols, forecast_schema.metric_model)}
            FROM
                base
            INNER JOIN
                (SELECT DISTINCT id, city_name from {self.entity_filter_table}) as city_filter
            ON
                city_filter.id = base.entity_id
            WHERE
                {utils.combine_filter_clauses([
                    period_clause,
                    location_clause,
                    updated_ts_clause
                ])}
            {utils.combine_group_by_clauses([
                time_cols if (detailed and not is_single_entity) else f'city_filter.city_name' if multi_aggregation.grain != AggregationGranularity.ENTITY else ''
            ])}
            ORDER BY {time_cols if detailed else (f'city_filter.city_name' if multi_aggregation.grain != AggregationGranularity.ENTITY else 'entity_id')} asc
        """

        return query, params

    def get_forecast_query(
        self,
        forecast_ids: List[int],
        multi_aggregation: MultiAggregation,
        time_grain: TimeGranularity,
        start_time: str,
        end_time: str,
        forecast_schema: ForecastSchema,
    ) -> Tuple[str, Dict[str, Any]]:
        return self.get_forecast_base_query(
            forecast_ids,
            multi_aggregation,
            time_grain,
            start_time,
            end_time,
            forecast_schema,
        )

    def get_forecast_detailed_query(
        self,
        forecast_ids: List[int],
        single_aggregation: SingleAggregation,
        time_grain: TimeGranularity,
        start_time: str,
        end_time: str,
        forecast_schema: ForecastSchema,
    ) -> Tuple[str, Dict[str, Any]]:
        multiaggregation: MultiAggregation = MultiAggregation(
            values=[single_aggregation.value], grain=single_aggregation.grain
        )
        return self.get_forecast_base_query(
            forecast_ids,
            multiaggregation,
            time_grain,
            start_time,
            end_time,
            forecast_schema,
            detailed=True,
        )

    def get_forecast_outputs_query(
        self,
        forecast_ids: List[int],
        multi_aggregation: MultiAggregation,
        time_grain: TimeGranularity,
        start_time: str,
        end_time: str,
        forecast_schema: ForecastSchema,
        updated_ts: datetime,
        ) -> Tuple[str, Dict[str, Any]]:
        
        return self.get_forecast_base_query(
            forecast_ids,
            multi_aggregation,
            time_grain,
            start_time,
            end_time,
            forecast_schema,
            outputs=True,
            updated_ts=updated_ts,
        )

    def get_city_entity_map_query(self, cities: List[str]) -> Tuple[str, Dict[str, Any]]:
        params = {"cities": cities}
        query = f"""
            SELECT
                city_name, id as entity_id
            FROM
                {self.entity_filter_table}
            WHERE
                city_name IN :cities
        """
        return query, params

    def get_forecast_last_updated_ts_query(self, forecast_table: str) -> Tuple[str, Dict[str, Any]]:
        return f"SELECT MAX(updated_ts) FROM {forecast_table}", {}