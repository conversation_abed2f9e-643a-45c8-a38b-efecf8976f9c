from typing import Optional, List, Dict, Union
import logging
from sqlalchemy.exc import IntegrityError
from backend.app.core.config import Settings
from backend.app.schemas.utils import ComparisonPeriod, AggregationGranularity
from backend.app.schemas.base import MetricSchema
from backend.app.schemas.metrics import MetricDefinition
from backend.app.db.rds_sync_client import RDSSyncClient
from backend.app.db.starrocks_async_client import StarRocksAsyncClient
from backend.app.models.metrics import Metrics as MetricModel, metric_dependencies
from backend.app.models.categories import Categories as CategoryModel
from backend.app.models.entities import Entities as EntityModel
from backend.app.crud.queries.query_metrics import QueryMetrics
import backend.app.crud.utils as utils

logger = logging.getLogger(__name__)

rds_client = RDSSyncClient()
settings = Settings()


async def list_metrics(tenant: str, entity: str, category_id: Union[int, None]):
    logger.debug(
        f"Listing metrics for tenant: {tenant}, entity: {entity}, category_id: {category_id}"
    )

    with rds_client.session_scope() as db:

        required_categories = {}
        if category_id:
            required_categories[category_id] = (
                db.query(CategoryModel)
                .filter(CategoryModel.id == category_id)
                .one()
                .name
            )
        else:
            query = (
                db.query(CategoryModel)
                .join(EntityModel)
                .filter(EntityModel.name == entity)
            )
            required_categories = {
                category.id: category.name for category in query.all()
            }

        category_wise_metrics = {}
        for category_id, category_name in required_categories.items():
            query = db.query(MetricModel).filter(MetricModel.category_id == category_id)
            metrics = query.all()
            category_wise_metrics[category_name] = [
                {
                    "metric_name": metric.name,
                    "display_name": metric.display_name,
                    "metric_type": metric.metric_type.value,
                }
                for metric in metrics
            ]

        return {"tenant": tenant, "entity": entity, "categories": category_wise_metrics}


async def register_metrics(
    tenant: str, entity: str, category_id: int, metric_definition: MetricDefinition
):
    logger.debug(
        f"Registering metrics for tenant: {tenant}, entity: {entity}, category_id: {category_id}"
    )

    category_name, metric_id = None, None
    with rds_client.session_scope() as db:
        query = db.query(CategoryModel).filter(CategoryModel.id == category_id)
        if query.count() == 0:
            raise Exception(f"Category {category_id} not found")
        category = query.one()
        category_name = category.name
        logger.debug(f"Found category: {category.name}")

        metric = MetricModel(
            name=metric_definition.metric_name,
            display_name=metric_definition.display_name,
            metric_type=metric_definition.metric_type.name,
            select_query=metric_definition.select_query,
            aggregate_query=metric_definition.aggregate_query,
            grain=metric_definition.grain,
            category_id=category_id,
        )
        db.add(metric)
        db.flush()
        db.refresh(metric)
        metric_id = metric.id
        logger.debug(f"Created metric: {metric.name}")

        # TODO: consider adding a check for circular dependencies
        try:
            if metric_definition.dependency_metrics:
                dependencies = [
                    {"dependent_metric_id": metric_id, "dependency_metric_id": dep_metric_id}
                    for dep_metric_id in metric_definition.dependency_metrics
                ]
                db.execute(metric_dependencies.insert(), dependencies)
                db.flush()

        except IntegrityError as e:
            db.rollback()
            error_msg = str(e)
            logger.error(f"Failed to add metric dependencies: {error_msg}")
            raise ValueError(
                "One or more dependency metrics do not exist in the database"
            )

    return {
        "tenant": tenant,
        "entity": entity,
        "category": category_name,
        "metric_id": metric_id,
        "metric_name": metric_definition.metric_name,
        "display_name": metric_definition.display_name,
        "metric_type": metric_definition.metric_type.value,
        "select_query": metric_definition.select_query,
        "aggregate_query": metric_definition.aggregate_query,
        "grain": metric_definition.grain,
        "table_details": {
            "db_type": "trino",
            "schema": f"{entity}_etls",
            "table": f"planner_{category_name}_metrics",
            "catalog": f"{tenant}",
            "column_dtypes": {
                "date": "date",
                "hour": "int",
                "entity_id": "int",
                metric_definition.metric_name: "float",
            },
        },
    }


class CrudMetrics:
    def __init__(self, tenant: str, entity: str, category_ids: List[int]):

        if len(category_ids) == 0:
            raise ValueError("No category ids provided")

        self.starrocks_client = StarRocksAsyncClient()
        self.tenant = tenant
        self.entity = entity
        self.categories = self._get_categories(category_ids)
        self.metrics_model = self._get_metric_model()
        self.query_metrics = QueryMetrics(self.entity, self.metrics_model)
        self.metrics_schema = MetricSchema(self.metrics_model)
        self.BaseMetric = self.metrics_schema.base_schema
        self.HourlyMetric = self.metrics_schema.hourly_schema
        self.AggregatedMetric = self.metrics_schema.aggregated_schema

    def _get_categories(self, category_ids: List[int]) -> Dict[int, str]:
        category_mapping = {}
        with rds_client.session_scope() as db:
            query = db.query(CategoryModel).filter(CategoryModel.id.in_(category_ids))
            categories = query.all()
            category_mapping = {category.id: category.name for category in categories}
            for category_id in category_ids:
                if category_id not in category_mapping:
                    raise ValueError(f"Category {category_id} not found")
        logger.debug(f"Found categories: {category_mapping}")
        return category_mapping

    def _get_metric_model(self) -> Dict[str, Dict]:
        metric_model = {}
        with rds_client.session_scope() as db:
            query = db.query(MetricModel).filter(
                MetricModel.category_id.in_(self.categories.keys())
            )
            metrics = query.all()
            metric_model = {
                metric.name: {
                    "metric_name": metric.name,
                    "display_name": metric.display_name,
                    "select_query": metric.select_query,
                    "aggregate_query": metric.aggregate_query,
                    "metric_type": metric.metric_type,
                    "category_name": self.categories[metric.category_id],
                }
                for metric in metrics
            }

        if len(metric_model) == 0:
            raise ValueError(
                f"No metrics found for provided category ids - {self.categories.keys()}"
            )

        return metric_model

    async def get_detailed_metrics(
        self,
        aggregation_granularity: AggregationGranularity,
        aggregation_id: Union[str, int, None],
        comparison_periods: List[ComparisonPeriod],
        metrics: Optional[List] = None,
    ) -> dict[str, List]:
        select_cols: List[str] = utils.parse_metrics(metrics, self.metrics_model)

        logger.debug(
            f"Getting detailed metrics for aggregation_granularity: {aggregation_granularity}, aggregation_id: {aggregation_id}, comparison_periods: {comparison_periods}, select_cols: {select_cols}"
        )

        query, params = self.query_metrics.get_detailed_query(
            aggregation_granularity, aggregation_id, comparison_periods, select_cols
        )

        result = await self.starrocks_client.execute_query(query, params)

        rows = result.fetchall()
        logger.debug(f"Result: {rows}")
        if len(rows) == 0:
            raise ValueError("No data found for the given time range")
        columns = list(result.keys())
        logger.debug(f"Columns: {columns}")

        hourly_metrics = utils.parse_hourly_rows(
            rows, self.metrics_model, columns, self.HourlyMetric
        )

        return hourly_metrics

    async def get_aggregated_metrics(
        self,
        aggregation_granularity: AggregationGranularity,
        aggregation_id: Union[str, int, None],
        comparison_periods: List[ComparisonPeriod],
        metrics: Optional[List] = None,
    ) -> dict[str, object]:
        select_cols = utils.parse_metrics(metrics, self.metrics_model)

        logger.debug(
            f"Getting aggregated order metrics for aggregation_granularity: {aggregation_granularity}, aggregation_id: {aggregation_id}, comparison_periods: {comparison_periods}, select_cols: {select_cols}"
        )

        query, params = self.query_metrics.get_aggregated_query(
            aggregation_granularity, aggregation_id, comparison_periods, select_cols
        )

        result = await self.starrocks_client.execute_query(query, params)

        rows = result.fetchall()
        logger.debug(f"Result: {rows}")
        if len(rows) == 0:
            raise ValueError("No data found for the given time range")
        columns = list(result.keys())
        logger.debug(f"Columns: {columns}")

        aggregated_metric = utils.parse_aggregated_rows(
            rows, self.metrics_model, columns, comparison_periods, self.AggregatedMetric
        )

        return aggregated_metric
