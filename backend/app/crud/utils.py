import datetime
from pydantic import BaseModel
from typing import List, Union, Dict, Tuple, Any, Optional
from collections import defaultdict, namedtuple
import logging
from backend.app.schemas.metrics import Metric, MetricType
from backend.app.schemas.utils import (
    ComparisonPeriod,
    AggregationGranularity,
    TimeGranularity,
)
from backend.app.schemas.forecasts import MultiAggregation, ForecastRow
from backend.app.schemas.inputs import ForecastInput, InputValue

logger = logging.getLogger(__name__)


def split_date_and_time(date_time_str):
    try:
        date_time_obj = datetime.datetime.strptime(date_time_str, "%Y-%m-%d %H:%M:%S")
    except ValueError:
        raise ValueError(
            "Invalid datetime format. Expected format: 'YYYY-MM-DD HH:MM:SS'"
        )

    date_str = date_time_obj.strftime("%Y-%m-%d")
    time_str = date_time_obj.strftime("%H:%M:%S")

    return date_str, time_str


def split_date_and_hour(date_time_str):

    date_str, time_str = split_date_and_time(date_time_str)
    hour_str = time_str.split(":")[0]
    hour_str = int(hour_str)

    return date_str, hour_str


def get_time_period(start_time: str, time_grain: TimeGranularity) -> Tuple[str, str]:

    if time_grain == TimeGranularity.HOURLY:
        delta = datetime.timedelta(hours=1)
    elif time_grain == TimeGranularity.DAILY:
        delta = datetime.timedelta(days=1)
    elif time_grain == TimeGranularity.WEEKLY:
        delta = datetime.timedelta(weeks=1)
    else:
        raise ValueError(f"Invalid time grain: {time_grain}")

    end_time = (
        datetime.datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S") + delta
    ).strftime("%Y-%m-%d %H:%M:%S")

    return start_time, end_time


# TODO: make requested_metrics optional
def parse_metrics(
    requested_metrics: Union[List[str], None], table_metrics: Dict[str, Dict]
) -> List[str]:

    if not requested_metrics:
        return list(table_metrics.keys())

    for metric_name in requested_metrics:
        if metric_name not in table_metrics.keys():
            raise ValueError(f"Invalid metric name: {metric_name}")
    return requested_metrics


def get_filter_clause_location(
    aggregation_granularity: AggregationGranularity,
    period: str,
    aggregation_id: Union[str, int, None],
) -> Tuple[str, Dict[str, str]]:
    if aggregation_granularity == AggregationGranularity.ENTITY:
        return f"(entity_id = :aggregation_id_{period})", {
            f"aggregation_id_{period}": aggregation_id
        }
    elif aggregation_granularity == AggregationGranularity.CITY:
        return (
            f"(city_filter.city_name = :aggregation_id_{period})",
            {f"aggregation_id_{period}": aggregation_id},
        )
    elif aggregation_granularity == AggregationGranularity.PAN_INDIA:
        return "", {}
    else:
        raise ValueError(f"Invalid aggregation granularity: {aggregation_granularity}")


# TODO: merge this function with get_filter_clause_location once we refactor playground apis (note that case of empty input is handled different in both flows)
def get_filter_clause_locations(
    multi_aggregation: MultiAggregation,
) -> Tuple[str, Dict[str, str]]:
    if len(multi_aggregation.values) == 0:
        return "", {}
    # TODO: making a check for if the input is city or store based on type of input, this is incorrect
    if not all(
        isinstance(x, type(multi_aggregation.values[0]))
        for x in multi_aggregation.values
    ):
        raise ValueError(
            f"Invalid multi aggregation values: {multi_aggregation.values}"
        )
    # If possible to convert to int, convert it
    multi_aggregation.values = [
        int(value.strip()) if value.strip().isdigit() else value.strip()
        for value in multi_aggregation.values
        if value
    ]
    if multi_aggregation.grain == AggregationGranularity.PAN_INDIA:
        return "", {}
    elif type(multi_aggregation.values[0]) == str:
        return (
            "(city_filter.city_name in :aggregation_ids)",
            {"aggregation_ids": multi_aggregation.values},
        )
    elif type(multi_aggregation.values[0]) == int:
        return "(entity_id in :aggregation_ids)", {
            "aggregation_ids": multi_aggregation.values
        }
    else:
        raise ValueError(
            f"Invalid multi aggregation values: {multi_aggregation.values}"
        )


# TODO: make period optional
def get_filter_clause_period(
    period: str,
    start_time: str,
    end_time: str,
    time_grain: TimeGranularity = TimeGranularity.HOURLY,
) -> Tuple[str, Dict[str, str]]:
    start_dt, start_hr = split_date_and_hour(start_time)
    end_dt, end_hr = split_date_and_hour(end_time)
    if time_grain == TimeGranularity.WEEKLY:
        query = f"""(week_start_date >= :start_dt_{period} and week_start_date < :end_dt_{period})"""
        params = {
            f"start_dt_{period}": start_dt,
            f"end_dt_{period}": end_dt,
        }
    elif time_grain == TimeGranularity.DAILY:
        query = f"""(date >= :start_dt_{period} and date < :end_dt_{period})"""
        params = {
            f"start_dt_{period}": start_dt,
            f"end_dt_{period}": end_dt,
        }
    elif time_grain == TimeGranularity.HOURLY:
        query = f"""((date = :start_dt_{period} and hour >= :start_hr_{period})
                or (date = :end_dt_{period} and hour < :end_hr_{period})
                or (date > :start_dt_{period} and date < :end_dt_{period}))"""
        params = {
            f"start_dt_{period}": start_dt,
            f"start_hr_{period}": start_hr,
            f"end_dt_{period}": end_dt,
            f"end_hr_{period}": end_hr,
        }
    else:
        raise ValueError(f"Invalid time grain: {time_grain}")
    return query, params


def combine_filter_clauses(filter_clauses: List[str]) -> str:
    return "(" + " and ".join(clause for clause in filter_clauses if clause) + ")"


def combine_group_by_clauses(group_by_clauses: List[str]) -> str:
    clauses = ", ".join(clause for clause in group_by_clauses if clause)
    return f"GROUP BY {clauses}" if clauses else ""


def get_threshold_clauses(
    select_cols: List[str], table_metrics: Dict[str, Dict], threshold: str
) -> str:
    clauses = []
    for metric_name in select_cols:
        metric = table_metrics[metric_name]
        if threshold in metric["thresholds"]:
            min_val = metric["thresholds"][threshold]["min"]
            max_val = metric["thresholds"][threshold]["max"]

            if max_val == float("inf"):
                clauses.append(f"({metric_name} >= {min_val})")
            else:
                clauses.append(
                    f"({metric_name} >= {min_val} and {metric_name} < {max_val})"
                )

    return " or ".join(clauses)


def convert_to_sql_row_string(
    requested_metrics: List[str], table_metrics: Dict[str, Dict]
) -> str:
    sql_cols = []
    for metric_name in requested_metrics:
        if table_metrics[metric_name].get("dependency_metric", False):
            continue
        sql_cols.append(
            f"{table_metrics[metric_name]['select_query']} as {metric_name}"
        )
    return ", ".join(sql_cols)


def convert_to_sql_aggregate_row_string(
    requested_metrics: List[str], table_metrics: Dict[str, Dict]
) -> str:
    sql_cols = []
    for metric_name in requested_metrics:
        if table_metrics[metric_name].get("dependency_metric", False):
            continue
        sql_cols.append(
            f"{table_metrics[metric_name]['aggregate_query']} as {metric_name}"
        )
    return ", ".join(sql_cols)


def is_starrocks_infinity(value):
    STARROCKS_INFINITY = "inf"
    if isinstance(value, float) and (value == float("inf") or value == float("-inf")):
        return True
    if isinstance(value, str) and value.strip().lower() == STARROCKS_INFINITY:
        return True
    return False


def parse_value_from_db(
    value: Any, expected_type: Union[str, MetricType]
) -> Union[int, float, str]:
    try:
        if is_starrocks_infinity(value):
            logger.warning(f"DB returned Infinity for expected type: {expected_type}")
            return None
        elif value is None:
            return "" if expected_type == "str" else 0
        elif expected_type == "str":
            return str(value)
        elif expected_type in [
            MetricType.COUNT,
            MetricType.SECONDS,
            MetricType.MINUTES,
            MetricType.HOURS,
        ]:
            return round(int(value), 2)
        else:
            return round(float(value), 2)
    except ValueError:
        logger.error(
            f"DB returned invalid value: {value} for expected type: {expected_type}"
        )
        return None
    except Exception as e:
        logger.error(f"Error parsing value: {value} for expected type: {expected_type}")
        logger.error(e)
        raise e


def parse_hourly_rows(
    rows: List,
    table_metrics: Dict[str, Dict],
    requested_metrics: List[str],
    HourlyMetricSchema: BaseModel,
) -> Dict[str, List]:
    BaseMetricSchema = HourlyMetricSchema.__annotations__["metrics"]
    period_idx = requested_metrics.index("period")

    result = defaultdict(list)
    for row in rows:

        period = row[period_idx]
        result[period].append(
            HourlyMetricSchema(
                hour=row[1],
                date=row[0],
                metrics=BaseMetricSchema(
                    **{
                        metric_name: Metric(
                            metric_name=table_metrics[metric_name]["metric_name"],
                            display_name=table_metrics[metric_name]["display_name"],
                            metric_type=table_metrics[metric_name]["metric_type"].value,
                            metric_value=parse_value_from_db(
                                row[requested_metrics.index(metric_name)],
                                table_metrics[metric_name]["metric_type"],
                            ),
                        )
                        for metric_name in requested_metrics
                        if metric_name not in ["date", "hour", "period"]
                    }
                ),
            )
        )

    return dict(result)


def parse_aggregated_rows(
    rows: List[Tuple],
    table_metrics: Dict[str, Dict],
    requested_metrics: List[str],
    comparison_periods: List[ComparisonPeriod],
    AggregatedMetricSchema: BaseModel,
) -> dict[str, BaseModel]:

    BaseMetricSchema = AggregatedMetricSchema.__annotations__["metrics"]
    requested_metrics.remove("period")

    result = {}
    for row in rows:

        period_name = row[-1]
        period = next(
            (period for period in comparison_periods if period.name == period_name),
            None,
        )
        if period is None:
            raise ValueError(f"Invalid comparison period in sql result: {period_name}")
        start_time = period.start_time
        end_time = period.end_time

        result[period_name] = AggregatedMetricSchema(
            time_range={"start_time": start_time, "end_time": end_time},
            metrics=BaseMetricSchema(
                **{
                    metric_name: Metric(
                        metric_name=table_metrics[metric_name]["metric_name"],
                        display_name=table_metrics[metric_name]["display_name"],
                        metric_type=table_metrics[metric_name]["metric_type"].value,
                        metric_value=parse_value_from_db(
                            row[idx], table_metrics[metric_name]["metric_type"]
                        ),
                    )
                    for idx, metric_name in enumerate(requested_metrics)
                }
            ),
        )

    return result


def parse_cols_to_dict(
    cols: List[str], metric_model: Dict[str, Dict]
) -> Dict[str, Dict]:

    metric_model["model_id"] = {
        "metric_name": "model_id",
        "display_name": "Model ID",
        "metric_type": "str",
        "editable": True,
    }

    return {
        col: {
            "metric_name": metric_model[col]["metric_name"],
            "display_name": metric_model[col]["display_name"],
            "metric_type": metric_model[col]["metric_type"],
            "editable": metric_model[col]["editable"],
        }
        for col in cols
        if col in metric_model.keys()
    }


def parse_db_output_forecasts(
    rows: List[Tuple],
    cols: List[str],
    metric_model: Dict[str, Dict],
    schema: BaseModel,
    time_grain: Optional[TimeGranularity] = None,
    aggregation_grain: Optional[AggregationGranularity] = None,
) -> List[ForecastRow]:

    if len(rows) == 0:
        return []

    if len(cols) != len(rows[0]):
        raise ValueError(
            "INVALID_RESPONSE FROM DB: Number of columns and number of values in rows do not match"
        )

    if (time_grain is None) == (aggregation_grain is None):
        raise ValueError(
            "Either time grain or aggregation grain should be provided but not both"
        )

    if time_grain is not None:
        hr_idx = cols.index("hour") if "hour" in cols else None
        dt_idx = cols.index("date") if "date" in cols else None
        week_idx = cols.index("week_start_date") if "week_start_date" in cols else None

    if aggregation_grain is not None:
        city_idx = cols.index("city") if "city" in cols else None
        entity_idx = cols.index("entity_id") if "entity_id" in cols else None

    model_idx = cols.index("model_id") if "model_id" in cols else None

    # TODO: might consider using multithreading for this code
    result: Dict[Union[int, str], ForecastRow] = {}
    for row in rows:
        key = None
        if time_grain is not None:
            if time_grain == TimeGranularity.HOURLY:
                key = f"{row[dt_idx]} {row[hr_idx]}:00:00"
                key = datetime.datetime.strptime(
                    key, "%Y-%m-%d %H:%M:%S"
                )
            elif time_grain == TimeGranularity.DAILY:
                key = row[dt_idx]
            elif time_grain == TimeGranularity.WEEKLY:
                key = row[week_idx]
            key = key.strftime("%Y-%m-%d %H:%M:%S") if isinstance(key, datetime.datetime) or isinstance(key, datetime.date) else key
            if key not in result:
                result[key] = ForecastRow(
                    time=key,
                    # TODO: remove the hardcoded "m1" key, replace with default forecast model
                    model_id="m1",
                    values={},
                )
        if aggregation_grain is not None:
            if aggregation_grain == AggregationGranularity.ENTITY:
                key = row[entity_idx]
            elif (
                aggregation_grain == AggregationGranularity.CITY
                or aggregation_grain == AggregationGranularity.PAN_INDIA
            ):
                key = row[city_idx]
            else:
                raise ValueError(f"Invalid aggregation grain: {aggregation_grain}")
            if key.isdigit():
                key = int(key)
            if key not in result:
                result[key] = ForecastRow(
                    entity=key,
                    # TODO: remove the hardcoded "m1" key, replace with default forecast model
                    model_id="m1",
                    values={},
                )
        metric_obj = schema()
        for col_name, value in zip(cols, row):
            if col_name not in metric_model.keys():
                continue
            try:
                metric_obj.__setattr__(col_name, parse_value_from_db(
                    value, metric_model[col_name]["metric_type"]
                ))
            except AttributeError as e:
                logger.error(
                    f"Failed to set attribute {col_name} with value {value} in schema {schema.__name__}"
                )
                raise e
        # TODO: remove the hardcoded "m1" key, replace with default forecast model
        model_id_value = row[model_idx] if model_idx is not None and row[model_idx] is not None else "m1"
        result[key].values[model_id_value] = metric_obj
    res = list(result.values())
    return res

def parse_db_forecast_output(
    rows: List[Tuple],
    cols: List[str],
    time_grain: TimeGranularity,
    start_time: str,
    metric_model: Dict[str, Dict],
) -> List[ForecastInput]:
    
    if len(rows) == 0:
        return []

    if len(cols) != len(rows[0]):
        raise ValueError(
            "INVALID_RESPONSE FROM DB: Number of columns and number of values in rows do not match"
        )

    entity_idx = cols.index("entity_id") if "entity_id" in cols else None
    model_idx = cols.index("model_id") if "model_id" in cols else None

    OutputRow = namedtuple(
                "OutputRow",
                "entity_id, start_time, time_grain",
            )

    cell_wise_values: Dict[OutputRow, Dict[str, InputValue]] = defaultdict(dict)
    model_id = Dict[OutputRow, str] = defaultdict(str)
    updated_ts: Dict[OutputRow, str] = defaultdict(str)
    for row in rows:
        entity_id = row[entity_idx]
        output_row = OutputRow(entity_id, start_time, time_grain)

        for col_name, value in zip(cols, row):
            if value is None or value == "":
                continue
            if col_name not in metric_model.keys():
                logger.warning(f"Column {col_name} not found in metric model")
                continue
            value = parse_value_from_db(value, metric_model[col_name]["metric_type"])
            if cell_wise_values[output_row].get(col_name) is not None:
                continue
            if col_name == "updated_ts" and updated_ts[output_row] == "":
                updated_ts[output_row] = str(value)
            elif col_name == "model_id" and model_id[output_row] == "":
                model_id[output_row] = str(value)
            elif col_name not in ["entity_id", "week_start_date", "date", "hour"]:
                cell_wise_values[output_row][col_name] = InputValue(metric_name=col_name, value=value)

    result = []
    for output_row, values in cell_wise_values.items():
        forecast_output = ForecastInput(
            is_input=False,
            model_id=model_id[output_row],
            entity_id=output_row.entity_id,
            start_time=output_row.start_time,
            time_grain=output_row.time_grain,
            values=list(values.values()),
            updated_at=updated_ts[output_row],
        )
        result.append(forecast_output)

    return result