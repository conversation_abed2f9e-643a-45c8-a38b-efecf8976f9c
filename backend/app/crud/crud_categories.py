import logging
from backend.app.db.rds_sync_client import RDSSyncClient
from backend.app.models.categories import Categories as CategoryModel
from backend.app.models.entities import Entities as EntityModel

logger = logging.getLogger(__name__)


class Categories:
    def __init__(self):
        self.rds_client = RDSSyncClient()

    async def get_categories(self, tenant: str, entity: str):
        with self.rds_client.session_scope() as db:
            query = (
                db.query(CategoryModel)
                .join(EntityModel)
                .filter(EntityModel.name == entity)
            )
            db_categories = query.all()
            logger.debug(f"Found {len(db_categories)} categories")

            categories = [
                {"id": category.id, "name": category.name} for category in db_categories
            ]

        return {"tenant": tenant, "entity": entity, "categories": categories}

    async def create_category(self, tenant: str, entity: str, category_name: str):
        category_id = None
        with self.rds_client.session_scope() as db:
            query = db.query(EntityModel).filter(EntityModel.name == entity)
            if query.count() == 0:
                raise Exception(f"Entity {entity} not found")
            db_entity = query.one()
            logger.debug(f"Found entity: {db_entity.name}")

            category = CategoryModel(name=category_name, entity_id=db_entity.id)
            db.add(category)
            db.flush()
            db.refresh(category)
            logger.debug(f"Created category: {category.name}")
            category_id = category.id

        return {
            "tenant": tenant,
            "entity": entity,
            "category_id": category_id,
            "category_name": category_name,
        }
