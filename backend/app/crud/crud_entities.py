from typing import List
import logging
from datetime import datetime
from backend.app.schemas.events import Event
from backend.app.schemas.stores import Store
import backend.app.crud.queries.query_entities as query_entities
from backend.app.db.starrocks_async_client import StarRocksAsyncClient
from backend.app.db.rds_sync_client import RDSSyncClient
from backend.app.models.tenants import Tenants as TenantModel
from backend.app.models.entities import Entities as EntityModel
from backend.app.schemas.entities import Entity

logger = logging.getLogger(__name__)


class CrudEntities:
    def __init__(self):
        self.starrocks_client = StarRocksAsyncClient()
        self.query_dims = query_entities.Dims()
        self.rds_client = RDSSyncClient()

    async def get_entities(self, tenant: str) -> List[Entity]:
        logger.debug("Getting list of entities")
        entities: List[Entity] = []
        with self.rds_client.session_scope() as db:
            query = db.query(EntityModel).join(
                TenantModel, TenantModel.id == EntityModel.tenant_id
            ).filter(TenantModel.name == tenant)
            res = query.all()
            for entity in res:
                entities.append(
                    Entity(id=entity.id, name=entity.name,)
                )
        if not entities:
            logger.error(f"No entities found for tenant {tenant}")
            return []
        logger.info(f"Found {len(entities)} entities for tenant {tenant}")
        return entities

    async def create_entities(self, tenant: str, entity: str) -> Entity:
        logger.debug(f"Creating entity {entity} for tenant {tenant}")

        with self.rds_client.session_scope() as db:
            tenant_obj = db.query(TenantModel).filter(TenantModel.name == tenant)
            tenants = tenant_obj.all()
            if not tenants or len(tenants) != 1:
                logger.error(f"Tenant {tenant} not found or multiple tenants found")
                raise ValueError(f"Tenant {tenant} not found or multiple tenants found")
            tenant_obj = tenants[0]

            new_entity = EntityModel(name=entity, tenant_id=tenant_obj.id)
            db.add(new_entity)
            db.commit()
            db.flush()
            db.refresh(new_entity)

        return Entity(id=new_entity.id, name=new_entity.name)

    async def get_dim_outlets(self) -> List[Store]:

        logger.debug("Getting list of stores")

        query = self.query_dims.get_query_dim_outlets()
        result = await self.starrocks_client.execute_query(query)

        rows = result.fetchall()
        columns = result.keys()

        stores = [Store(**dict(zip(columns, row))) for row in rows]

        return stores

    async def get_dim_events(self) -> List[Event]:

        logger.debug("Getting list of events")

        query = self.query_dims.get_query_dim_events()
        result = await self.starrocks_client.execute_query(query)

        rows = result.fetchall()
        columns = result.keys()

        vals = [dict(zip(columns, row)) for row in rows]

        events = [
            Event(
                event_dt=val["event_dt"].strftime("%Y-%m-%d %H:%M:%S")
                if val["event_dt"]
                else None,
                event_id=val["event_id"],
                event_name=val["event_name"],
                event_dt_last_year=val["event_dt_last_yr"].strftime("%Y-%m-%d %H:%M:%S")
                if val["event_dt_last_yr"]
                else None,
            )
            for val in vals
        ]

        # events = [Event(**dict(zip(columns, row))) for row in rows]

        return events
