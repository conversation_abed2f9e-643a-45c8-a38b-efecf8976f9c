import logging
from typing import List
from backend.app.db.rds_sync_client import RDS<PERSON>ync<PERSON>lient
from backend.app.models.tenants import Tenants as TenantModel
from backend.app.schemas.tenants import Tenant

logger = logging.getLogger(__name__)


class Tenants:
    def __init__(self):
        self.rds_client = RDSSyncClient()

    async def get_tenants(self)->List[Tenant]:
        with self.rds_client.session_scope() as db:
            query = db.query(TenantModel)
            res = query.all()
            logger.debug(f"Found {len(res)} tenants")
            if not res:
                logger.debug("No tenants found")
                return []
            return [Tenant(id=tenant.id, name=tenant.name) for tenant in res]

    async def create_tenant(self, tenant_name: str)-> Tenant:
        with self.rds_client.session_scope() as db:
            tenant = TenantModel(name=tenant_name)
            db.add(tenant)
            db.commit()
            db.flush()
            db.refresh(tenant)
            logger.debug(f"Created tenant: {tenant.name}")

            return Tenant(id=tenant.id, name=tenant.name)