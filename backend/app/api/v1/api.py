from fastapi import APIRouter
from typing import Optional, List
from backend.app.api.v1.endpoints import (
    entities,
    categories,
    metrics,
    forecast,
    store_health,
    auth,
)
from backend.app.api.v1.endpoints.metrics import (
    list_metrics as list_category_metrics,
    get_combined_category_metrics,
)
from backend.app.api.v1.endpoints.forecast import (
    get_forecasts as get_category_forecasts,
    get_combined_forecast,
    get_combined_forecast_detailed,
    get_combined_forecast_inputs,
)

api_router = APIRouter()

api_router.include_router(entities.router, prefix="/{tenant}", tags=["entities"])

api_router.include_router(
    categories.router, prefix="/{tenant}/{entity}/categories", tags=["categories"]
)

api_router.include_router(
    metrics.router, prefix="/{tenant}/{entity}/{category_id}/metrics", tags=["metrics"]
)

api_router.include_router(auth.router, prefix="/auth", tags=["auth"])

api_router.include_router(
    forecast.router,
    prefix="/{tenant}/{entity}/{category_id}/forecasts",
    tags=["forecast"],
)

# Adding a separate route for listing metrics outside of a category


@api_router.get("/{tenant}/{entity}/metrics")
async def list_metrics(
    tenant: str,
    entity: str,
):
    return await list_category_metrics(tenant, entity, None)


api_router.add_api_route(
    "/{tenant}/{entity}/metrics/viz",
    get_combined_category_metrics,
    methods=["GET"],
    tags=["metrics"],
)


@api_router.get("/{tenant}/{entity}/forecasts")
async def get_forecasts(
    tenant: str,
    entity: str,
):
    return await get_category_forecasts(tenant, entity, None)


api_router.add_api_route(
    "/{tenant}/{entity}/forecasts/viz",
    get_combined_forecast,
    methods=["GET"],
    tags=["forecast"],
)


api_router.add_api_route(
    "/{tenant}/{entity}/forecasts/detailed",
    get_combined_forecast_detailed,
    methods=["GET"],
    tags=["forecast"],
)

api_router.add_api_route(
    "/{tenant}/{entity}/forecasts/inputs",
    get_combined_forecast_inputs,
    methods=["GET"],
    tags=["forecast"],
)

# Hardcoding an endpoint for store health metrics, need to generalise it for other entites later
api_router.include_router(
    store_health.router, prefix="/blinkit/store/health", tags=["store_health"]
)
