from typing import Optional, List, Union, Tuple, Dict
from datetime import datetime, timedelta
from enum import Enum
from pydantic import BaseModel
from backend.app.schemas.utils import (
    ComparisonOptions,
    ComparisonPeriod,
    AggregationGranularity,
)


class ResponseStatus(Enum):
    SUCCESS = "success"
    ERROR = "error"


class SuccessResponse(BaseModel):
    status: str
    data: Optional[Union[Dict, List]] = None

    def __init__(self, data: Optional[Union[Dict, List]] = None):
        super().__init__(status=ResponseStatus.SUCCESS.value, data=data)


def validate_day_of_week(
    start_time: str, day_of_week: int = 0
):  # Setting Monday as default
    start_time = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
    if start_time.weekday() != day_of_week:
        raise ValueError(
            f"Invalid start_time: {start_time} for day_of_week: {day_of_week}"
        )


def parse_aggregation_granularity(
    city: Union[str, None], store_id: Union[int, None]
) -> Tuple[AggregationGranularity, Union[str, int, None]]:
    if store_id is not None:
        return AggregationGranularity.ENTITY, store_id
    elif city is not None:
        return AggregationGranularity.CITY, city
    else:
        return AggregationGranularity.PAN_INDIA, None


def create_comparison_period(
    name: str, start_time: datetime, end_time: datetime, time_delta: timedelta
) -> ComparisonPeriod:
    delta_period = end_time - start_time
    new_start = start_time - time_delta
    new_end = new_start + delta_period
    return ComparisonPeriod(
        name=name,
        start_time=new_start.strftime("%Y-%m-%d %H:%M:%S"),
        end_time=new_end.strftime("%Y-%m-%d %H:%M:%S"),
    )


def parse_comparison_options(
    start_time: str,
    end_time: str,
    comparison: List[str],
    event_date: Optional[Dict[str, str]] = None,
) -> List[ComparisonPeriod]:

    comparisons_periods: List[ComparisonPeriod] = []
    start_time = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
    end_time = datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")

    comparisons_periods.append(
        ComparisonPeriod(
            name="current",
            start_time=start_time.strftime("%Y-%m-%d %H:%M:%S"),
            end_time=end_time.strftime("%Y-%m-%d %H:%M:%S"),
        )
    )

    # No special handling for months of 31 days and leap years
    for option in comparison:
        if option == ComparisonOptions.Do1D.value:
            comparisons_periods.append(
                create_comparison_period(
                    option, start_time, end_time, timedelta(days=1)
                )
            )

        elif option == ComparisonOptions.Wo1W.value:
            comparisons_periods.append(
                create_comparison_period(
                    option, start_time, end_time, timedelta(weeks=1)
                )
            )

        elif option == ComparisonOptions.Wo2W.value:
            comparisons_periods.append(
                create_comparison_period(
                    option, start_time, end_time, timedelta(weeks=2)
                )
            )

        elif option == ComparisonOptions.Mo1M.value:
            comparisons_periods.append(
                create_comparison_period(
                    option, start_time, end_time, timedelta(days=30)
                )
            )

        elif option == ComparisonOptions.Mo2M.value:
            comparisons_periods.append(
                create_comparison_period(
                    option, start_time, end_time, timedelta(days=60)
                )
            )

        elif option == ComparisonOptions.Yo1Y.value:
            if event_date:
                curr_yr_datetime = datetime.strptime(
                    event_date["current_yr_date"], "%Y-%m-%d %H:%M:%S"
                )
                last_yr_datetime = datetime.strptime(
                    event_date["last_yr_date"], "%Y-%m-%d %H:%M:%S"
                )
                time_delta = curr_yr_datetime - last_yr_datetime
                comparisons_periods.append(
                    create_comparison_period(option, start_time, end_time, time_delta)
                )
            else:
                comparisons_periods.append(
                    create_comparison_period(
                        option, start_time, end_time, timedelta(days=365)
                    )
                )

        else:
            raise ValueError(f"Invalid comparison option: {option}")

    return comparisons_periods
