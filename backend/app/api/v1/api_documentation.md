# Planner API Documentation

## Historical Playground APIs


### List Available Entity values

* Endpoint: `GET /api/v1/{tenant}/{entity}`
* Response:

```json
{
    "status": "success",
    "data": [{
        "entity_id": 1234,
        "entity_name": "SS Delhi Rajokri ES308",
        "city_id": 456,
        "city_name": "Delhi"
    }]
}
```

### Get Events

* Endpoint: `GET /api/v1/{tenant}/events`
* Response:

```json
{
    "status": "success",
    "data": [
        {
            "event_dt": "1970-01-01 00:00:00",
            "event_id": "1",
            "event_name": "Holi",
            "event_dt_last_year": "1970-01-01 00:00:00"
        }
    ]
}
```

### Get Available Categories

* Endpoint: `GET /api/v1/{tenant}/{entity}/categories`
* Response:

```json
{
    "status": "success",
    "data": [  
        {  
            "id": 1,  
            "name": "instore"  
        }
    ]
}
```

### Create Category

* Endpoint: `POST /api/v1/{tenant}/{entity}/categories`
* Parameters: 
  * `category_name`: Name of the category to be created
* Response:

```json
{
    "status": "success",
    "data": {
        "id": 1,
        "name": "blinkit_storeops"
    }
}
```

### Get available metrics for department

* Endpoint: 
    * `GET /api/v1/{tenant}/{entity}/{category_id}/metrics`
    * `GET /api/v1/{tenant}/{entity}/metrics`
* Response:
```json
{
    "status": "success",
    "data": {
        "categories":{
            "instore": [
                {
                    "metric_name": "gmv",
                    "display_name": "GMV",
                    "metric_type": "rupees"
                }
            ],
            "core": [
                {
                    "metric_name": "orders",
                    "display_name": "Orders",
                    "metric_type": "count"
                }
            ]
        }
    }
}
```

### Register Metrics Endpoint

* Endpoint: `POST /api/v1/{tenant}/{entity}/{category_id}/metrics`
* Parameters: 
  * `metric_name`: Name of the metric to be registered
  * `display_name`: Display name of the metric
  * `metric_type`: Type of the metric (count, percentage, rupees, etc.)
  * `select_query` (optional): Query to be used for selecting the metric from the database
    * e.g. `items_put_away_per_hour` (only mention the column if the metric is directly available)
  * `aggregate_query` (optional): Query to be used for aggregating the metric from the database
    * e.g. `CAST(SUM(putawayed_qty) AS FLOAT) / NULLIF(CAST(SUM(putter_active_time_mins) AS FLOAT) / 60, 0)` (mention the query to be used for aggregating the metric if it is not directly available, ensure that the base metric is already registered)
  * `grain` (optional): Time grain for the metric
* Response:
  ```json
  {
	"status": "success",
	"data": {
		"metric_id": 2,
		"metric_name": "items_put_away_per_hour",
		"display_name": "IPH",
		"select_query": "items_put_away_per_hour",
		"aggregate_query": "CAST(SUM(putawayed_qty) AS FLOAT) / NULLIF(CAST(SUM(putter_active_time_mins) AS FLOAT) / 60, 0)",
		"metric_type": "HOURS",
		"table_id": 1,
		"table_details": {
			"db_type": "trino",
			"schema": "<entity>_etls",
			"table": "planner_<category>_metrics",
			"catalog": "<tenant>",
            "column_dtypes": {
                "dt": "date",
                "hour": "int",
                "entity_id": "int",
                "items_put_away_per_hour": "float"
            }
		}
	}
  }
  ```

### Get Playground (Historical) Metric Endpoint

* Endpoint: `GET /api/v1/{tenant}/{entity}/{category_id}/metrics/viz` or `GET /api/v1/{tenant}/{entity}/metrics/viz`
* Parameters:  
  * `store_id` (optional, default = None): Store ID for which metrics are requested  
  * `city` (optional, default = None): name of the city for which metrics are requested  
    (if both `store_id` and `city` is left empty, Pan India metrics will be returned)  
  * `start_time`: Starting time for aggregating requested metrics  
    * Format: `YYYY-MM-DD HH:mm:ss`  
  * `end_time`: Ending time for aggregating requested metrics  
    * Format: `YYYY-MM-DD HH:mm:ss`  
<!-- TODO: Handle metrics at custom grain instead of hourly -->
  * `detailed` (optional, default = false): Provide the list of hourly records between start_hour and end_hour  
  * `comparison` (optional, default = None): Provide comparison values based on provided comparison periods.  
    * Format: `comparison = ["do1d", "wo1w", "mo1m", "yo1y"]`
  * `metric_list` (optional, default = all): Provide the list of order metrics required from the endpoint  
  * `event_date` (optional, default = None): set the event_date incase we are setting start_time and end_time around an event, used for YoY comparison.   
      * Format: `event_date = {"current_yr_date": "1970-01-01 00:00:00", "last_yr_date": "1970-01-01 00:00:00" }`
* Response:

Aggregated (if detailed boolean is False):

```json
{
    "status": "success",
    "data": {
        "current": {
            "time_range": {
                "start_time": "1970-01-01 00:00:00",
                "end_time": "1970-01-07 00:00:00"
            },
            "metrics": {
                "orders": {
                    "display_name": "Orders placed",
                    "metric_type": "count",
                    "metric_value": 1021
                },
                "items_per_order": {
                    "display_name": "Avg. number of items per order",
                    "metric_type": "count",
                    "metric_value": 4.9510283
                }
            }
        }
    }
}
```

Detailed (if detailed boolean is True):

```json
{
    "status": "success",
    "data": {
        "current": [
            {
                "hour": 0,
                "date": "2025-04-01",
                "metrics": {
                    "orders": {
                        "display_name": "Orders placed",
                        "metric_type": "count",
                        "metric_value": 18
                    },
                    "items_per_order": {
                        "display_name": "Avg. number of items per order",
                        "metric_type": "count",
                        "metric_value": 3.44
                    }
                }
            },
            {
                "hour": 1,
                "date": "2025-04-01",
                "metrics": {
                    "orders": {
                        "display_name": "Orders placed",
                        "metric_type": "count",
                        "metric_value": 8
                    },
                    "items_per_order": {
                        "display_name": "Avg. number of items per order",
                        "metric_type": "count",
                        "metric_value": 5.25
                    }
                }
            }
        ]
    }
}
```

### Store Health Endpoint

* Endpoint: `GET /api/v1/blinkit/store/health`
* Parameters:  
  * `store_id`: Store ID for which metrics are requested  
  * `city`: name of the city for which metrics are requested     
  (if both `store_id` and `city` is left empty, Pan India metrics will be returned)  
  * `start_time`: Starting time for aggregating requested metrics in `yyyy-MM-dd HH:mm:ss` format.  
  * `end_time`: Ending time for aggregating requested metrics in `yyyy-MM-dd HH:mm:ss` format.  
* Response:

```json
{
	"status": "success",
	"data": {
		"store_health": "good",
        "picker_surge_pct": {
			"metric_name": "picker_surge_pct",
			"display_name": "Picker Surge %",
			"metric_type": "percentage",
			"metric_value": 0.0
		},
		"picker_utilization_fixed": {
			"metric_name": "picker_utilization_fixed",
			"display_name": "Fixed Picker Utilization",
			"metric_type": "percentage",
			"metric_value": 40.0
		},
		"r2a_within_10_sec": {
			"metric_name": "r2a_within_10_sec",
			"display_name": "R2A % under 10 sec",
			"metric_type": "percentage",
			"metric_value": 85.0
		}
	}
}
```

## Planner Forecast APIs

### Register Forecast

* Endpoint: `POST /api/v1/{tenant}/{entity}/{category_id}/forecasts`
* Parameters: 
  * `display_name`: Name of the forecast to be registered
  * `sla`: SLA timestamp 
  * `default_model`: Model ID for the default model to be used for the forecast
  * `approvers`: List of User IDs of each approver for the forecast
    * format - `[1, 2, 3]`
  * `metrics`: List of metric IDs for the forecast along with their time grains for the forecast.
    * format - `[1, 2, 3]`
* Response:
  ```json
  {
    "status": "success",
    "data": {
        "id": 1,
        "display_name": "blinkit_instore_week1",
        "category": 1,
        "sla": "2025-04-01 00:00:00",
        "default_model": "model_1",
        "approvers": [1, 2],
        "metrics": [1, 2, 3]
    }
  }
  ```

### List Forecasts

* Endpoint `GET /api/v1/{tenant}/{entity}/{category_id}/forecasts`
* Response:
  ```json
  {
    "status": "success",
    "data": {
        "1": {
            "id": 1,
            "display_name": "blinkit_instore_week1",
            "sla": "2025-04-01 00:00:00",
            "default_model": "model_1",
            "approvers": [1, 2],
            "metrics": [1, 2, 3]
        }
    }
  }
  ```

### Get Forecast Predictions

* Endpoint: `GET /api/v1/{tenant}/{entity}/{category_id}/forecasts/{forecast_id}` or `GET /api/v1/{tenant}/{entity}/forecasts/{forecast_id}`
* Parameters:  
  * `forecast_id`: Forecast ID for which metrics are requested  (Pass as list in case using at `entity` level)
  * `multi_aggregation_values`: Store IDs/City Names for which metrics are requested, defaults to Pan India.
    * Format: `multi_aggregation_values[]=Delhi&multi_aggregation_values[]=Gurugram`
  * `multi_aggregation_grain`: Aggregation grain (city, store, pan_india)
  * `start_time`: Starting time for aggregating requested metrics  
    * Format: `YYYY-MM-DD HH:mm:ss`  
  * `time_grain`: Time grain (daily, weekly, hourly)
* Response:
  ```json
  {
    "status": "success",
    "data": {
        "idx_cols": {
            "entity": {
                "display_name": "Store/City Names",
                "metric_type": "str"
            }
        },
        "cols": {
            "orders": {
                "display_name": "Orders placed",
                "metric_type": "count"
            },
            "items_per_order": {
                "display_name": "Avg. number of items per order",
                "metric_type": "count"
            }
        },
        "values": [
            {
              "entity": "Delhi",
              "default_model": "m1"
              "values":{
                "m1": {
                  "orders": 100,
                  "items_per_order": 100
                },
                "m2": {
                  "orders": 100,
                  "items_per_order": 100
                }   
              }
            },
            {
                "entity": "Gurugram",
                "default_model": "m1"
                "values":{
                  "m1": {
                    "orders": 100,
                    "items_per_order": 100
                  },
                  "m2": {
                    "orders": 100,
                    "items_per_order": 100
                  }   
                }
            }
        ]
    }
  }
  ```
  <!-- TODO: make values a dict and add an "aggregate column" -->

### Get Forecast Entity Detailed Records

* Endpoint: `GET /api/v1/{tenant}/{entity}/{category_id}/forecasts/{forecast_id}/detailed` or `GET /api/v1/{tenant}/{entity}/forecasts/{forecast_id}/detailed`
* Parameters:  
  * `forecast_id`: Forecast ID for which metrics are requested  (Pass as list in case using at `entity` level)
  * `single_aggregation_value`: Store ID/City Name for which metrics are requested, defaults to Pan India.
  * `single_aggregation_grain`: Aggregation grain (city, store, pan_india)
  * `start_time`: Starting time for aggregating requested metrics  
    * Format: `YYYY-MM-DD HH:mm:ss`  
  * `time_grain`: Time grain (daily, weekly, hourly)
* Response:
  ```json
  {
    "status": "success",
    "data": {
        "idx_cols": {
            "time": {
                "display_name": "Time",
                "metric_type": "str"
            }
        },
        "cols": {
            "orders": {
                "metric_name": "orders",
                "display_name": "Orders placed",
                "metric_type": "count"
            },
            "items_per_order": {
                "metric_name": "items_per_order",
                "display_name": "Avg. number of items per order",
                "metric_type": "count"
            }
        },
        "values": [
            {
              "time": "2025-04-24 00:00:00",
              "default_model": "m1"
              "values":{
                "m1": {
                  "orders": 100,
                  "items_per_order": 100
                },
                "m2": {
                  "orders": 100,
                  "items_per_order": 100
                }   
              }
            },
            {
                "time": "2025-04-24 00:00:00",
                "default_model": "m1"
                "values":{
                  "m1": {
                    "orders": 100,
                    "items_per_order": 100
                  },
                  "m2": {
                    "orders": 100,
                    "items_per_order": 100
                  }   
                }
            }
        ]
    }
  }
  ```

### Create Forecast Inputs

* Endpoint: `POST /api/v1/{tenant}/{entity}/{category_id}/forecasts/{forecast_id}/inputs`
* Parameters:  
  * `forecast_id`: Forecast ID for which inputs are created.
  * `inputs` : List of inputs provided by the user. Format for every input element in the list  - 

    ```json
    {
        "forecast_id": 1,
        "inputs": [
            {
                "entity_id": 123,
                "start_time": "2025-04-24 00:00:00",
                "time_grain": "daily",
                "values": [
                    {
                        "metric_name": "orders",
                        "value": 101
                    }
                ]
            }
        ]
    }
    ```
* Response:
  ```json
  {
    "status": "success",
    "data": [
        {
            "input_id": 1,
            "entity_id": 123,
            "start_time": "2025-04-24 00:00:00",
            "time_grain": "daily",
            "status": "pending",
            "values": [
                {
                    "metric_name": "orders",
                    "value": 101
                }
            ]
        }
    ]
  }
  ```

### Get Forecast Inputs

* Endpoint: `GET /api/v1/{tenant}/{entity}/{category_id}/forecasts/{forecast_id}/inputs`
* Parameters:  
  * `forecast_id`: Forecast ID for which inputs are created.  
  * `multi_aggregation_values`: Store IDs/City Names for which metrics are requested, defaults to Pan India.
    * Format: `multi_aggregation_values[]=Delhi&multi_aggregation_values[]=Gurugram`
  * `multi_aggregation_grain`: Aggregation grain (city, store, pan_india)
  * `start_time`: Starting time for aggregating requested metrics  
    * Format: `YYYY-MM-DD HH:mm:ss`  
  * `time_grain`: Time grain (daily, weekly, hourly)
  * `get_outputs`: Flag to get the output values for the inputs. Defaults to False.
* Response:  
  ```json
  {
    "status": "success",
    "data": [
        {
            "type": "input"
            "input_id": 1,
            "entity_id": 123,
            "start_time": "2025-04-24 00:00:00",
            "time_grain": "daily",
            "status": "pending",
            "values": [
                {
                    "metric_name": "orders",
                    "value": 101
                }
            ]
        },
        {
            "type": "output",
            "model_id": "m1"
            "entity_id": 123,
            "start_time": "2025-04-24 00:00:00",
            "time_grain": "daily",
            "status": "pending",
            "values": [
                {
                    "metric_name": "ppi",
                    "value": 101
                }
            ]
        }
    ]
  }
  ```

### Get Forecast Models

* Endpoint: `GET /api/v1/{tenant}/{entity}/{category_id}/forecasts/{forecast_id}/models`
* Parameters:  
  * `forecast_id`: Forecast ID for which models are requested.  
* Response:
  ```json
  {
    "status": "success",
    "data": [
        {
            "model_id": "model_1",
            "model_name": "Model 1",
            "model_description": "Description for Model 1"
        }
    ]
  }
  ```

### Update Model for entity

* Endpoint: `POST /api/v1/{tenant}/{entity}/{category_id}/forecasts/{forecast_id}/update_entity_model`
* Parameters:  
  * `forecast_id`: Forecast ID for which the model is to be updated.  
  * `entity_value`: Store ID/City ID for which the model is to be updated.
  * `model_id`: Model ID to be updated.
* Request Body:
  ```json
  {
    "entity_value": 123,
    "model_id": "model_1"
  }
  ```
* Response:
  ```json
  {
    "status": "success",
    "data": {
        "forecast_id": 1,
        "entity": "storeops",
        "entity_value": 123,
        "model_id": "model_1"
    }
  }
  ```

### Update Default Model for Forecast

* Endpoint: `POST /api/v1/{tenant}/{entity}/{category_id}/forecasts/{forecast_id}/update_model`
* Parameters:  
  * `forecast_id`: Forecast ID for which the default model is to be updated.  
  * `model_id`: Model ID to be set as default.
* Request Body:
  ```json
  {
    "model_id": "model_1"
  }
  ```
* Response:
  ```json
  {
    "status": "success",
    "data": {
        "forecast_id": 1,
        "model_id": "model_1"
    }
  }
  ```

### Get Forecast Inputs with Time Filter

* Endpoint: `GET /api/v1/{tenant}/{entity}/{category_id}/forecasts/{forecast_id}/inputs/time_filter`
* Parameters:  
  * `forecast_id`: Forecast ID for which inputs are requested.  
  * `start_time`: Starting time for filtering inputs
    * Format: `YYYY-MM-DD HH:mm:ss`  
  * `end_time`: Ending time for filtering inputs
    * Format: `YYYY-MM-DD HH:mm:ss`  
  * `time_grain`: Time grain (daily, weekly, hourly)
* Response:
  ```json
  {
    "status": "success",
    "data": [
        {
            "input_id": 1,
            "entity_id": 123,
            "start_time": "2025-04-24 00:00:00",
            "time_grain": "daily",
            "status": "pending",
            "values": [
                {
                    "metric_name": "orders",
                    "value": 101
                }
            ]
        }
    ]
  }
  ```

### Update Input Status

* Endpoint: `POST /api/v1/{tenant}/{entity}/{category_id}/forecasts/{forecast_id}/inputs/update_status`
* Parameters:  
  * `forecast_id`: Forecast ID for which input status is to be updated.
* Request Body:
  ```json
  [
    {
      "input_id": 1,
      "status": "approved"
    }
  ]
  ```
* Response:
  ```json
  {
    "status": "success",
    "data": [
        {
            "input_id": 1,
            "status": "approved"
        }
    ]
  }
  ```


## Open Points:

  - Pagination  
  - use cookies instead of local storage for auth
