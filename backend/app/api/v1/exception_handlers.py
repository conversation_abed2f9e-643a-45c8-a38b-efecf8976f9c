from fastapi import Request, HTTPException
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
import pymysql
import logging
import asyncio
from pydantic import ValidationError
from typing import Union

logger = logging.getLogger(__name__)


async def db_connection_error_handler(
    _: Request,
    exc: Union[
        pymysql.err.OperationalError,
        pymysql.err.ProgrammingError,
        pymysql.err.InternalError,
    ],
):
    logger.error(f"Database connection error: {str(exc)}")
    return JSONResponse(
        status_code=503,
        content={"status": "error", "error": f"Database error: {str(exc)}"},
    )


async def validation_exception_handler(_: Request, exc: RequestValidationError):
    """Handle validation errors from FastAPI request validation"""
    logger.error(f"Validation error: {str(exc)}")

    error_details = []
    for error in exc.errors():
        error_dict = {
            "loc": error.get("loc", []),
            "msg": error.get("msg", ""),
            "type": error.get("type", ""),
        }
        error_details.append(error_dict)

    return JSONResponse(
        status_code=422,
        content={"status": "error", "error": error_details},
    )


async def http_exception_handler(_: Request, exc: HTTPException):
    logger.error(f"HTTP Exception: {str(exc)}")
    return JSONResponse(
        status_code=exc.status_code,
        content={"status": "error", "error": exc.detail},
    )


async def query_timeout_handler(_: Request, exc: asyncio.TimeoutError):
    logger.error(f"Query timeout: {str(exc)}")
    return JSONResponse(
        status_code=504,
        content={"status": "error", "error": "Query timed out"},
    )


async def generic_error_handler(_: Request, exc: Exception):
    logger.error(f"Unhandled Error: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content={"status": "error", "error": f"Unexpected error: {str(exc)}"},
    )
