from fastapi import APIRouter
from typing import Union
import logging
import backend.app.api.v1.utils as utils
from backend.app.crud.crud_tenants import Tenants
from backend.app.schemas.tenants import Tenant

logger = logging.getLogger(__name__)

router = APIRouter()
tenants = Tenants()

@router.get(
    "/",
    response_model=utils.SuccessResponse,
    description="Get all tenants",
)
async def get_tenants():
    """
    Get all tenants
    Returns:
        dict
    """
    logger.debug("Getting list of tenants")
    result = await tenants.get_tenants()
    return utils.SuccessResponse(data=result)

@router.post(
    "/",
    response_model=Tenant,
    status_code=201,
    description="Create a tenant",
)
async def create_tenant(
    tenant_name: str,
):
    """
    Create a tenant
    Returns:
        dict
    """
    logger.debug("Creating a tenant")
    tenant = await tenants.create_tenant(tenant_name)
    return tenant