from fastapi import APIRouter
from typing import Union
import logging
import backend.app.api.v1.utils as utils
from backend.app.crud.crud_categories import Categories

logger = logging.getLogger(__name__)

router = APIRouter()
categories = Categories()


@router.get(
    "/",
    response_model=utils.SuccessResponse,
    description="Get all categories for an entity",
)
async def get_categories(
    tenant: str,
    entity: str,
):
    """
    Get all categories
    Returns:
        dict
    """
    logger.debug("Getting list of categories")
    result = await categories.get_categories(tenant, entity)
    return utils.SuccessResponse(data=result)


@router.post("/")
async def create_category(
    tenant: str,
    entity: str,
    category_name: str,
):
    """
    Create a category
    Returns:
        dict
    """
    logger.debug("Creating a category")
    result = await categories.create_category(tenant, entity, category_name)
    return utils.SuccessResponse(data=result)
