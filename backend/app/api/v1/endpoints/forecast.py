from fastapi import APIRouter, Query
from pydantic import BaseModel
from typing import List, Union, Optional, Dict
import logging
from backend.app.crud.crud_forecasts import CrudForecasts
from backend.app.schemas.inputs import (
    CreateForecastInputRequest,
    ForecastInputResponse,
    UpdateInputStatus,
)
from backend.app.schemas.models import Model
from backend.app.schemas.utils import AggregationGranularity, TimeGranularity
from backend.app.schemas.forecasts import (
    Forecast,
    ForecastDataRequest,
    DetailedForecastDataRequest,
    ForecastData,
    SingleAggregation,
    MultiAggregation,
)
import backend.app.api.v1.utils as utils

logger = logging.getLogger(__name__)

router = APIRouter()

crud_forecasts = CrudForecasts()

# TODO: move these classes to schemas folder for better maintainability


class GetForecastsResponse(BaseModel):
    tenant: str
    entity: str
    forecasts: Dict[str, Forecast]


class ForecastDataResponse(BaseModel):
    status: str
    data: ForecastData


class GetModelsResponse(BaseModel):
    status: str
    data: List[Model]


class UpdateForecastModelRequest(BaseModel):
    forecast_id: Optional[int] = None
    model_id: str


class UpdateForecastModelResponse(BaseModel):
    status: str
    data: UpdateForecastModelRequest


class UpdateModelEntityRequest(BaseModel):
    forecast_id: Optional[int] = None
    entity: Optional[str] = None
    entity_value: int
    model_id: str


class UpdateModelEntityResponse(BaseModel):
    status: str
    data: UpdateModelEntityRequest


@router.get(
    "/",
    response_model=utils.SuccessResponse,
    description="Get all forecasts for category_id or entity",
)
async def get_forecasts(
    tenant: str,
    entity: str,
    category_id: Optional[int],
):
    logger.debug(f"Getting forecasts for category_id: {category_id}")
    forecasts = await crud_forecasts.get_forecasts(tenant, entity, category_id)
    return utils.SuccessResponse(data=forecasts)


@router.post(
    "",
    response_model=Forecast,
    description="Register a new forecast",
)
async def register_forecast(tenant: str, entity: str, category_id: int, data: Forecast):
    logger.debug(f"Registering a new forecast for category_id: {category_id}")
    data.tenant = tenant
    data.entity = entity
    data.category = category_id
    forecast = await crud_forecasts.register_forecast(data)
    return forecast


@router.get(
    "/{forecast_id}",
    response_model=ForecastDataResponse,
    description="Get forecast data over provided time range for passed stores/cities",
)
async def get_entity_forecast(
    tenant: str,
    entity: str,
    category_id: int,
    forecast_id: int,
    start_time: str,
    time_grain: TimeGranularity,
    multi_aggregation_values: List[Union[str, int]] = Query(
        default=[], alias="multi_aggregation_values[]"
    ),
    multi_aggregation_grain: AggregationGranularity = Query(
        default=AggregationGranularity.PAN_INDIA
    ),
):
    data = ForecastDataRequest(
        tenant=tenant,
        entity=entity,
        category_id=category_id,
        forecast_ids=[forecast_id],
        multi_aggregation=MultiAggregation(
            values=multi_aggregation_values, grain=multi_aggregation_grain
        ),
        start_time=start_time,
        time_grain=time_grain,
    )
    logger.debug(
        f"Getting store forecast for entities: {data.multi_aggregation.values} at entity_grain: {data.multi_aggregation.grain}, start_time: {data.start_time}, time_grain: {data.time_grain}, forecast_id: {data.forecast_ids}"
    )

    if data.time_grain == TimeGranularity.WEEKLY:
        utils.validate_day_of_week(data.start_time)

    data = await crud_forecasts.get_entity_forecast(data)

    return ForecastDataResponse(
        status="success", data=data
    )


async def get_combined_forecast(
    tenant: str,
    entity: str,
    start_time: str,
    time_grain: TimeGranularity,
    forecast_ids: List[int] = Query(alias="forecast_ids[]"),
    multi_aggregation_values: List[Union[str, int]] = Query(
        default=[], alias="multi_aggregation_values[]"
    ),
    multi_aggregation_grain: AggregationGranularity = Query(
        default=AggregationGranularity.PAN_INDIA
    ),
):
    data = ForecastDataRequest(
        tenant=tenant,
        entity=entity,
        category_id=None,
        forecast_ids=forecast_ids,
        multi_aggregation=MultiAggregation(
            values=multi_aggregation_values, grain=multi_aggregation_grain
        ),
        start_time=start_time,
        time_grain=time_grain,
    )
    logger.debug(
        f"Getting store forecast for entities: {data.multi_aggregation.values} at entity_grain: {data.multi_aggregation.grain}, start_time: {data.start_time}, time_grain: {data.time_grain}, forecast_id: {data.forecast_ids}"
    )

    if data.time_grain == TimeGranularity.WEEKLY:
        utils.validate_day_of_week(data.start_time)

    return ForecastDataResponse(
        status="success", data=await crud_forecasts.get_entity_forecast(data)
    )


@router.get(
    "/{forecast_id}/detailed",
    response_model=ForecastDataResponse,
    description="Get detailed forecast data over provided time range for passed store/city",
)
async def get_entity_forecast_detailed(
    tenant: str,
    entity: str,
    category_id: int,
    forecast_id: int,
    start_time: str,
    time_grain: TimeGranularity,
    single_aggregation_value: Union[str, int, None] = Query(default=None),
    single_aggregation_grain: AggregationGranularity = Query(
        default=AggregationGranularity.PAN_INDIA
    ),
):
    data = DetailedForecastDataRequest(
        tenant=tenant,
        entity=entity,
        category_id=category_id,
        forecast_ids=[forecast_id],
        start_time=start_time,
        time_grain=time_grain,
        single_aggregation=SingleAggregation(
            value=single_aggregation_value, grain=single_aggregation_grain
        ),
    )
    logger.debug(
        f"Getting store forecast for entities: {data.single_aggregation.value} at entity_grain: {data.single_aggregation.grain}, start_time: {data.start_time}, time_grain: {data.time_grain}, forecast_id: {data.forecast_ids}"
    )

    return ForecastDataResponse(
        status="success",
        data=await crud_forecasts.get_detailed_entity_forecast(data),
    )


async def get_combined_forecast_detailed(
    tenant: str,
    entity: str,
    category_id: int,
    forecast_ids: List[int],
    start_time: str,
    time_grain: TimeGranularity,
    single_aggregation_value: Union[str, int, None] = Query(default=None),
    single_aggregation_grain: AggregationGranularity = Query(
        default=AggregationGranularity.PAN_INDIA
    ),
):
    data = DetailedForecastDataRequest(
        tenant=tenant,
        entity=entity,
        category_id=category_id,
        forecast_ids=forecast_ids,
        start_time=start_time,
        time_grain=time_grain,
        single_aggregation=SingleAggregation(
            value=single_aggregation_value, grain=single_aggregation_grain
        ),
    )
    logger.debug(
        f"Getting store forecast for entities: {data.single_aggregation.value} at entity_grain: {data.single_aggregation.grain}, start_time: {data.start_time}, time_grain: {data.time_grain}, forecast_id: {data.forecast_ids}"
    )

    return ForecastDataResponse(
        status="success",
        data=await crud_forecasts.get_detailed_entity_forecast(data),
    )


@router.get(
    "/{forecast_id}/inputs",
    response_model=ForecastInputResponse,
    description="Get unprocessed forecast inputs for forecast_id over provided time range",
)
async def get_entity_forecast_inputs(
    tenant: str,
    entity: str,
    category_id: int,
    forecast_id: int,
    start_time: str,
    time_grain: TimeGranularity,
    multi_aggregation_values: List[Union[str, int]] = Query(
        default=[], alias="multi_aggregation_values[]"
    ),
    multi_aggregation_grain: AggregationGranularity = Query(
        default=AggregationGranularity.PAN_INDIA
    ),
    get_outputs: bool = Query(default=False),
):
    data = ForecastDataRequest(
        tenant=tenant,
        entity=entity,
        category_id=category_id,
        forecast_ids=[forecast_id],
        multi_aggregation=MultiAggregation(
            values=multi_aggregation_values, grain=multi_aggregation_grain
        ),
        start_time=start_time,
        time_grain=time_grain,
    )
    logger.debug(
        f"Getting store forecast inputs for forecast_id: {data.forecast_ids}, entities: {data.multi_aggregation.values} at entity_grain: {data.multi_aggregation.grain}, start_time: {data.start_time}, time_grain: {data.time_grain}"
    )

    if data.time_grain == TimeGranularity.WEEKLY:
        utils.validate_day_of_week(data.start_time)

    inputs = await crud_forecasts.get_entity_forecast_input(data, get_outputs)
    if len(inputs) == 0:
        return ForecastInputResponse(
            status="success", message="No inputs found", data=[]
        )

    return ForecastInputResponse(status="success", data=inputs)


async def get_combined_forecast_inputs(
    tenant: str,
    entity: str,
    category_id: int,
    forecast_ids: List[int],
    start_time: str,
    time_grain: TimeGranularity,
    multi_aggregation_values: List[Union[str, int]] = Query(
        default=[], alias="multi_aggregation_values[]"
    ),
    multi_aggregation_grain: AggregationGranularity = Query(
        default=AggregationGranularity.PAN_INDIA
    ),
    get_outputs: bool = Query(default=False),
):
    data = ForecastDataRequest(
        tenant=tenant,
        entity=entity,
        category_id=category_id,
        forecast_ids=forecast_ids,
        multi_aggregation=MultiAggregation(
            values=multi_aggregation_values, grain=multi_aggregation_grain
        ),
        start_time=start_time,
        time_grain=time_grain,
    )
    logger.debug(
        f"Getting store forecast inputs for forecast_ids: {forecast_ids}, entities: {data.multi_aggregation.values} at entity_grain: {data.multi_aggregation.grain}, start_time: {data.start_time}, time_grain: {data.time_grain}"
    )

    if data.time_grain == TimeGranularity.WEEKLY:
        utils.validate_day_of_week(data.start_time)

    inputs = await crud_forecasts.get_entity_forecast_input(data, get_outputs)
    if len(inputs) == 0:
        return ForecastInputResponse(
            status="success", message="No inputs found", data=[]
        )

    return ForecastInputResponse(status="success", data=inputs)


@router.post(
    "/{forecast_id}/inputs",
    response_model=ForecastInputResponse,
    description="Create forecast inputs for forecast_id over provided time range",
)
async def create_store_forecast_inputs(
    tenant: str, entity: str, category_id: int, data: CreateForecastInputRequest
):
    logger.debug(f"Creating store forecast inputs for forecast_id: {data.forecast_id}")

    updated_inputs = await crud_forecasts.create_entity_forecast_input(data)
    if len(updated_inputs) != len(data.inputs):
        return ForecastInputResponse(
            status="success",
            message="Some inputs were not created",
            data=updated_inputs,
        )

    return ForecastInputResponse(status="success", data=updated_inputs)


@router.post(
    "/{forecast_id}/inputs/update_status",
    response_model=utils.SuccessResponse,
    description="Update input status for forecast_id",
)
async def update_input_status(
    tenant: str,
    entity: str,
    category_id: int,
    forecast_id: int,
    data: List[UpdateInputStatus],
):
    logger.debug(f"Updating input status for forecast_id: {forecast_id}")
    updated_inputs = crud_forecasts.update_input_status(data)
    return utils.SuccessResponse(data=updated_inputs)


@router.get(
    "/{forecast_id}/models",
    response_model=GetModelsResponse,
    description="Get all forecast models for forecast_id",
)
async def get_store_forecast_models(
    tenant: str,
    entity: str,
    category_id: int,
    forecast_id: int,
):
    logger.debug(f"Getting store forecast models for forecast_id: {forecast_id}")
    models = crud_forecasts.get_entity_forecast_models(forecast_id)
    return GetModelsResponse(status="success", data=models)


@router.post(
    "/{forecast_id}/update_model",
    response_model=UpdateForecastModelResponse,
    description="Update model for forecast_id",
)
async def update_forecast_model(
    tenant: str,
    entity: str,
    category_id: int,
    forecast_id: int,
    data: UpdateForecastModelRequest,
):
    logger.debug(f"Updating forecast model for forecast_id: {data.forecast_id}")
    data.forecast_id = forecast_id
    crud_forecasts.update_forecast_model(data.forecast_id, data.model_id)
    return UpdateForecastModelResponse(status="success", data=data)


@router.post(
    "/{forecast_id}/update_entity_model",
    response_model=UpdateModelEntityResponse,
    description="Update model for entity",
)
async def update_forecast_entity_model(
    tenant: str,
    entity: str,
    category_id: int,
    forecast_id: int,
    data: UpdateModelEntityRequest,
):
    logger.debug(f"Updating forecast model for forecast_id: {data.forecast_id}")
    data.entity = entity
    data.forecast_id = forecast_id
    crud_forecasts.update_forecast_entity_model(
        data.entity, data.forecast_id, data.entity_value, data.model_id
    )
    return UpdateModelEntityResponse(status="success", data=data)
