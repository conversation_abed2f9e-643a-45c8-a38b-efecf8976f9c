from fastapi import APIRouter, Query
from typing import Optional, List
import logging
import json
from backend.app.schemas.metrics import MetricDefinition, MetricType
from backend.app.crud.crud_metrics import (
    CrudMetrics,
    list_metrics as list_category_metrics,
    register_metrics as register_category_metrics,
)
import backend.app.api.v1.utils as utils

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("")
async def list_metrics(
    tenant: str,
    entity: str,
    category_id: int,
):
    """
    List all order metrics
    Params:
        None
    Returns:
        dict
    """

    logger.debug("Listing all order metrics")
    result = await list_category_metrics(tenant, entity, category_id)
    return utils.SuccessResponse(data=result)


@router.post("")
async def register_metrics(
    tenant: str,
    entity: str,
    category_id: int,
    metric_name: str,
    display_name: str,
    metric_type: MetricType,
    select_query: Optional[str] = None,
    aggregate_query: Optional[str] = None,
    grain: Optional[str] = None,
    dependency_metrics: Optional[List[int]] = Query(
        default=[], alias="dependency_metrics[]"
    ),
):
    """
    Register a new metric
    Params:
        None
    Returns:
        dict
    """
    logger.debug("Registering a new metric")

    new_metric = MetricDefinition(
        metric_name=metric_name,
        display_name=display_name,
        metric_type=metric_type,
        select_query=select_query,
        aggregate_query=aggregate_query,
        grain=grain,
        dependency_metrics=dependency_metrics,
    )
    result = await register_category_metrics(tenant, entity, category_id, new_metric)
    return utils.SuccessResponse(data=result)


@router.get("/viz")
async def get_category_metrics(
    tenant: str,
    entity: str,
    category_id: int,
    start_time: str,
    end_time: str,
    store_id: Optional[int] = Query(default=None),
    city: Optional[str] = Query(default=None),
    detailed: Optional[bool] = False,
    comparison: Optional[List[str]] = Query(default=None, alias="comparison[]"),
    metric_list: Optional[List[str]] = Query(default=None, alias="metric_list[]"),
    event_date: Optional[str] = Query(None),
):
    """
    Get metrics for historical playground
    Params:
        store_id: int
        city: str
        start_time: str
        end_time: str
        detailed: bool
        comparison: List[str]
        metric_list: List[str]
        event_date: str
    Returns:
        dict
    """
    if comparison is None:
        comparison = []
    if metric_list is None:
        metric_list = []
    logger.debug(
        f"Getting store metrics for store_id: {store_id}, city: {city}, start_time: {start_time}, end_time: {end_time}, detailed: {detailed}, comparison: {comparison}, metric_list: {metric_list}, event_date: {event_date}"
    )
    if event_date:
        event_date = json.loads(event_date)
    aggregation_granularity, aggregation_id = utils.parse_aggregation_granularity(
        city, store_id
    )
    comparison_periods: List[utils.ComparisonPeriod] = utils.parse_comparison_options(
        start_time, end_time, comparison, event_date
    )

    crud_metrics = CrudMetrics(tenant, entity, [category_id])

    data = {}
    if detailed:
        data = await crud_metrics.get_detailed_metrics(
            aggregation_granularity, aggregation_id, comparison_periods, metric_list
        )
    else:
        data = await crud_metrics.get_aggregated_metrics(
            aggregation_granularity, aggregation_id, comparison_periods, metric_list
        )

    return utils.SuccessResponse(data=data)


async def get_combined_category_metrics(
    tenant: str,
    entity: str,
    start_time: str,
    end_time: str,
    store_id: Optional[int] = None,
    city: Optional[str] = None,
    detailed: Optional[bool] = False,
    comparison: Optional[List[str]] = None,
    category_ids: Optional[List[int]] = None,
    event_date: Optional[str] = None,
):
    """
    Get metrics for historical playground
    Params:
        store_id: int
        city: str
        start_time: str
        end_time: str
        detailed: bool
        comparison: List[str]
        category_ids: List[int]
        event_date: str
    Returns:
        dict
    """
    if comparison is None:
        comparison = []
    if category_ids is None:
        category_ids = []
    if event_date:
        event_date = json.loads(event_date)
    aggregation_granularity, aggregation_id = utils.parse_aggregation_granularity(
        city, store_id
    )
    comparison_periods: List[utils.ComparisonPeriod] = utils.parse_comparison_options(
        start_time, end_time, comparison, event_date
    )

    crud_metrics = CrudMetrics(tenant, entity, category_ids)

    data = {}
    if detailed:
        data = await crud_metrics.get_detailed_metrics(
            aggregation_granularity, aggregation_id, comparison_periods
        )
    else:
        data = await crud_metrics.get_aggregated_metrics(
            aggregation_granularity, aggregation_id, comparison_periods
        )

    return utils.SuccessResponse(data=data)
