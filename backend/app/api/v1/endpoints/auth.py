from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPEx<PERSON>, Request
from fastapi.responses import JSONResponse
from fastapi.security import OAuth2PasswordBearer
import logging
from backend.app.core.config import settings
from sqlalchemy.orm import Session
from backend.app import crud
from backend.app.schemas.user import User<PERSON><PERSON>
from backend.app.db import session as db_session
from backend.app.api.v1.auth_utils import (
    fetch_oauth_session,
    generate_authorization_url,
    fetch_user_info,
    is_allowed_domain,
    create_jwt_pair,
    set_cookie,
)

logger = logging.getLogger(__name__)

router = APIRouter()

# OAuth2 scheme
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")


@router.get("/google/login")
async def google_login(request: Request):
    # origin = request.headers.get("origin")
    # redirectURL="http://localhost:3000/auth/google/callback"

    # if origin != "":
    #     redirectURL = origin + "/auth/google/callback"
    # ? might need to change the origin
    client = fetch_oauth_session()
    authorization_url = generate_authorization_url(client)
    return {"redirect_url": authorization_url}


@router.get("/google/callback")
async def google_callback(request: Request, db: Session = Depends(db_session.get_db)):
    client = fetch_oauth_session()
    user_info = fetch_user_info(client, request.query_params.get("code"))
    if not is_allowed_domain(user_info):
        raise HTTPException(status_code=403, detail="Not allowed domain")

    user = crud.user.get_by_email(db, email=user_info["email"])

    if not user:
        # create user, by default is_allowed will be false
        user = crud.user.create(
            db,
            obj_in=UserCreate(
                email=user_info["email"],
                full_name=user_info["name"],
            ),
        )

    if not user.is_allowed:
        return {
            "status": "failed",
            "message": "Not allowed",
        }

    jwt_access_token, jwt_refresh_token, error = create_jwt_pair(user)

    response = JSONResponse(
        content={
            "status": "success",
            "message": "Google OAuth callback processed",
            "result": {
                "user": {
                    "id": str(user.id),
                    "email": user_info["email"],
                    "name": user_info["name"],
                },
            },
        }
    )

    # Set cookies

    set_cookie(
        response, jwt_access_token, "access", settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
    )
    set_cookie(
        response,
        jwt_refresh_token,
        "refresh",
        settings.REFRESH_TOKEN_EXPIRE_DAYS * 24 * 60 * 60,
    )

    return response


# * don't need this atm
@router.post("/refresh")
async def refresh_token(token: str = Depends(oauth2_scheme)):
    # Implement token refresh logic
    return {"access_token": "new_token", "token_type": "bearer"}


@router.post("/logout")
async def logout():
    # Authorize.unset_jwt_cookies()
    response = JSONResponse(
        content={"status": "success", "message": "Logged out successfully"}
    )
    response.delete_cookie(key="access_token")
    response.delete_cookie(key="refresh_token")
    return response
