from fastapi import APIRouter, Query
from typing import Optional
import logging
import backend.app.api.v1.utils as utils
from backend.app.crud.crud_health_metrics import HealthMetrics

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/")
async def get_store_health(
    start_time: str,
    end_time: str,
    store_id: Optional[int] = Query(default=None),
    city: Optional[str] = Query(default=None),
):
    """
    Get store health for a store
    Params:
        store_id: int
        city: str
        start_time: str
        end_time: str
    Returns:
        dict
    """
    logger.debug(
        f"Getting store health for store_id: {store_id}, city: {city}, start_time: {start_time}, end_time: {end_time}"
    )
    aggregation_granularity, aggregation_id = utils.parse_aggregation_granularity(
        city, store_id
    )
    store_metrics = HealthMetrics()
    store_health = await store_metrics.get_health(
        aggregation_granularity, aggregation_id, start_time, end_time
    )
    return utils.SuccessResponse(data=store_health.model_dump())
