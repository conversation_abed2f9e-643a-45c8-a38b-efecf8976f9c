from fastapi import APIRouter
from typing import List
import logging
import backend.app.api.v1.utils as utils
from backend.app.crud.crud_entities import CrudEntities
from backend.app.schemas.events import Event
from backend.app.schemas.stores import Store
from backend.app.schemas.entities import Entity

logger = logging.getLogger(__name__)

router = APIRouter()

crud_entities = CrudEntities()

@router.get("")
async def list_entities(
    tenant: str,
):
    """
    Get all entities
    Returns:
        dict
    """
    logger.debug("Getting list of entities")
    data = await crud_entities.get_entities(tenant)
    return utils.SuccessResponse(data=data)

@router.post("",
             response_model=Entity,
             status_code=201)
async def create_entity(
    tenant: str,
    entity: str,
):
    """
    Create a new entity
    Returns:
        dict
    """
    logger.debug(f"Creating entity {entity} for tenant {tenant}")

    entity_obj: Entity = await crud_entities.create_entities(tenant, entity)

    logger.info(f"Created entity {entity_obj.name} with ID {entity_obj.id}")
    return entity_obj

@router.get("/{entity}")
async def get_entities(
    tenant: str,
    entity: str,
):
    """
    Get all events
    Returns:
        dict
    """
    logger.debug("Getting list of events")
    if tenant != "blinkit":
        raise ValueError("Only blinkit is supported for now")
    if entity == "events":
        events: List[Event] = await crud_entities.get_dim_events()
        logger.info(f"Found {len(events)} events")
        return utils.SuccessResponse(data=events)
    elif entity == "storeops":
        stores: List[Store] = await crud_entities.get_dim_outlets()
        logger.info(f"Found {len(stores)} stores")
        return utils.SuccessResponse(data=stores)
    else:
        raise ValueError(f"Entity {entity} not supported")
