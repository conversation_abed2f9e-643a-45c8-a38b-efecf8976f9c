from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, status
from datetime import datetime, timedelta
import jwt
import json
from requests_oauthlib import OAuth2Session
from backend.app.core.config import settings
import logging
from fastapi.responses import JSONResponse

logger = logging.getLogger(__name__)


def fetch_oauth_session():
    client_id = settings.GOOGLE_CLIENT_ID
    redirect_uri = settings.GOOGLE_REDIRECT_URI
    scope = [
        "https://www.googleapis.com/auth/userinfo.email",
        "openid",
        "https://www.googleapis.com/auth/userinfo.profile",
    ]
    return OAuth2Session(client_id, scope=scope, redirect_uri=redirect_uri)


def generate_authorization_url(oauth_provider: OAuth2Session):
    authorization_url, state = oauth_provider.authorization_url(
        "https://accounts.google.com/o/oauth2/v2/auth",
        access_type="offline",
        prompt="select_account",
    )
    return authorization_url


def fetch_user_info(oauth_provider: OAuth2Session, authorization_token: str):
    try:
        client_secret = settings.GOOGLE_CLIENT_SECRET
        # We need to fetch the token to complete the OAuth flow
        # even if we don't use the token variable later
        _ = oauth_provider.fetch_token(
            "https://www.googleapis.com/oauth2/v4/token",
            client_secret=client_secret,
            code=authorization_token,
        )

        r = oauth_provider.get("https://www.googleapis.com/oauth2/v3/userinfo")

        return json.loads(r.content)
    except Exception as e:
        logger.error(f"Error fetching user info: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to authenticate with Google: {str(e)}",
        )


def is_allowed_domain(user: dict):
    hd = user.get("hd")
    if hd:
        return hd in ["grofers.com", "zomato.com", "blinkit.com"]
    return False


def create_jwt_pair(user: dict):
    """
    Return a JWT Pair of AccessToken & RefreshToken

    Args:
        user: User object from database

    Returns:
        tuple: (access_token, refresh_token, error)
    """
    try:
        # Access Token Generation
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token_expiry = datetime.utcnow() + access_token_expires

        access_token_payload = {
            "sub": user.email,
            "authorized": True,
            "email": user.email,
            "is_allowed": user.is_allowed,
            "iat": datetime.timestamp(datetime.utcnow()),
            "exp": datetime.timestamp(access_token_expiry),
        }

        access_token = jwt.encode(
            access_token_payload, settings.JWT_SECRET_KEY, algorithm="HS256"
        )

        # Decode bytes to string if needed: made it into b'<token>'
        if isinstance(access_token, bytes):
            access_token = access_token.decode("utf-8")

        # Refresh Token Generation
        refresh_token_expires = timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
        refresh_token_expiry = datetime.utcnow() + refresh_token_expires

        refresh_token_payload = {
            "sub": user.email,
            "authorized": True,
            "email": user.email,
            "is_allowed": user.is_allowed,
            "iat": datetime.timestamp(datetime.utcnow()),
            "exp": datetime.timestamp(refresh_token_expiry),
        }

        refresh_token = jwt.encode(
            refresh_token_payload, settings.JWT_SECRET_KEY, algorithm="HS256"
        )
        # Decode bytes to string if needed: made it into b'<token>'
        if isinstance(refresh_token, bytes):
            refresh_token = refresh_token.decode("utf-8")

        return access_token, refresh_token, None
    except Exception as e:
        logger.error(f"Unable to generate JWT tokens: {str(e)}")
        return None, None, e


def set_cookie(
    response: JSONResponse, token: str, token_type: str, max_age: int = None
):
    response.set_cookie(
        key=f"{token_type}_token",
        value=token,
        httponly=True,
        max_age=max_age,
        secure=settings.DEBUG_MODE,
        samesite="none",  # Required for cross-origin cookies
    )
