from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, func
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.orm import relationship

from backend.app.db.base_class import Base


class Entities(Base):
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String, nullable=True)
    tenant_id = Column(Integer, ForeignKey("tenants.id"), nullable=True)
    created_at = Column(DateTime, nullable=True, server_default=func.now())
    updated_at = Column(
        DateTime, nullable=True, server_default=func.now(), onupdate=func.now()
    )

    tenants = relationship("Tenants", back_populates="entities")

    categories = relationship(
        "Categories", back_populates="entities", cascade="all, delete-orphan"
    )
    forecast_model_entities = relationship(
        "ForecastModelEntities", back_populates="entities", cascade="all, delete-orphan"
    )

    @declared_attr
    def __tablename__(cls) -> str:
        return "entities"
