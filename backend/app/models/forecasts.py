from sqlalchemy import <PERSON>umn, Inte<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, DateTime, ForeignKey, func
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.orm import relationship

from backend.app.db.base_class import Base


class Forecasts(Base):
    id = Column(Integer, primary_key=True)
    category_id = Column(Integer, ForeignKey("categories.id"), nullable=True)
    table_name = Column(String, nullable=True)
    display_name = Column(String, nullable=True)
    is_active = Column(Boolean, default=True)
    # TODO: Add created_by and updated_by columns once we have auth in place
    created_at = Column(DateTime, nullable=True, server_default=func.now())
    updated_at = Column(
        DateTime, nullable=True, server_default=func.now(), onupdate=func.now()
    )

    categories = relationship("Categories", back_populates="forecasts")
    forecast_metrics = relationship(
        "ForecastMetrics",
        back_populates="forecasts",
        cascade="all, delete-orphan",
    )
    inputs = relationship(
        "Inputs", back_populates="forecasts", cascade="all, delete-orphan"
    )
    forecast_models = relationship(
        "ForecastModels",
        back_populates="forecasts",
        cascade="all, delete-orphan",
    )

    @declared_attr
    def __tablename__(cls) -> str:
        return "forecasts"
