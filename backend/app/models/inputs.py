from sqlalchemy import (
    Column,
    Integer,
    String,
    Float,
    DateTime,
    Boolean,
    Foreign<PERSON>ey,
    func,
    Enum,
)
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum

from backend.app.db.base_class import Base


class InputStatus(PyEnum):
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"


class Inputs(Base):
    id = Column(Integer, primary_key=True, autoincrement=True)
    forecast_id = Column(Integer, ForeignKey("forecasts.id"), nullable=False)
    time_grain = Column(String(255), nullable=False)
    entity_id = Column(Integer, nullable=False)
    start_time = Column(DateTime, nullable=False)
    # TODO: Add created_by and updated_by columns once we have auth in place
    created_at = Column(DateTime, nullable=True, server_default=func.now())
    updated_at = Column(
        DateTime, nullable=True, server_default=func.now(), onupdate=func.now()
    )
    is_approved = Column(Enum(InputStatus), nullable=False, default=InputStatus.PENDING)

    forecasts = relationship("Forecasts", back_populates="inputs")
    input_values = relationship(
        "InputValues",
        back_populates="inputs",
        cascade="all, delete-orphan",
    )

    @declared_attr
    def __tablename__(cls) -> str:
        return "inputs"
