from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, DateTime, func, String
from sqlalchemy.orm import relationship
from sqlalchemy.schema import UniqueConstraint
from sqlalchemy.ext.declarative import declared_attr

from backend.app.db.base_class import Base


class ForecastModels(Base):
    id = Column(Integer, primary_key=True, autoincrement=True)
    forecast_id = Column(Integer, ForeignKey("forecasts.id"), nullable=False)
    model_id = Column(String, ForeignKey("models.id"), nullable=False)
    created_at = Column(DateTime, nullable=True, server_default=func.now())
    updated_at = Column(
        DateTime, nullable=True, server_default=func.now(), onupdate=func.now()
    )

    forecasts = relationship("Forecasts", back_populates="forecast_models")
    models = relationship("Models", back_populates="forecast_models")
    forecast_model_entities = relationship(
        "ForecastModelEntities",
        back_populates="forecast_models",
        cascade="all, delete-orphan",
    )

    __table_args__ = (
        UniqueConstraint("forecast_id", "model_id", name="uq_forecast_model"),
    )

    @declared_attr
    def __tablename__(cls) -> str:
        return "forecast_models"
