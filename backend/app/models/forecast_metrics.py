from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>teger, ForeignKey, DateTime, func, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.schema import UniqueConstraint

from backend.app.db.base_class import Base


class ForecastMetrics(Base):
    id = Column(Integer, primary_key=True, autoincrement=True)
    forecast_id = Column(Integer, ForeignKey("forecasts.id"), nullable=False)
    metric_id = Column(Integer, ForeignKey("metrics.id"), nullable=False)
    editable = Column(Boolean, nullable=False, default=True)
    created_at = Column(DateTime, nullable=True, server_default=func.now())
    updated_at = Column(
        DateTime, nullable=True, server_default=func.now(), onupdate=func.now()
    )

    forecasts = relationship("Forecasts", back_populates="forecast_metrics")
    metrics = relationship("Metrics", back_populates="forecast_metrics")

    __table_args__ = (
        UniqueConstraint("forecast_id", "metric_id", name="uq_forecast_metric"),
    )

    @declared_attr
    def __tablename__(cls) -> str:
        return "forecast_metrics"
