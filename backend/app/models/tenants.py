from sqlalchemy import Column, Integer, String, DateTime, func
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.orm import relationship

from backend.app.db.base_class import Base


class Tenants(Base):
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String, nullable=True)
    created_at = Column(DateTime, nullable=True, server_default=func.now())
    updated_at = Column(
        DateTime, nullable=True, server_default=func.now(), onupdate=func.now()
    )

    entities = relationship(
        "Entities", back_populates="tenants", cascade="all, delete-orphan"
    )

    @declared_attr
    def __tablename__(cls) -> str:
        return "tenants"
