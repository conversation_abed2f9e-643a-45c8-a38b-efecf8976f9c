from sqlalchemy import <PERSON><PERSON>n, Integer, Foreign<PERSON>ey, Float
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.orm import relationship

from backend.app.db.base_class import Base


class InputValues(Base):
    input_id = Column(
        Integer, ForeignKey("inputs.id"), nullable=False, primary_key=True
    )
    metric_id = Column(
        Integer, ForeignKey("metrics.id"), nullable=False, primary_key=True
    )
    value = Column(Float, nullable=False)

    inputs = relationship("Inputs", back_populates="input_values")
    metrics = relationship("Metrics", back_populates="input_values")

    @declared_attr
    def __tablename__(cls) -> str:
        return "input_values"
