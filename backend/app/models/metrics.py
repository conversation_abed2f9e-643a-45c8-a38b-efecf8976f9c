from sqlalchemy import (
    Column,
    String,
    Integer,
    DateTime,
    ForeignKey,
    func,
    Enum,
    Table,
)
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum

from backend.app.db.base_class import Base

metric_dependencies = Table(
    "metric_dependencies",
    Base.metadata,
    Column("dependent_metric_id", Integer, ForeignKey("metrics.id"), primary_key=True),
    Column("dependency_metric_id", Integer, ForeignKey("metrics.id"), primary_key=True),
)


class MetricType(PyEnum):
    COUNT = "count"
    AVERAGE = "average"
    PERCENTAGE = "percentage"
    SECONDS = "seconds"
    MINUTES = "minutes"
    HOURS = "hours"
    RUPEES = "rupees"
    DAYS = "days"


class Metrics(Base):
    id = Column(Integer, primary_key=True)
    category_id = Column(Integer, ForeignKey("categories.id"))
    select_query = Column(String)
    aggregate_query = Column(String)
    name = Column(String, nullable=True)
    display_name = Column(String, nullable=True)
    metric_type = Column(Enum(MetricType))
    # TODO: Add created_by and updated_by columns once we have auth in place
    created_at = Column(DateTime, nullable=True, server_default=func.now())
    updated_at = Column(
        DateTime, nullable=True, server_default=func.now(), onupdate=func.now()
    )
    grain = Column(String)

    dependencies = relationship(
        "Metrics",
        secondary=metric_dependencies,
        primaryjoin=(id == metric_dependencies.c.dependent_metric_id),
        secondaryjoin=(id == metric_dependencies.c.dependency_metric_id),
        backref="dependents",
    )

    categories = relationship("Categories", back_populates="metrics")
    forecast_metrics = relationship(
        "ForecastMetrics",
        back_populates="metrics",
        cascade="all, delete-orphan",
    )
    input_values = relationship(
        "InputValues",
        back_populates="metrics",
        cascade="all, delete-orphan",
    )

    @declared_attr
    def __tablename__(cls) -> str:
        return "metrics"
