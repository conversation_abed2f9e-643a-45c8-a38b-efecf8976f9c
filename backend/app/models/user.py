from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON>n, Integer, String, TIMESTAMP
from sqlalchemy.sql import func
from sqlalchemy.sql import false
from sqlalchemy.orm import relationship

from backend.app.db.base_class import Base


class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    full_name = Column(String, index=True)
    email = Column(String, unique=True, index=True, nullable=False)
    is_allowed = Column(Boolean(), server_default=false())
    created_at = Column(TIMESTAMP, nullable=False, server_default=func.now())
