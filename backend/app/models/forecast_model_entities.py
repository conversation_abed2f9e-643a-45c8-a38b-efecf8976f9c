from sqlalchemy import <PERSON>umn, Integer, Foreign<PERSON>ey, DateTime, func
from sqlalchemy.orm import relationship
from sqlalchemy.schema import UniqueConstraint
from sqlalchemy.ext.declarative import declared_attr

from backend.app.db.base_class import Base


class ForecastModelEntities(Base):
    id = Column(Integer, primary_key=True, autoincrement=True)
    forecast_models_id = Column(
        Integer, ForeignKey("forecast_models.id"), nullable=False
    )
    entity_id = Column(Integer, ForeignKey("entities.id"), nullable=False)
    entity_value = Column(Integer, nullable=False)
    created_at = Column(DateTime, nullable=True, server_default=func.now())
    updated_at = Column(
        DateTime, nullable=True, server_default=func.now(), onupdate=func.now()
    )

    forecast_models = relationship(
        "ForecastModels", back_populates="forecast_model_entities"
    )
    entities = relationship("Entities", back_populates="forecast_model_entities")

    __table_args__ = (
        UniqueConstraint(
            "forecast_models_id",
            "entity_id",
            "entity_value",
            name="uq_forecast_model_entity",
        ),
    )

    @declared_attr
    def __tablename__(cls) -> str:
        return "forecast_model_entities"
