from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, func
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.orm import relationship

from backend.app.db.base_class import Base


class Categories(Base):
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String, nullable=True)
    entity_id = Column(Integer, ForeignKey("entities.id"), nullable=True)
    created_at = Column(DateTime, nullable=True, server_default=func.now())
    updated_at = Column(
        DateTime, nullable=True, server_default=func.now(), onupdate=func.now()
    )

    entities = relationship("Entities", back_populates="categories")
    metrics = relationship(
        "Metrics", back_populates="categories", cascade="all, delete-orphan"
    )
    forecasts = relationship(
        "Forecasts", back_populates="categories", cascade="all, delete-orphan"
    )
    models = relationship(
        "Models", back_populates="categories", cascade="all, delete-orphan"
    )

    @declared_attr
    def __tablename__(cls) -> str:
        return "categories"
