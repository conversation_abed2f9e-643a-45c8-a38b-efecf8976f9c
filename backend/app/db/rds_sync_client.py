import logging
from typing import List, Optional, Dict, Any
import sqlalchemy
from sqlalchemy.orm import sessionmaker, scoped_session
from sqlalchemy.engine import Result
from backend.app.core.config import settings
from contextlib import contextmanager

logger = logging.getLogger(__name__)


class RDSSyncClient:
    def __init__(self):
        self.engine = sqlalchemy.create_engine(
            settings.DATABASE_URL, echo=False, logging_name="rds_engine"
        )
        self.sessionmaker = sessionmaker(
            autocommit=False, autoflush=False, bind=self.engine
        )
        self.scoped_session = scoped_session(self.sessionmaker)
        logger.info("Initialized Sync RDS client")

    def execute_query(
        self, query: str, params: Optional[Dict[str, Any]] = None
    ) -> Result:
        try:
            logger.debug(f"Executing query:\n{query} with params: {params}")
            with self.engine.connect() as conn:
                result = conn.execute(sqlalchemy.text(query), params)
            logger.debug(f"Result: {result}")
            return result
        except Exception as e:
            logger.error(f"Error executing query: {str(e)}")
            raise e

    def get_session(self):
        return self.scoped_session()

    def remove_session(self):
        self.scoped_session.remove()

    @contextmanager
    def session_scope(self):
        session = self.get_session()
        try:
            yield session
            session.commit()
        except Exception as e:
            logger.error(f"Error in session: {str(e)}")
            session.rollback()
            raise e
        finally:
            self.remove_session()
