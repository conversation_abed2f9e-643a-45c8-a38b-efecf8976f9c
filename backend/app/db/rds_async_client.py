import logging
import asyncio
from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy import text
from sqlalchemy.engine import Result
from backend.app.core.config import settings
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)


class RDSAsyncClient:
    def __init__(self):
        self.engine = create_async_engine(
            settings.DATABASE_URL, echo=False, logging_name="rds_async_engine"
        )
        self.async_sessionmaker = sessionmaker(
            autocommit=False, autoflush=False, bind=self.engine, class_=AsyncSession
        )
        logger.info("Initialized Async RDS client")

    async def execute_query(
        self, query: str, params: Optional[Dict[str, Any]] = None
    ) -> Result:
        try:
            logger.debug(f"Executing query:\n{query} with params: {params}")
            async with self.engine.connect() as conn:
                result = await asyncio.wait_for(
                    conn.execute(text(query), params), timeout=settings.QUERY_TIMEOUT
                )
                logger.debug(f"Result: {result}")
                return result
        except Exception as e:
            logger.error(f"Error executing query: {str(e)}")
            raise e

    def get_session(self) -> AsyncSession:
        return self.async_sessionmaker()

    @asynccontextmanager
    async def session_scope(self):
        session = self.get_session()
        try:
            yield session
            await session.commit()
        except Exception as e:
            logger.error(f"Error in session: {str(e)}")
            await session.rollback()
            raise e
        finally:
            await session.close()

    async def cleanup(self):
        await self.engine.dispose()
        logger.info("Disposed Async RDS engine")
