import logging
from typing import List, Optional, Dict, Any
import time
import asyncio
import signal
from fastapi import HTTPException
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy import text
from sqlalchemy.engine import Result
from backend.app.core.config import settings

logger = logging.getLogger(__name__)


class StarRocksAsyncClient:
    def __init__(self, max_concurrent_queries: int = 10):
        self.semaphore = asyncio.Semaphore(max_concurrent_queries)
        logger.debug("Initialized Async StarRocks client")
        self.engine = create_async_engine(
            settings.STARROCKS_DB_URL, echo=False, logging_name="starrocks_engine"
        )
        self.session = sessionmaker(self.engine, class_=AsyncSession)

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.cleanup()

    async def cleanup(self):
        await self.engine.dispose()
        logger.debug("Disposed Async StarRocks engine")
        await self.session.close_all()
        logger.debug("Closed Async StarRocks session")
        logger.debug("Closed Async StarRocks client")

    def handle_signals(self):
        loop = asyncio.get_running_loop()
        for sig in (signal.SIGTERM, signal.SIGINT):
            loop.add_signal_handler(sig, self.cleanup)

    async def execute_query(
        self, query: str, params: Optional[Dict[str, Any]] = None
    ) -> Result:
        # TODO: consider creating a connection pool and handling connection errors and retries
        logger.debug(f"Executing query: {query} with params: {params}")
        if params is None:
            params = {}
        async with self.semaphore:
            try:
                async with self.session() as session:
                    start_time = time.time()
                    result = await asyncio.wait_for(
                        session.execute(text(query), params),
                        timeout=settings.QUERY_TIMEOUT,
                    )
                    total_time = int((time.time() - start_time) * 1000)
                    logger.debug(f"Query executed in {total_time} ms")
                    return result

            except Exception as e:
                logger.error(f"Error executing query '{query}': {str(e)}")
                raise e
