from datetime import date, datetime
from typing import Type, Dict, Optional, List, Union
from .metrics import Metric
from pydantic import BaseModel, create_model


class MetricSchema(BaseModel):
    base_schema: Optional[BaseModel] = None
    hourly_schema: Optional[BaseModel] = None
    aggregated_schema: Optional[BaseModel] = None

    # TODO: use id instead of name
    def __init__(self, metric_model: Optional[Dict[str, Dict]], name: str = "Metric"):
        super().__init__()
        self.base_schema = SchemaGenerator.create_base_schema_from_metrics(
            metric_model, f"Base{name}"
        )
        self.hourly_schema = SchemaGenerator.create_hourly_schema_from_base_schema(
            self.base_schema, f"Hourly{name}"
        )
        self.aggregated_schema = (
            SchemaGenerator.create_aggregated_schema_from_base_schema(
                self.base_schema, f"Aggregated{name}"
            )
        )


class ForecastSchema(BaseModel):
    # TODO: since we are storing all the info here, this is no longer just a schema
    metric_model: Dict[str, Dict]
    metric_schema: Optional[BaseModel] = None

    def __init__(self, metric_model: Dict[str, Dict]):
        super().__init__(
            metric_model=metric_model,
        )
        # Adding model_id to the response schema for forecasts
        metric_model["model_id"] = {
            "metric_name": "model_id",
            "display_name": "Model ID",
            "metric_type": "str",
            "editable": False,
        }
        self.metric_schema = SchemaGenerator.create_base_schema_from_metrics(
            metric_model, "Forecast"
        )


class SchemaGenerator:
    @staticmethod
    def create_base_schema_from_metrics(
        metrics: Dict[str, Dict], schema_name: str
    ) -> Type[BaseModel]:
        fields = {
            metric["metric_name"]: (
                Optional[Union[Metric, float, int, str, date]],
                None,
            )
            for metric in metrics.values()
            if metric.get("dependency_metric", False) is False
        }

        return create_model(
            schema_name,
            **fields,
            __config__=type("Config", (), {"from_attributes": True}),
        )

    @staticmethod
    def create_base_schema_from_table_cols(
        table_cols: Dict[str, Dict], schema_name: str
    ) -> Type[BaseModel]:
        type_mapping = {
            "str": Optional[str],
            "int": Optional[int],
            "float": Optional[float],
            "date": Optional[datetime],
        }

        fields = {
            col["metric_name"]: (type_mapping[col["metric_type"]], None)
            for col in table_cols.values()
        }

        return create_model(
            schema_name,
            **fields,
            __config__=type("Config", (), {"from_attributes": True}),
        )

    @staticmethod
    def create_hourly_schema_from_base_schema(
        base_schema: Type[BaseModel], schema_name: str
    ) -> Type[BaseModel]:
        return create_model(
            schema_name,
            hour=(int, None),
            date=(str, None),
            metrics=(base_schema, None),
            __config__=type("Config", (), {"from_attributes": True}),
        )

    @staticmethod
    def create_aggregated_schema_from_base_schema(
        base_schema: Type[BaseModel], schema_name: str
    ) -> Type[BaseModel]:
        return create_model(
            schema_name,
            time_range=(dict, None),
            metrics=(base_schema, None),
            __config__=type("Config", (), {"from_attributes": True}),
        )