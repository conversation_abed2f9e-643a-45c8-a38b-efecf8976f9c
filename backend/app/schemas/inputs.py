from typing import Optional, Union, List
from enum import Enum
from pydantic import BaseModel
from backend.app.schemas.utils import TimeGranularity
from backend.app.schemas.forecasts import SingleAggregation


class InputStatus(Enum):
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"


class UpdateInputStatus(BaseModel):
    input_id: int
    status: InputStatus


# TODO: Rename, they no longer hold just input values
class InputValue(BaseModel):
    metric_name: str
    value: Optional[Union[float, int]] = None


# TODO: Rename, they no longer hold just input values
class ForecastInput(BaseModel):
    is_input: bool = True
    input_id: Optional[int] = None
    model_id: Optional[str] = None
    entity_id: int
    start_time: str
    time_grain: TimeGranularity
    status: Optional[InputStatus] = None
    updated_at: Optional[str] = None
    values: List[InputValue]

    model_config = {"from_attributes": True}


class CreateForecastInputRequest(BaseModel):
    forecast_id: int
    inputs: List[ForecastInput]


class ForecastInputResponse(BaseModel):
    status: str
    message: Optional[str] = None
    data: List[ForecastInput]
