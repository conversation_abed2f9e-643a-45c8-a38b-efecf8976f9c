from pydantic import BaseModel
from enum import Enum
from typing import Optional, Dict


class TimeRange(BaseModel):
    start_time: str
    end_time: str

    model_config = {"from_attributes": True}


class AggregationGranularity(Enum):
    ENTITY = "entity"
    CITY = "city"
    PAN_INDIA = "pan_india"


class TimeGranularity(Enum):
    HOURLY = "hourly"
    DAILY = "daily"
    WEEKLY = "weekly"

    @property
    def next_grain(self) -> Optional["TimeGranularity"]:
        # TODO: hardcoding this since previous logic was not working
        if self == TimeGranularity.HOURLY:
            return TimeGranularity.DAILY
        elif self == TimeGranularity.DAILY:
            return TimeGranularity.WEEKLY
        return None


class ComparisonOptions(Enum):
    Do1D = "do1d"
    Wo1W = "wo1w"
    Wo2W = "wo2w"
    Mo1M = "mo1m"
    Mo2M = "mo2m"
    Yo1Y = "yo1y"


class ComparisonPeriod:

    name: str
    start_time: str
    end_time: str

    def __init__(self, name: str, start_time: str, end_time: str):
        self.name = name
        self.start_time = start_time
        self.end_time = end_time

    def __repr__(self):
        return f"ComparisonPeriod(name={self.name}, start_time={self.start_time}, end_time={self.end_time})"
