from backend.app.schemas.metrics import MetricType
from backend.app.schemas.definitions.base_metrics import BaseMetricsModel


class StoreMetricsModel(BaseMetricsModel):
    # TODO: Make schema and crud generation dynamic using the following definitions
    health_metrics = {
        "picker_surge_pct": {
            "metric_name": "picker_surge_pct",
            "display_name": "Picker Surge %",
            "aggregate_query": "CAST(SUM(picker_surge_carts) AS DOUBLE) * 100.0 / NULLIF(SUM(carts), 0)",
            "metric_type": MetricType.PERCENTAGE,
            "thresholds": {
                "good": {"min": 0, "max": 2},
                "okay": {"min": 2, "max": 5},
                "critical": {"min": 5, "max": float("inf")},
            },
        },
        "picker_utilization_fixed": {
            "metric_name": "picker_utilization_fixed",
            "display_name": "Fixed Picker Utilization",
            "aggregate_query": "CAST(SUM(picker_busy_time_fixed_sec) AS DOUBLE) * 100 / NULLIF(SUM(picker_active_time_fixed_sec), 0)",
            "metric_type": MetricType.PERCENTAGE,
            "thresholds": {
                "good": {"min": 40, "max": 45},
                "okay": {"min": 0, "max": 40},
                "critical": {"min": 45, "max": float("inf")},
            },
        },
        "r2a_within_10_sec": {
            "metric_name": "r2a_within_10_sec",
            "display_name": r"R2A % under 10 sec",
            "aggregate_query": "CAST(SUM(r2a_within_10_orders) AS DOUBLE) * 100 / NULLIF(SUM(orders), 0)",
            "metric_type": MetricType.PERCENTAGE,
            "thresholds": {
                "good": {"min": 85, "max": float("inf")},
                "okay": {"min": 75, "max": 85},
                "critical": {"min": 0, "max": 75},
            },
        },
    }
