from backend.app.core.config import Settings


class EventModel:
    settings = Settings()
    table_name = "blinkit_iceberg_staging.storeops_etls.planner_time_filters"

    table_cols = {
        "event_dt": {
            "metric_name": "event_dt",
            "select_query": "date",
            "metric_type": "str",
        },
        "event_id": {
            "metric_name": "event_id",
            "select_query": "event_id",
            "metric_type": "str",
        },
        "event_name": {
            "metric_name": "event_name",
            "select_query": "event_name",
            "metric_type": "str",
        },
        "event_dt_last_year": {
            "metric_name": "event_dt_last_year",
            "select_query": "event_dt_last_year",
            "metric_type": "str",
        },
    }
