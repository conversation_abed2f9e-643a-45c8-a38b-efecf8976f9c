from backend.app.core.config import Settings


class StoreModel:
    settings = Settings()
    table_name = "blinkit_iceberg_staging.storeops_etls.planner_entity_filters"

    table_cols = {
        "entity_id": {
            "metric_name": "entity_id",
            "select_query": "id",
            "metric_type": "int",
        },
        "entity_name": {
            "metric_name": "entity_name",
            "select_query": "name",
            "metric_type": "str",
        },
        "city_id": {
            "metric_name": "city_id",
            "select_query": "city_id",
            "metric_type": "int",
        },
        "city_name": {
            "metric_name": "city_name",
            "select_query": "city_name",
            "metric_type": "str",
        },
    }
