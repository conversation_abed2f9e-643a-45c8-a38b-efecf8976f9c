from typing import Optional
from enum import Enum
from pydantic import BaseModel
from .metrics import Metric


class StoreHealthStatus(Enum):
    GOOD = "good"
    OKAY = "okay"
    CRITICAL = "critical"


class StoreHealth(BaseModel):
    store_health: Optional[StoreHealthStatus] = None
    picker_surge_pct: Optional[Metric] = None
    picker_utilization_fixed: Optional[Metric] = None
    r2a_within_10_sec: Optional[Metric] = None

    model_config = {"from_attributes": True}
