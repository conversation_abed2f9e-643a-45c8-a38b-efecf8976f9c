from pydantic import BaseModel
from typing import Optional, Union, Type, List
from enum import Enum


class MetricType(Enum):
    COUNT = "count"
    AVERAGE = "average"
    PERCENTAGE = "percentage"
    SECONDS = "seconds"
    MINUTES = "minutes"
    HOURS = "hours"
    RUPEES = "rupees"
    DAYS = "days"

    @classmethod
    def infer_python_type(cls, metric_type: "MetricType") -> Type:
        mapping = {
            cls.COUNT: int,
            cls.AVERAGE: float,
            cls.PERCENTAGE: float,
            cls.SECONDS: int,
            cls.MINUTES: int,
            cls.HOURS: int,
            cls.RUPEES: int,
            cls.DAYS: int,
        }
        return mapping.get(metric_type, str)


class MetricDefinition(BaseModel):
    metric_name: str
    display_name: str
    metric_type: MetricType
    select_query: Optional[str] = None
    aggregate_query: Optional[str] = None
    # TODO: grain is string, making any validation checks for allowed values harder
    grain: Optional[str] = None
    dependency_metrics: List[int] = []

    model_config = {"from_attributes": True}


class Metric(MetricDefinition):
    metric_value: Optional[Union[float, int, str]]

    model_config = {"from_attributes": True}
