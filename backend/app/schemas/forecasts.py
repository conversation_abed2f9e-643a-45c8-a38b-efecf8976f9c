from typing import Dict, List, Union, Optional
from pydantic import BaseModel
from backend.app.schemas.utils import AggregationGranularity, TimeGranularity


class SingleAggregation(BaseModel):
    value: Union[str, int, None]
    grain: AggregationGranularity = AggregationGranularity.PAN_INDIA


class MultiAggregation(BaseModel):
    values: List[Union[str, int, None]]
    grain: AggregationGranularity = AggregationGranularity.PAN_INDIA


class Forecast(BaseModel):
    tenant: Optional[str] = None
    entity: Optional[str] = None
    category: Optional[int] = None
    id: Optional[int] = None
    display_name: str
    default_model: str
    sla: str
    approvers: List[int]
    metrics: Optional[List[int]] = None


class ForecastDataRequest(BaseModel):
    tenant: str
    entity: str
    category_id: Optional[int]
    forecast_ids: List[int]
    multi_aggregation: MultiAggregation
    start_time: str
    time_grain: TimeGranularity


class DetailedForecastDataRequest(BaseModel):
    tenant: str
    entity: str
    category_id: int
    forecast_ids: List[int]
    single_aggregation: SingleAggregation
    start_time: str
    time_grain: TimeGranularity

class ForecastRow(BaseModel):
    entity: Optional[Union[int, str]] = None
    time: Optional[str] = None
    model_id: Optional[str] = None
    values: Dict = {}

class ForecastData(BaseModel):
    idx_cols: Dict
    cols: Dict
    values: List[ForecastRow] = []
