import logging

import sys
import os
from backend.app.core.config import settings


def setup_logging() -> None:
    """
    Configure logging settings for the application
    Args:
        log_level: Minimum logging level to display
    """
    app_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    log_dir = os.path.join(app_root, "logs")
    os.makedirs(log_dir, exist_ok=True)
    # log_file = os.path.join(log_dir, "app.log")  # Uncomment if file logging is needed

    log_format = "%(asctime)s - %(name)s - [%(levelname)s] - %(message)s - %(pathname)s:%(lineno)d"
    date_format = "%Y-%m-%d %H:%M:%S"
    base_level = logging.DEBUG if settings.DEBUG_MODE else logging.INFO

    # Configure root logger
    logging.basicConfig(
        level=base_level,
        format=log_format,
        datefmt=date_format,
        handlers=[
            logging.StreamHandler(sys.stdout),
            # logging.FileHandler(log_file, encoding='utf-8')
        ],
    )

    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    logging.getLogger("pinotdb.sqlalchemy").setLevel(logging.WARNING)
    logging.getLogger("asyncio").setLevel(logging.WARNING)
    logging.getLogger("fastapi").setLevel(logging.INFO)

    logger = logging.getLogger(__name__)
    logger.info(f"Logging system initialized, debug mode: {settings.DEBUG_MODE}")
