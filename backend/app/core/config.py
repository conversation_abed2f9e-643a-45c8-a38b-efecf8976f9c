from pydantic_settings import BaseSettings

# from fastapi_jwt_auth import AuthJWT
from typing import Optional


class Settings(BaseSettings):
    PROJECT_NAME: str = "Store Planning Platform"
    DATABASE_URL: str = ""
    ANALYTICS_DB_URL: Optional[str] = None
    PINOT_DB_URL: str = ""
    STARROCKS_DB_URL: str = ""
    DEBUG_MODE: bool = False
    POTGRES_ASYNC_URL: str = ""
    ICEBERG_CATALOG: str = ""
    QUERY_TIMEOUT: int = 150

    # auth
    GOOGLE_CLIENT_ID: str = ""
    GOOGLE_CLIENT_SECRET: str = ""
    GOOGLE_REDIRECT_URI: str = ""
    JWT_SECRET_KEY: str = "capacity-planner-secret-key"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    USER_REGISTRAION_ALLOWED: bool = False

    model_config = {"env_file": ".env", "extra": "allow"}


# @AuthJWT.load_config
# def get_config():
#     return Settings()

settings = Settings()
