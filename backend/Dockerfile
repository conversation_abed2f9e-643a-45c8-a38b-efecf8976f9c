FROM public.ecr.aws/zomato/python:3.11

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install poetry (pinned version for reproducibility - matches lock file)
RUN pip install poetry==2.0.1

# Configure poetry: don't create virtual env (we're in container)
RUN poetry config virtualenvs.create false

# Set working directory
WORKDIR /app
ENV PYTHONPATH="${PYTHONPATH}:/app"

# Copy dependency files
COPY pyproject.toml poetry.lock README.md ./

# Install dependencies (only main group, excluding dev dependencies)
RUN poetry install --no-root --only=main

# Copy application code
COPY . .

# Expose port
EXPOSE 8080

# Run the application
CMD ["poetry", "run", "uvicorn", "backend.app.main:app", "--host", "0.0.0.0", "--port", "8080", "--reload"]

