# Capacity Planner

## Architecture
![High Level Design](./docs/HLD.png)

## Local Development Setup

### Prerequisites
- Python 3.11
- pip (Python package manager)
- Docker (optional, for containerized setup)
- Node version 22

### Setup and Run (Local)

1. Clone the repository
```bash
git clone <repository-url>
cd capacity-planner
```


2. Create and activate virtual environment
```bash
python3.11 -m venv venv
source venv/bin/activate
```

3. Install dependencies
```bash
pip3 install -r requirements.txt
```

4. Spin up Postgres DB (for local testing)

```
docker-compose up postgres_db
cd backend
alembic revision --autogenerate -m "Initialise DB"
alembic upgrade head
```

5. Environment variables

    Add the following env variable to the `.env` file:
    - `PINOT_DB_URL`
    - `STARROCKS_DB_URL`
    - `DATABASE_URL` (for Postgres/AWS RDS)
    - set `DEBUG` to `True` to see detailed logs

6. Run the application
```bash
python3 -m uvicorn backend.app.main:app --host 0.0.0.0 --port 8000 --reload
```
The API will be available at http://localhost:8000

7. Running frontend
```bash
cd frontend
npm install
npm run dev
```
The frontend will be available at http://localhost:3000
