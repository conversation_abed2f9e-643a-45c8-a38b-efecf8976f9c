name: Prevent committers from approving a PR

on:
  pull_request_review:
    types: [submitted]

jobs:
  preventapprove:
    name: reject PR approval by committer
    runs-on: blinkit-ci-action-runner-default
    if: github.event.review.state == 'approved'
    steps:
      - uses: actions/create-github-app-token@v1
        id: app-token
        with:
          app-id: ${{ vars.CIGROFERS_CORE_APP_APPID }}
          private-key: ${{ secrets.CIGROFERS_CORE_APP_PVT_KEY }}
          owner: ${{ github.repository_owner }}

      - name: Get central action repository
        uses: actions/checkout@v3
        with:
          repository: grofers/github-actions
          ref: v2.4.5
          token:  ${{ steps.app-token.outputs.token }}

      - name: Get reject-pr-approval-from-committer action repository
        uses: actions/checkout@v3
        with:
          repository: grofers/reject-pr-approval-from-committer
          ref: v1.0.0
          token:  ${{ steps.app-token.outputs.token }}
          path: reject-pr-approval-from-committer

      - name: Dismiss code reviews from collaborators
        uses: ./reject-pr-approval-from-committer
        with:
          github-token:  ${{ steps.app-token.outputs.token }}
