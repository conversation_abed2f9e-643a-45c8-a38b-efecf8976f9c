name: planner-platform-frontend preprod CI


on:
  push:
    tags:
      - 'v*.*.*-preprod*'

env:
  REPO_NAME: planner-platform-frontend

jobs:
  setup:
    runs-on: blinkit-ci-action-runner-default
    timeout-minutes: 30
    outputs:
      SHA: ${{ steps.setup.outputs.SHA }}
      TAG: ${{ steps.setup.outputs.TAG }}
    steps:
      - name: Check out main repository
        uses: actions/checkout@v3

      - name: setup
        id: setup
        run: |
          sha=commit-$(git rev-parse --short HEAD)
          echo "SHA=${sha}" >> $GITHUB_OUTPUT

          tag=preprod-$(git tag --points-at HEAD)
          echo "TAG=${tag}" >> $GITHUB_OUTPUT
        shell: bash

  retag-image-preprod:
    needs: [ setup ]
    runs-on: blinkit-ci-action-runner-default
    timeout-minutes: 30
    steps:
      - uses: actions/create-github-app-token@v1
        id: app-token
        with:
          app-id: ${{ vars.CI_RELEASE_PROD_APP_ID }}
          private-key: ${{ secrets.CI_RELEASE_PROD_APP_PVT_KEY }}
          owner: ${{ github.repository_owner }}

      - name: Get retag action repository
        uses: actions/checkout@v3
        with:
          repository: grofers/github-actions
          ref: v2.4.11
          token: ${{ steps.app-token.outputs.token }}

      - name: Call retag action
        uses: ./retag-ecr-image
        with:
          ECR_REPO_NAME: '${{ env.REPO_NAME }}'
          EXISTING_TAG: ${{ needs.setup.outputs.SHA }}
          NEW_TAG: ${{ needs.setup.outputs.TAG }}

  argo-preprod-pr:
    needs: [retag-image-preprod, setup]
    runs-on: blinkit-ci-action-runner-default
    timeout-minutes: 30

    steps:
      - uses: actions/create-github-app-token@v1
        id: app-token
        with:
          app-id: ${{ vars.CI_RELEASE_PROD_APP_ID }}
          private-key: ${{ secrets.CI_RELEASE_PROD_APP_PVT_KEY }}
          owner: ${{ github.repository_owner }}

      - name: Get release action repository
        uses: actions/checkout@v3
        with:
          repository: grofers/github-actions
          ref: v2.4.11
          token: ${{ steps.app-token.outputs.token }}

      - name: Create PR for release version update in argo track repo
        uses: ./argo-release-version-update
        with:
          NEW_VERSION: ${{ needs.setup.outputs.TAG }}
          ARGO_TRACK_TOKEN: ${{ steps.app-token.outputs.token }}
          IS_PREPROD: true
          ARGO_TRACK_DIR: 'services/data/capacity-planner'
          ECR_NAME: '${{ env.REPO_NAME }}'
