name: Financial Label should be present

on:
  pull_request:
    branches:
      - master
    types:
      - labeled
      - unlabeled
      - opened
      - edited
      - reopened
      - synchronize

jobs:
  verify:
    runs-on: blinkit-ci-action-runner-default
    timeout-minutes: 30
    steps:
      - uses: actions/create-github-app-token@v1
        id: app-token
        with:
          app-id: ${{ vars.CIGROFERS_CORE_APP_APPID }}
          private-key: ${{ secrets.CIGROFERS_CORE_APP_PVT_KEY }}
          owner: ${{ github.repository_owner }}

      - name: Get label check action repository
        uses: actions/checkout@v3
        with:
          repository: grofers/github-actions
          ref: v2.4.5
          token: ${{ steps.app-token.outputs.token }}
          
      - name: validate label
        id: validate
        uses: ./financial-label
        with:
          FINANCIAL_LABEL: ${{ vars.FINANCE_LABEL }}
          NON_FINANCIAL_LABEL: ${{ vars.NON_FINANCE_LABEL }}

      - name: Get PR comment action repository
        if: always()
        uses: actions/checkout@v3
        with:
          repository: grofers/sticky-pull-request-comment
          ref: v1.0.0
          token: ${{ steps.app-token.outputs.token }}
          path: ./sticky-pull-request-comment

      - name: setup 
        run: |
          PR_URL="${{ github.event.issue.pull_request.url }}"
          PR_NUMBER=$(echo "$PR_URL" | awk -F'/' '{print $NF}')
          echo "PR_NUMBER=${PR_NUMBER}" >> $GITHUB_OUTPUT

      - name: Post Comment on PR
        if: always()
        uses: ./sticky-pull-request-comment
        with:
          number: ${{ steps.setup.outputs.PR_NUMBER }}
          header: label
          message: ${{ steps.validate.outputs.result }}
