name: planner-platform-backend build

on:
  push:
    branches:
      - 'main'
      - 'development'
      - 'master'
      - 'preprod*'

env:
  REPO_NAME: planner-platform-backend

jobs:
  docker-build-prod:
    runs-on: blinkit-ci-action-runner-default
    timeout-minutes: 30
    steps:
      - name: Check out main repository
        uses: actions/checkout@v3

      - uses: actions/create-github-app-token@v1
        id: app-token
        with:
          app-id: ${{ vars.CI_BUILDER_APP_ID }}
          private-key: ${{ secrets.CI_BUILDER_APP_PVT_KEY }}
          owner: ${{ github.repository_owner }}

      - name: Get build action repository
        uses: actions/checkout@v3
        with:
          repository: grofers/builder-github-action
          ref: v2.2.1
          token: ${{ steps.app-token.outputs.token }}
          path: .github/builder-action


      - name: Run the private action
        uses: ./.github/builder-action
        with:
          ECR_REPO_NAME: '${{ env.REPO_NAME }}'
          SLACK_CHANNEL_ID: '${{ secrets.SLACK_CHANNEL_ID }}'
          IS_PR: 'false'
          GH_ACCESS_TOKEN: ${{ steps.app-token.outputs.token }}
          DOCKER_BUILD_FILE_NAME: 'backend/Dockerfile'
