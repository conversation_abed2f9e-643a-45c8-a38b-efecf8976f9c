name: sonarqube

on:
  push:
    branches:
      - master
      - main
      - preprod
  pull_request:
    branches:
      - master
      - main
      - preprod

env:
  SONAR_TOKEN: ${{ secrets.SONARQUBE_TOKEN }}
  SONAR_HOST_URL: ${{ vars.SONARQUBE_HOST_URL }}
  SONAR_LINK_UUID: branch

jobs:
  scan:
    name: sonar scan
    runs-on: blinkit-ci-action-runner-default
    timeout-minutes: 30

    steps:
      - uses: actions/checkout@v2
        with:
          fetch-depth: 1

      - name: pull request property
        if: ${{ github.event.pull_request }}
        run: echo "SONAR_LINK_UUID=pullRequest" >> $GITHUB_ENV

      

      - name: Run analysis of ${{ github.REF }} branch
        if: ${{ endsWith(github.REF, '/master') != true && github.GITHUB_EVENT_NAME == 'push' }}
        uses: sonarsource/sonarqube-scan-action@master
        with:
          args: >
            -Dsonar.branch.name=${{ github.HEAD_REF }}

      - name: Run analysis of master branch
        if: ${{ endsWith(github.REF, '/master') == true }}
        uses: sonarsource/sonarqube-scan-action@master
        with:
          args: >
            -Dsonar.branch.name=master

      - name: Run analysis of pull request
        if: ${{ github.event.pull_request }}
        uses: sonarsource/sonarqube-scan-action@master
        with:
          args: >
            -Dsonar.pullrequest.key=${{ github.event.pull_request.head.ref }}
            -Dsonar.pullrequest.branch=${{ github.event.pull_request.head.ref }}
            -Dsonar.pullrequest.base=${{ github.event.pull_request.base.ref }}
            -Dsonar.pullrequest.github.repository=${{ github.repository }}
            -Dsonar.scm.revision=${{ github.event.pull_request.head.sha }}

      # Check the Quality Gate status.
      - name: SonarQube Quality Gate check
        id: sonarqube-quality-gate-check
        uses: sonarsource/sonarqube-quality-gate-action@master
        # Force to fail step after specific time.
        timeout-minutes: 25

      - uses: actions/create-github-app-token@v1
        id: app-token
        with:
          app-id: ${{ vars.CIGROFERS_CORE_APP_APPID }}
          private-key: ${{ secrets.CIGROFERS_CORE_APP_PVT_KEY }}
          owner: ${{ github.repository_owner }}

      - name: Get PR comment action repository
        if: ${{ github.event.pull_request && always() }}
        uses: actions/checkout@v3
        with:
          repository: grofers/sticky-pull-request-comment
          ref: v1.0.0
          token: ${{ steps.app-token.outputs.token }}
          path: ./sticky-pull-request-comment

      - name: Post Comment on PR
        if: ${{ github.event.pull_request && always() }}
        uses: ./sticky-pull-request-comment
        with:
          header: sonar
          message: "Quality Gate status is ${{ steps.sonarqube-quality-gate-check.outputs.quality-gate-status }}.\nQuality Report link for the PR can be find here:  [SonarQube Link](https://sonarqube.grofer.io/dashboard?${{ env.SONAR_LINK_UUID }}=${{ github.event.pull_request.head.ref }}&id=)."
