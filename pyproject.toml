[tool.poetry]
name = "capacity-planner"
version = "0.1.0"
description = "Capacity Planning Tool"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
packages = [
    {include = "backend"}
]

[tool.poetry.dependencies]
python = ">=3.11,<3.12"
fastapi = "0.104.1"
uvicorn = "0.17.4"
sqlalchemy = "1.4.31"
alembic = "1.12.1"
pydantic = "2.5.1"
pydantic-settings = "2.1.0"
python-dotenv = "1.0.0"
asyncpg = "0.30.0"
psycopg2-binary = "2.9.9"
python-jose = {extras = ["cryptography"], version = "3.3.0"}
python-multipart = "0.0.5"
aiohttp = "3.8.6"
httpx = "0.22.0"
tenacity = "8.0.1"
aiomysql = "0.1.0"
annotated-types = "0.7.0"
anyio = "3.7.1"
certifi = "2025.1.31"
charset-normalizer = "3.4.1"
ciso8601 = "2.3.2"
click = "8.1.8"
ecdsa = "0.19.1"
exceptiongroup = "1.2.2"
fastapi-jwt-auth = "0.5.0"
greenlet = "2.0.2"
h11 = "0.12.0"
httpcore = "0.14.7"
idna = "3.10"
Mako = "1.3.9"
MarkupSafe = "3.0.2"
passlib = "1.7.4"
pinotdb = ">=0.3.2,<0.3.9"
pyasn1 = "0.6.1"
PyJWT = "1.7.1"
requests = "2.32.3"
rsa = "4.9"
six = "1.17.0"
sniffio = "1.3.1"
starlette = "0.27.0"
typing-extensions = "4.13.0"
urllib3 = "2.3.0"
pydantic_core = "2.14.3"
poetry-dotenv-plugin = "^0.2.0"
requests-oauthlib = "^2.0.0"

[tool.poetry.group.dev.dependencies]
pytest = "7.4.0"
black = "23.7.0"
isort = "5.12.0"
mypy = "1.5.1"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
