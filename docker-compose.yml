services:
  backend:
    build:
      context: ./backend
    ports:
      - "8080:8080"
    volumes:
      - ./backend:/app
    environment:
      - DATABASE_URL=***********************************************/planning_db
      - CORS_ORIGINS=["http://localhost:3000"]
      - DEBUG_MODE=True
    depends_on:
      - postgres_db
    env_file:
      - .env

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - REACT_APP_API_URL=http://localhost:8080/api/v1

  postgres_db:
    image: postgres:17
    platform: linux/amd64
    volumes:
      - postgres_data:/var/lib/postgresql/data
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=planning_db
    ports:
      - "5432:5432"

volumes:
  postgres_data:
